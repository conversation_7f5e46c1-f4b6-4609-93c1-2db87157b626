{"name": "chatbot-ai-be", "version": "1.0.0", "main": "index.js", "scripts": {"test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "dev": "nodemon --exec tsx src/server.ts", "dev:worker": "nodemon --exec tsx src/worker.ts", "build": "tsc && node copy-env.js", "start": "node dist/server.js", "start:worker": "node dist/worker.js", "lint": "tsc --noEmit", "clean": "<PERSON><PERSON><PERSON> dist", "dev:mastra": "<PERSON>ra dev"}, "keywords": [], "author": "", "license": "ISC", "description": "", "dependencies": {"@ai-sdk/google": "^1.2.19", "@ai-sdk/openai": "^1.3.20", "@bull-board/api": "^6.10.1", "@bull-board/express": "^6.10.1", "@bull-board/ui": "^6.10.1", "@mastra/client-js": "^0.10.5", "@mastra/core": "^0.10.8", "@mastra/loggers": "^0.10.2", "@mastra/mcp": "^0.10.4", "@mastra/memory": "^0.11.0", "@mastra/pg": "^0.12.0", "@openrouter/ai-sdk-provider": "^0.4.5", "@opentelemetry/api": "^1.8.0", "@supabase/supabase-js": "^2.50.0", "@trigger.dev/sdk": "^3.3.17", "ai": "^4.3.16", "axios": "^1.9.0", "base64-arraybuffer": "^1.0.2", "body-parser": "^1.20.2", "bullmq": "^5.55.0", "cors": "^2.8.5", "dotenv": "^16.5.0", "express": "^4.18.3", "ioredis": "^5.6.1", "langfuse-vercel": "^3.37.2", "multer": "^2.0.0", "postgres": "^3.4.5", "redis-server": "^1.2.2", "weaviate-client": "^3.5.2", "zod": "^3.24.3"}, "devDependencies": {"@trigger.dev/build": "^3.3.17", "@types/body-parser": "^1.19.5", "@types/cors": "^2.8.17", "@types/express": "^4.17.21", "@types/jest": "^29.5.14", "@types/node": "^22.15.3", "@types/supertest": "^6.0.3", "concurrently": "^8.2.2", "jest": "^29.7.0", "mastra": "^0.10.6", "nodemon": "^3.1.10", "rimraf": "^5.0.5", "supertest": "^7.1.0", "ts-jest": "^29.3.2", "ts-node": "^10.9.2", "tsx": "^4.19.3", "typescript": "^5.8.3"}, "packageManager": "yarn@1.22.22+sha512.a6b2f7906b721bba3d67d4aff083df04dad64c399707841b7acf00f6b133b7ac24255f2652fa22ae3534329dc6180534e98d17432037ff6fd140556e2bb3137e"}