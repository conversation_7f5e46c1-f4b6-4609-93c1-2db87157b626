# This file is generated by running "yarn install" inside your project.
# Manual changes might be lost - proceed with caution!

__metadata:
  version: 8
  cacheKey: 10c0

"@ag-ui/client@npm:^0.0.27":
  version: 0.0.27
  resolution: "@ag-ui/client@npm:0.0.27"
  dependencies:
    "@ag-ui/core": "npm:0.0.27"
    "@ag-ui/encoder": "npm:0.0.27"
    "@ag-ui/proto": "npm:0.0.27"
    "@types/uuid": "npm:^10.0.0"
    fast-json-patch: "npm:^3.1.1"
    rxjs: "npm:7.8.1"
    untruncate-json: "npm:^0.0.1"
    uuid: "npm:^11.1.0"
    zod: "npm:^3.22.4"
  checksum: 10c0/25da4b127f15b63591f95ead0096c7817a1c55206268870d66901993947a2a34377fc99cfbc76306f312c6a583f7674bea745c9ccba68eb668f1708558a2c748
  languageName: node
  linkType: hard

"@ag-ui/core@npm:0.0.27":
  version: 0.0.27
  resolution: "@ag-ui/core@npm:0.0.27"
  dependencies:
    rxjs: "npm:7.8.1"
    zod: "npm:^3.22.4"
  checksum: 10c0/2d09052f1acdae4799a515de14ebaefdb96acc64d12d3269e8dc3e7c12f5bac94a9f08726e991bc42484e61759444eb461e0ff7d2fe24aac2ba6d72e51d55fbd
  languageName: node
  linkType: hard

"@ag-ui/encoder@npm:0.0.27":
  version: 0.0.27
  resolution: "@ag-ui/encoder@npm:0.0.27"
  dependencies:
    "@ag-ui/core": "npm:0.0.27"
    "@ag-ui/proto": "npm:0.0.27"
  checksum: 10c0/dc7f038d394e26dc858c1a1203cfa5c73e2c8ebeea3353f1164412aaa54089583444818baf9a2e9f69ffe48b51e7606c214d65025717c8fc25c57194936ebcf8
  languageName: node
  linkType: hard

"@ag-ui/proto@npm:0.0.27":
  version: 0.0.27
  resolution: "@ag-ui/proto@npm:0.0.27"
  dependencies:
    "@ag-ui/core": "npm:0.0.27"
    "@bufbuild/protobuf": "npm:^2.2.5"
  checksum: 10c0/649a1f5fbd309a763c8e176ea97a62981dfdc6e44ac22fc399473bd14fcbba8565feed03af29ad829783af5cfb667f3df4239c38b40897d5981a55d44986e135
  languageName: node
  linkType: hard

"@ai-sdk/google@npm:^1.2.19":
  version: 1.2.19
  resolution: "@ai-sdk/google@npm:1.2.19"
  dependencies:
    "@ai-sdk/provider": "npm:1.1.3"
    "@ai-sdk/provider-utils": "npm:2.2.8"
  peerDependencies:
    zod: ^3.0.0
  checksum: 10c0/b40d62ce822ce00850492e4a41c8b6b1ba2ddaaaa8f8d9b8381c198781adb23000fc4f434ef7edf5ba356a4455f8afbbdc5cbecbb0f66b7bcabbcd25758fc6b8
  languageName: node
  linkType: hard

"@ai-sdk/openai@npm:^1.3.20":
  version: 1.3.22
  resolution: "@ai-sdk/openai@npm:1.3.22"
  dependencies:
    "@ai-sdk/provider": "npm:1.1.3"
    "@ai-sdk/provider-utils": "npm:2.2.8"
  peerDependencies:
    zod: ^3.0.0
  checksum: 10c0/bcc73a84bebd15aa54568c3c77cedd5f999e282c5be180d5e28ebc789f8873dd0a74d87f1ec4a0f16e3e61b658c3b0734835daf176ed910966246db73c72b468
  languageName: node
  linkType: hard

"@ai-sdk/provider-utils@npm:2.1.10":
  version: 2.1.10
  resolution: "@ai-sdk/provider-utils@npm:2.1.10"
  dependencies:
    "@ai-sdk/provider": "npm:1.0.9"
    eventsource-parser: "npm:^3.0.0"
    nanoid: "npm:^3.3.8"
    secure-json-parse: "npm:^2.7.0"
  peerDependencies:
    zod: ^3.0.0
  peerDependenciesMeta:
    zod:
      optional: true
  checksum: 10c0/d33bbe18f05b3713870ee400378d356e3ccd4a648e2c1bcd492fd3517781b8f7dae91e2916265641098861c4a447e23c178ad22026e2c47e286f56ecfd50b156
  languageName: node
  linkType: hard

"@ai-sdk/provider-utils@npm:2.2.8, @ai-sdk/provider-utils@npm:^2.2.8":
  version: 2.2.8
  resolution: "@ai-sdk/provider-utils@npm:2.2.8"
  dependencies:
    "@ai-sdk/provider": "npm:1.1.3"
    nanoid: "npm:^3.3.8"
    secure-json-parse: "npm:^2.7.0"
  peerDependencies:
    zod: ^3.23.8
  checksum: 10c0/34c72bf5f23f2d3e7aef496da7099422ba3b3ff243c35511853e16c3f1528717500262eea32b19e3e09bc4452152a5f31e650512f53f08a5f5645d907bff429e
  languageName: node
  linkType: hard

"@ai-sdk/provider@npm:1.0.9":
  version: 1.0.9
  resolution: "@ai-sdk/provider@npm:1.0.9"
  dependencies:
    json-schema: "npm:^0.4.0"
  checksum: 10c0/49ecd7e69e949c0290159bab15ac0228ae51f2eeb5b7694b19bc98f1058891a570ef75bcc5748afdff5fa607f6da50d9d426500d4a1651f922ff18e74ba2a840
  languageName: node
  linkType: hard

"@ai-sdk/provider@npm:1.1.3, @ai-sdk/provider@npm:^1.1.3":
  version: 1.1.3
  resolution: "@ai-sdk/provider@npm:1.1.3"
  dependencies:
    json-schema: "npm:^0.4.0"
  checksum: 10c0/40e080e223328e7c89829865e9c48f4ce8442a6a59f7ed5dfbdb4f63e8d859a76641e2d31e91970dd389bddb910f32ec7c3dbb0ce583c119e5a1e614ea7b8bc4
  languageName: node
  linkType: hard

"@ai-sdk/react@npm:1.2.12":
  version: 1.2.12
  resolution: "@ai-sdk/react@npm:1.2.12"
  dependencies:
    "@ai-sdk/provider-utils": "npm:2.2.8"
    "@ai-sdk/ui-utils": "npm:1.2.11"
    swr: "npm:^2.2.5"
    throttleit: "npm:2.1.0"
  peerDependencies:
    react: ^18 || ^19 || ^19.0.0-rc
    zod: ^3.23.8
  peerDependenciesMeta:
    zod:
      optional: true
  checksum: 10c0/5422feb4ffeebd3287441cf658733e9ad7f9081fc279e85f57700d7fe9f4ed8a0504789c1be695790df44b28730e525cf12acf0f52bfa5adecc561ffd00cb2a5
  languageName: node
  linkType: hard

"@ai-sdk/ui-utils@npm:1.2.11, @ai-sdk/ui-utils@npm:^1.2.11":
  version: 1.2.11
  resolution: "@ai-sdk/ui-utils@npm:1.2.11"
  dependencies:
    "@ai-sdk/provider": "npm:1.1.3"
    "@ai-sdk/provider-utils": "npm:2.2.8"
    zod-to-json-schema: "npm:^3.24.1"
  peerDependencies:
    zod: ^3.23.8
  checksum: 10c0/de0a10f9e16010126a21a1690aaf56d545b9c0f8d8b2cc33ffd22c2bb2e914949acb9b3f86e0e39a0e4b0d4f24db12e2b094045e34b311de0c8f84bfab48cc92
  languageName: node
  linkType: hard

"@ampproject/remapping@npm:^2.2.0":
  version: 2.3.0
  resolution: "@ampproject/remapping@npm:2.3.0"
  dependencies:
    "@jridgewell/gen-mapping": "npm:^0.3.5"
    "@jridgewell/trace-mapping": "npm:^0.3.24"
  checksum: 10c0/81d63cca5443e0f0c72ae18b544cc28c7c0ec2cea46e7cb888bb0e0f411a1191d0d6b7af798d54e30777d8d1488b2ec07*******************************
  languageName: node
  linkType: hard

"@apidevtools/json-schema-ref-parser@npm:^11.1.0":
  version: 11.9.3
  resolution: "@apidevtools/json-schema-ref-parser@npm:11.9.3"
  dependencies:
    "@jsdevtools/ono": "npm:^7.1.3"
    "@types/json-schema": "npm:^7.0.15"
    js-yaml: "npm:^4.1.0"
  checksum: 10c0/5745813b3d964279f387677b7a903ba6634cdeaf879ff3a331a694392cbc923763f398506df190be114f2574b8b570baab3e367c2194bb35f50147ff6cf27d7a
  languageName: node
  linkType: hard

"@aws-crypto/crc32@npm:3.0.0":
  version: 3.0.0
  resolution: "@aws-crypto/crc32@npm:3.0.0"
  dependencies:
    "@aws-crypto/util": "npm:^3.0.0"
    "@aws-sdk/types": "npm:^3.222.0"
    tslib: "npm:^1.11.1"
  checksum: 10c0/09189ada61a4ffe6b3bd363b0535438470a8cc1a83c89a2591ef2a0b91acb9c4ba95626557cddf856abb9df0d2bfdb0969512f1949b6db7bff5d17109d8beb3f
  languageName: node
  linkType: hard

"@aws-crypto/sha256-browser@npm:5.2.0":
  version: 5.2.0
  resolution: "@aws-crypto/sha256-browser@npm:5.2.0"
  dependencies:
    "@aws-crypto/sha256-js": "npm:^5.2.0"
    "@aws-crypto/supports-web-crypto": "npm:^5.2.0"
    "@aws-crypto/util": "npm:^5.2.0"
    "@aws-sdk/types": "npm:^3.222.0"
    "@aws-sdk/util-locate-window": "npm:^3.0.0"
    "@smithy/util-utf8": "npm:^2.0.0"
    tslib: "npm:^2.6.2"
  checksum: 10c0/05f6d256794df800fe9aef5f52f2ac7415f7f3117d461f85a6aecaa4e29e91527b6fd503681a17136fa89e9dd3d916e9c7e4cfb5eba222875cb6c077bdc1d00d
  languageName: node
  linkType: hard

"@aws-crypto/sha256-js@npm:5.2.0, @aws-crypto/sha256-js@npm:^5.2.0":
  version: 5.2.0
  resolution: "@aws-crypto/sha256-js@npm:5.2.0"
  dependencies:
    "@aws-crypto/util": "npm:^5.2.0"
    "@aws-sdk/types": "npm:^3.222.0"
    tslib: "npm:^2.6.2"
  checksum: 10c0/6c48701f8336341bb104dfde3d0050c89c288051f6b5e9bdfeb8091cf3ffc86efcd5c9e6ff2a4a134406b019c07aca9db608128f8d9267c952578a3108db9fd1
  languageName: node
  linkType: hard

"@aws-crypto/supports-web-crypto@npm:^5.2.0":
  version: 5.2.0
  resolution: "@aws-crypto/supports-web-crypto@npm:5.2.0"
  dependencies:
    tslib: "npm:^2.6.2"
  checksum: 10c0/4d2118e29d68ca3f5947f1e37ce1fbb3239a0c569cc938cdc8ab8390d595609b5caf51a07c9e0535105b17bf5c52ea256fed705a07e9681118120ab64ee73af2
  languageName: node
  linkType: hard

"@aws-crypto/util@npm:^3.0.0":
  version: 3.0.0
  resolution: "@aws-crypto/util@npm:3.0.0"
  dependencies:
    "@aws-sdk/types": "npm:^3.222.0"
    "@aws-sdk/util-utf8-browser": "npm:^3.0.0"
    tslib: "npm:^1.11.1"
  checksum: 10c0/71ab6963daabbf080b274e24d160e4af6c8bbb6832bb885644018849ff53356bf82bb8000b8596cf296e7d6b14ad6201872b6b902f944e97e121eb2b2f692667
  languageName: node
  linkType: hard

"@aws-crypto/util@npm:^5.2.0":
  version: 5.2.0
  resolution: "@aws-crypto/util@npm:5.2.0"
  dependencies:
    "@aws-sdk/types": "npm:^3.222.0"
    "@smithy/util-utf8": "npm:^2.0.0"
    tslib: "npm:^2.6.2"
  checksum: 10c0/0362d4c197b1fd64b423966945130207d1fe23e1bb2878a18e361f7743c8d339dad3f8729895a29aa34fff6a86c65f281cf5167c4bf253f21627ae80b6dd2951
  languageName: node
  linkType: hard

"@aws-sdk/client-cognito-identity@npm:3.830.0":
  version: 3.830.0
  resolution: "@aws-sdk/client-cognito-identity@npm:3.830.0"
  dependencies:
    "@aws-crypto/sha256-browser": "npm:5.2.0"
    "@aws-crypto/sha256-js": "npm:5.2.0"
    "@aws-sdk/core": "npm:3.826.0"
    "@aws-sdk/credential-provider-node": "npm:3.830.0"
    "@aws-sdk/middleware-host-header": "npm:3.821.0"
    "@aws-sdk/middleware-logger": "npm:3.821.0"
    "@aws-sdk/middleware-recursion-detection": "npm:3.821.0"
    "@aws-sdk/middleware-user-agent": "npm:3.828.0"
    "@aws-sdk/region-config-resolver": "npm:3.821.0"
    "@aws-sdk/types": "npm:3.821.0"
    "@aws-sdk/util-endpoints": "npm:3.828.0"
    "@aws-sdk/util-user-agent-browser": "npm:3.821.0"
    "@aws-sdk/util-user-agent-node": "npm:3.828.0"
    "@smithy/config-resolver": "npm:^4.1.4"
    "@smithy/core": "npm:^3.5.3"
    "@smithy/fetch-http-handler": "npm:^5.0.4"
    "@smithy/hash-node": "npm:^4.0.4"
    "@smithy/invalid-dependency": "npm:^4.0.4"
    "@smithy/middleware-content-length": "npm:^4.0.4"
    "@smithy/middleware-endpoint": "npm:^4.1.11"
    "@smithy/middleware-retry": "npm:^4.1.12"
    "@smithy/middleware-serde": "npm:^4.0.8"
    "@smithy/middleware-stack": "npm:^4.0.4"
    "@smithy/node-config-provider": "npm:^4.1.3"
    "@smithy/node-http-handler": "npm:^4.0.6"
    "@smithy/protocol-http": "npm:^5.1.2"
    "@smithy/smithy-client": "npm:^4.4.3"
    "@smithy/types": "npm:^4.3.1"
    "@smithy/url-parser": "npm:^4.0.4"
    "@smithy/util-base64": "npm:^4.0.0"
    "@smithy/util-body-length-browser": "npm:^4.0.0"
    "@smithy/util-body-length-node": "npm:^4.0.0"
    "@smithy/util-defaults-mode-browser": "npm:^4.0.19"
    "@smithy/util-defaults-mode-node": "npm:^4.0.19"
    "@smithy/util-endpoints": "npm:^3.0.6"
    "@smithy/util-middleware": "npm:^4.0.4"
    "@smithy/util-retry": "npm:^4.0.5"
    "@smithy/util-utf8": "npm:^4.0.0"
    tslib: "npm:^2.6.2"
  checksum: 10c0/17b234e5cd5090acb0e84f934cef1435cb078c6e74c0c6f328ea248229fee841912e5a076d00819c212006e8d5fe269a3b19c5b3829fc8f4ec3e5ee93b4135dd
  languageName: node
  linkType: hard

"@aws-sdk/client-sagemaker@npm:^3.583.0":
  version: 3.833.0
  resolution: "@aws-sdk/client-sagemaker@npm:3.833.0"
  dependencies:
    "@aws-crypto/sha256-browser": "npm:5.2.0"
    "@aws-crypto/sha256-js": "npm:5.2.0"
    "@aws-sdk/core": "npm:3.826.0"
    "@aws-sdk/credential-provider-node": "npm:3.830.0"
    "@aws-sdk/middleware-host-header": "npm:3.821.0"
    "@aws-sdk/middleware-logger": "npm:3.821.0"
    "@aws-sdk/middleware-recursion-detection": "npm:3.821.0"
    "@aws-sdk/middleware-user-agent": "npm:3.828.0"
    "@aws-sdk/region-config-resolver": "npm:3.821.0"
    "@aws-sdk/types": "npm:3.821.0"
    "@aws-sdk/util-endpoints": "npm:3.828.0"
    "@aws-sdk/util-user-agent-browser": "npm:3.821.0"
    "@aws-sdk/util-user-agent-node": "npm:3.828.0"
    "@smithy/config-resolver": "npm:^4.1.4"
    "@smithy/core": "npm:^3.5.3"
    "@smithy/fetch-http-handler": "npm:^5.0.4"
    "@smithy/hash-node": "npm:^4.0.4"
    "@smithy/invalid-dependency": "npm:^4.0.4"
    "@smithy/middleware-content-length": "npm:^4.0.4"
    "@smithy/middleware-endpoint": "npm:^4.1.11"
    "@smithy/middleware-retry": "npm:^4.1.12"
    "@smithy/middleware-serde": "npm:^4.0.8"
    "@smithy/middleware-stack": "npm:^4.0.4"
    "@smithy/node-config-provider": "npm:^4.1.3"
    "@smithy/node-http-handler": "npm:^4.0.6"
    "@smithy/protocol-http": "npm:^5.1.2"
    "@smithy/smithy-client": "npm:^4.4.3"
    "@smithy/types": "npm:^4.3.1"
    "@smithy/url-parser": "npm:^4.0.4"
    "@smithy/util-base64": "npm:^4.0.0"
    "@smithy/util-body-length-browser": "npm:^4.0.0"
    "@smithy/util-body-length-node": "npm:^4.0.0"
    "@smithy/util-defaults-mode-browser": "npm:^4.0.19"
    "@smithy/util-defaults-mode-node": "npm:^4.0.19"
    "@smithy/util-endpoints": "npm:^3.0.6"
    "@smithy/util-middleware": "npm:^4.0.4"
    "@smithy/util-retry": "npm:^4.0.5"
    "@smithy/util-utf8": "npm:^4.0.0"
    "@smithy/util-waiter": "npm:^4.0.5"
    "@types/uuid": "npm:^9.0.1"
    tslib: "npm:^2.6.2"
    uuid: "npm:^9.0.1"
  checksum: 10c0/a7bd09e98c7cf85a20c05d70656a33e837c246296b0d38ed0f087f745f785f3ae0647279fc4170168111f2e0397561c09c456129a4a06cf77fce9fc7c3ce7fe1
  languageName: node
  linkType: hard

"@aws-sdk/client-sso@npm:3.830.0":
  version: 3.830.0
  resolution: "@aws-sdk/client-sso@npm:3.830.0"
  dependencies:
    "@aws-crypto/sha256-browser": "npm:5.2.0"
    "@aws-crypto/sha256-js": "npm:5.2.0"
    "@aws-sdk/core": "npm:3.826.0"
    "@aws-sdk/middleware-host-header": "npm:3.821.0"
    "@aws-sdk/middleware-logger": "npm:3.821.0"
    "@aws-sdk/middleware-recursion-detection": "npm:3.821.0"
    "@aws-sdk/middleware-user-agent": "npm:3.828.0"
    "@aws-sdk/region-config-resolver": "npm:3.821.0"
    "@aws-sdk/types": "npm:3.821.0"
    "@aws-sdk/util-endpoints": "npm:3.828.0"
    "@aws-sdk/util-user-agent-browser": "npm:3.821.0"
    "@aws-sdk/util-user-agent-node": "npm:3.828.0"
    "@smithy/config-resolver": "npm:^4.1.4"
    "@smithy/core": "npm:^3.5.3"
    "@smithy/fetch-http-handler": "npm:^5.0.4"
    "@smithy/hash-node": "npm:^4.0.4"
    "@smithy/invalid-dependency": "npm:^4.0.4"
    "@smithy/middleware-content-length": "npm:^4.0.4"
    "@smithy/middleware-endpoint": "npm:^4.1.11"
    "@smithy/middleware-retry": "npm:^4.1.12"
    "@smithy/middleware-serde": "npm:^4.0.8"
    "@smithy/middleware-stack": "npm:^4.0.4"
    "@smithy/node-config-provider": "npm:^4.1.3"
    "@smithy/node-http-handler": "npm:^4.0.6"
    "@smithy/protocol-http": "npm:^5.1.2"
    "@smithy/smithy-client": "npm:^4.4.3"
    "@smithy/types": "npm:^4.3.1"
    "@smithy/url-parser": "npm:^4.0.4"
    "@smithy/util-base64": "npm:^4.0.0"
    "@smithy/util-body-length-browser": "npm:^4.0.0"
    "@smithy/util-body-length-node": "npm:^4.0.0"
    "@smithy/util-defaults-mode-browser": "npm:^4.0.19"
    "@smithy/util-defaults-mode-node": "npm:^4.0.19"
    "@smithy/util-endpoints": "npm:^3.0.6"
    "@smithy/util-middleware": "npm:^4.0.4"
    "@smithy/util-retry": "npm:^4.0.5"
    "@smithy/util-utf8": "npm:^4.0.0"
    tslib: "npm:^2.6.2"
  checksum: 10c0/1e457581b5c05e2cff22c7dfd9b572b92a0e2b494dffdc4ff2869ab6d2f38cb9b69a153d6bc6e4210ebc83231fda6cb3fc2530bd9763052be149eed7e4dbf983
  languageName: node
  linkType: hard

"@aws-sdk/core@npm:3.826.0":
  version: 3.826.0
  resolution: "@aws-sdk/core@npm:3.826.0"
  dependencies:
    "@aws-sdk/types": "npm:3.821.0"
    "@aws-sdk/xml-builder": "npm:3.821.0"
    "@smithy/core": "npm:^3.5.3"
    "@smithy/node-config-provider": "npm:^4.1.3"
    "@smithy/property-provider": "npm:^4.0.4"
    "@smithy/protocol-http": "npm:^5.1.2"
    "@smithy/signature-v4": "npm:^5.1.2"
    "@smithy/smithy-client": "npm:^4.4.3"
    "@smithy/types": "npm:^4.3.1"
    "@smithy/util-base64": "npm:^4.0.0"
    "@smithy/util-body-length-browser": "npm:^4.0.0"
    "@smithy/util-middleware": "npm:^4.0.4"
    "@smithy/util-utf8": "npm:^4.0.0"
    fast-xml-parser: "npm:4.4.1"
    tslib: "npm:^2.6.2"
  checksum: 10c0/d8e1d72e8503d27c31891aabc5e7de893e4994a64d48e80614547c9cb886e664f068ec3fd80d6e3dcea16950a9dfe4abb6cdca2552775c12b0c8ea1d05588c71
  languageName: node
  linkType: hard

"@aws-sdk/credential-provider-cognito-identity@npm:3.830.0":
  version: 3.830.0
  resolution: "@aws-sdk/credential-provider-cognito-identity@npm:3.830.0"
  dependencies:
    "@aws-sdk/client-cognito-identity": "npm:3.830.0"
    "@aws-sdk/types": "npm:3.821.0"
    "@smithy/property-provider": "npm:^4.0.4"
    "@smithy/types": "npm:^4.3.1"
    tslib: "npm:^2.6.2"
  checksum: 10c0/3ee2ccca97be9759356a32ccf0bd4804f289a56b27a1f4a3f6316085288a17cb0d6bd1ed76ec61cb040d30ff70fa706fa7b04ff34187c826bf35f6751b6945e3
  languageName: node
  linkType: hard

"@aws-sdk/credential-provider-env@npm:3.826.0":
  version: 3.826.0
  resolution: "@aws-sdk/credential-provider-env@npm:3.826.0"
  dependencies:
    "@aws-sdk/core": "npm:3.826.0"
    "@aws-sdk/types": "npm:3.821.0"
    "@smithy/property-provider": "npm:^4.0.4"
    "@smithy/types": "npm:^4.3.1"
    tslib: "npm:^2.6.2"
  checksum: 10c0/3cfcf7ec921c39ce0e5cb43d821e99d1260a1aa62e16617383f1401a0f600fe60a77cf92f97f86c9a81b99d741a8d640f1401da1e492b5a9f845833031c1ee44
  languageName: node
  linkType: hard

"@aws-sdk/credential-provider-http@npm:3.826.0":
  version: 3.826.0
  resolution: "@aws-sdk/credential-provider-http@npm:3.826.0"
  dependencies:
    "@aws-sdk/core": "npm:3.826.0"
    "@aws-sdk/types": "npm:3.821.0"
    "@smithy/fetch-http-handler": "npm:^5.0.4"
    "@smithy/node-http-handler": "npm:^4.0.6"
    "@smithy/property-provider": "npm:^4.0.4"
    "@smithy/protocol-http": "npm:^5.1.2"
    "@smithy/smithy-client": "npm:^4.4.3"
    "@smithy/types": "npm:^4.3.1"
    "@smithy/util-stream": "npm:^4.2.2"
    tslib: "npm:^2.6.2"
  checksum: 10c0/042c3b873cc57acfd711ff90350a32d2f4371a1de80ec301d9e4268c184ec42d6de94ecbab3784d6853a18c6fb3bb06a198d02f9025479681e1e9a230b18e436
  languageName: node
  linkType: hard

"@aws-sdk/credential-provider-ini@npm:3.830.0":
  version: 3.830.0
  resolution: "@aws-sdk/credential-provider-ini@npm:3.830.0"
  dependencies:
    "@aws-sdk/core": "npm:3.826.0"
    "@aws-sdk/credential-provider-env": "npm:3.826.0"
    "@aws-sdk/credential-provider-http": "npm:3.826.0"
    "@aws-sdk/credential-provider-process": "npm:3.826.0"
    "@aws-sdk/credential-provider-sso": "npm:3.830.0"
    "@aws-sdk/credential-provider-web-identity": "npm:3.830.0"
    "@aws-sdk/nested-clients": "npm:3.830.0"
    "@aws-sdk/types": "npm:3.821.0"
    "@smithy/credential-provider-imds": "npm:^4.0.6"
    "@smithy/property-provider": "npm:^4.0.4"
    "@smithy/shared-ini-file-loader": "npm:^4.0.4"
    "@smithy/types": "npm:^4.3.1"
    tslib: "npm:^2.6.2"
  checksum: 10c0/766eabc244574668235f132078038fa4fb29785b8d7d2e5339f055c0ae827977526ace3cf5feaa66c8adf4c226a91609738895e6c9f507ca2297039bda83fcb6
  languageName: node
  linkType: hard

"@aws-sdk/credential-provider-node@npm:3.830.0":
  version: 3.830.0
  resolution: "@aws-sdk/credential-provider-node@npm:3.830.0"
  dependencies:
    "@aws-sdk/credential-provider-env": "npm:3.826.0"
    "@aws-sdk/credential-provider-http": "npm:3.826.0"
    "@aws-sdk/credential-provider-ini": "npm:3.830.0"
    "@aws-sdk/credential-provider-process": "npm:3.826.0"
    "@aws-sdk/credential-provider-sso": "npm:3.830.0"
    "@aws-sdk/credential-provider-web-identity": "npm:3.830.0"
    "@aws-sdk/types": "npm:3.821.0"
    "@smithy/credential-provider-imds": "npm:^4.0.6"
    "@smithy/property-provider": "npm:^4.0.4"
    "@smithy/shared-ini-file-loader": "npm:^4.0.4"
    "@smithy/types": "npm:^4.3.1"
    tslib: "npm:^2.6.2"
  checksum: 10c0/1c007cc2547703227128247d05dd9c2b49c0f9f3069bc7b49eaedf3764909811017fb2561c25773caf1478bc0822decdcfa2b041c204c393cba69f26a9fea071
  languageName: node
  linkType: hard

"@aws-sdk/credential-provider-process@npm:3.826.0":
  version: 3.826.0
  resolution: "@aws-sdk/credential-provider-process@npm:3.826.0"
  dependencies:
    "@aws-sdk/core": "npm:3.826.0"
    "@aws-sdk/types": "npm:3.821.0"
    "@smithy/property-provider": "npm:^4.0.4"
    "@smithy/shared-ini-file-loader": "npm:^4.0.4"
    "@smithy/types": "npm:^4.3.1"
    tslib: "npm:^2.6.2"
  checksum: 10c0/af6d32620b32cbe5069e967b6727e79076b9be9ae22977da5e0f08a9e95358a43060455e75fa77e58dae22ffe058f0c4c055675b3f80ecce34a2680464988da5
  languageName: node
  linkType: hard

"@aws-sdk/credential-provider-sso@npm:3.830.0":
  version: 3.830.0
  resolution: "@aws-sdk/credential-provider-sso@npm:3.830.0"
  dependencies:
    "@aws-sdk/client-sso": "npm:3.830.0"
    "@aws-sdk/core": "npm:3.826.0"
    "@aws-sdk/token-providers": "npm:3.830.0"
    "@aws-sdk/types": "npm:3.821.0"
    "@smithy/property-provider": "npm:^4.0.4"
    "@smithy/shared-ini-file-loader": "npm:^4.0.4"
    "@smithy/types": "npm:^4.3.1"
    tslib: "npm:^2.6.2"
  checksum: 10c0/9d30bf8c1c45957eadbb912db9303fc7b94aee3dc76f7189e451bd60dd263ca4114ace2651b0ac84c4150c736ec9bd4d66a4eaa776388a163d34a779c9ab1d71
  languageName: node
  linkType: hard

"@aws-sdk/credential-provider-web-identity@npm:3.830.0":
  version: 3.830.0
  resolution: "@aws-sdk/credential-provider-web-identity@npm:3.830.0"
  dependencies:
    "@aws-sdk/core": "npm:3.826.0"
    "@aws-sdk/nested-clients": "npm:3.830.0"
    "@aws-sdk/types": "npm:3.821.0"
    "@smithy/property-provider": "npm:^4.0.4"
    "@smithy/types": "npm:^4.3.1"
    tslib: "npm:^2.6.2"
  checksum: 10c0/31fea28695739267418be58a211ba71cd62f73484a38d327fc01685de4afbba018eb543ad17b10bc073dc36f9b96d1fdcaadd6d55801aa8cd02231827f779db5
  languageName: node
  linkType: hard

"@aws-sdk/credential-providers@npm:^3.583.0":
  version: 3.834.0
  resolution: "@aws-sdk/credential-providers@npm:3.834.0"
  dependencies:
    "@aws-sdk/client-cognito-identity": "npm:3.830.0"
    "@aws-sdk/core": "npm:3.826.0"
    "@aws-sdk/credential-provider-cognito-identity": "npm:3.830.0"
    "@aws-sdk/credential-provider-env": "npm:3.826.0"
    "@aws-sdk/credential-provider-http": "npm:3.826.0"
    "@aws-sdk/credential-provider-ini": "npm:3.830.0"
    "@aws-sdk/credential-provider-node": "npm:3.830.0"
    "@aws-sdk/credential-provider-process": "npm:3.826.0"
    "@aws-sdk/credential-provider-sso": "npm:3.830.0"
    "@aws-sdk/credential-provider-web-identity": "npm:3.830.0"
    "@aws-sdk/nested-clients": "npm:3.830.0"
    "@aws-sdk/types": "npm:3.821.0"
    "@smithy/config-resolver": "npm:^4.1.4"
    "@smithy/core": "npm:^3.5.3"
    "@smithy/credential-provider-imds": "npm:^4.0.6"
    "@smithy/node-config-provider": "npm:^4.1.3"
    "@smithy/property-provider": "npm:^4.0.4"
    "@smithy/types": "npm:^4.3.1"
    tslib: "npm:^2.6.2"
  checksum: 10c0/7ea7d07e26f2bc7827c449b66d2d9051b27825568b6bbc3422bb41cf60cd968f51733b9a3da6bd859cad229b40083257e7a0c6d7f359e3e676f4f109f45e5181
  languageName: node
  linkType: hard

"@aws-sdk/middleware-host-header@npm:3.821.0":
  version: 3.821.0
  resolution: "@aws-sdk/middleware-host-header@npm:3.821.0"
  dependencies:
    "@aws-sdk/types": "npm:3.821.0"
    "@smithy/protocol-http": "npm:^5.1.2"
    "@smithy/types": "npm:^4.3.1"
    tslib: "npm:^2.6.2"
  checksum: 10c0/8c6282ef885a455cf34667831577085fca3c58cd7b4ee3c773eb7fabab0704583497a13230b1f877879414646f1eb74225278c7587b3207df042b054d777d4b7
  languageName: node
  linkType: hard

"@aws-sdk/middleware-logger@npm:3.821.0":
  version: 3.821.0
  resolution: "@aws-sdk/middleware-logger@npm:3.821.0"
  dependencies:
    "@aws-sdk/types": "npm:3.821.0"
    "@smithy/types": "npm:^4.3.1"
    tslib: "npm:^2.6.2"
  checksum: 10c0/2d9d9995c62e741fa059dbf8b20ff2000c63b73c2ece256146e1c5862a6aec4ab14165bfa52c9369906eca906f7b2247b6795c2e29f66408b5195c5ac738963a
  languageName: node
  linkType: hard

"@aws-sdk/middleware-recursion-detection@npm:3.821.0":
  version: 3.821.0
  resolution: "@aws-sdk/middleware-recursion-detection@npm:3.821.0"
  dependencies:
    "@aws-sdk/types": "npm:3.821.0"
    "@smithy/protocol-http": "npm:^5.1.2"
    "@smithy/types": "npm:^4.3.1"
    tslib: "npm:^2.6.2"
  checksum: 10c0/209d7cad9c86baa84053c3a6bdb364cd5ad06d6f964f0a040b8572ac5300723adfb6914f4e875024f1af5058ca6f468c5c9d5c8c0d283b403ffed5e08dccb711
  languageName: node
  linkType: hard

"@aws-sdk/middleware-user-agent@npm:3.828.0":
  version: 3.828.0
  resolution: "@aws-sdk/middleware-user-agent@npm:3.828.0"
  dependencies:
    "@aws-sdk/core": "npm:3.826.0"
    "@aws-sdk/types": "npm:3.821.0"
    "@aws-sdk/util-endpoints": "npm:3.828.0"
    "@smithy/core": "npm:^3.5.3"
    "@smithy/protocol-http": "npm:^5.1.2"
    "@smithy/types": "npm:^4.3.1"
    tslib: "npm:^2.6.2"
  checksum: 10c0/3b2576d33f21d07b6b6b42f823e9277d55ea81913e6ad526b300961c5525e70d1571dd6953c282208b3da6a58ffb19fa2bc3b3b04962122cb87365bb7353478a
  languageName: node
  linkType: hard

"@aws-sdk/nested-clients@npm:3.830.0":
  version: 3.830.0
  resolution: "@aws-sdk/nested-clients@npm:3.830.0"
  dependencies:
    "@aws-crypto/sha256-browser": "npm:5.2.0"
    "@aws-crypto/sha256-js": "npm:5.2.0"
    "@aws-sdk/core": "npm:3.826.0"
    "@aws-sdk/middleware-host-header": "npm:3.821.0"
    "@aws-sdk/middleware-logger": "npm:3.821.0"
    "@aws-sdk/middleware-recursion-detection": "npm:3.821.0"
    "@aws-sdk/middleware-user-agent": "npm:3.828.0"
    "@aws-sdk/region-config-resolver": "npm:3.821.0"
    "@aws-sdk/types": "npm:3.821.0"
    "@aws-sdk/util-endpoints": "npm:3.828.0"
    "@aws-sdk/util-user-agent-browser": "npm:3.821.0"
    "@aws-sdk/util-user-agent-node": "npm:3.828.0"
    "@smithy/config-resolver": "npm:^4.1.4"
    "@smithy/core": "npm:^3.5.3"
    "@smithy/fetch-http-handler": "npm:^5.0.4"
    "@smithy/hash-node": "npm:^4.0.4"
    "@smithy/invalid-dependency": "npm:^4.0.4"
    "@smithy/middleware-content-length": "npm:^4.0.4"
    "@smithy/middleware-endpoint": "npm:^4.1.11"
    "@smithy/middleware-retry": "npm:^4.1.12"
    "@smithy/middleware-serde": "npm:^4.0.8"
    "@smithy/middleware-stack": "npm:^4.0.4"
    "@smithy/node-config-provider": "npm:^4.1.3"
    "@smithy/node-http-handler": "npm:^4.0.6"
    "@smithy/protocol-http": "npm:^5.1.2"
    "@smithy/smithy-client": "npm:^4.4.3"
    "@smithy/types": "npm:^4.3.1"
    "@smithy/url-parser": "npm:^4.0.4"
    "@smithy/util-base64": "npm:^4.0.0"
    "@smithy/util-body-length-browser": "npm:^4.0.0"
    "@smithy/util-body-length-node": "npm:^4.0.0"
    "@smithy/util-defaults-mode-browser": "npm:^4.0.19"
    "@smithy/util-defaults-mode-node": "npm:^4.0.19"
    "@smithy/util-endpoints": "npm:^3.0.6"
    "@smithy/util-middleware": "npm:^4.0.4"
    "@smithy/util-retry": "npm:^4.0.5"
    "@smithy/util-utf8": "npm:^4.0.0"
    tslib: "npm:^2.6.2"
  checksum: 10c0/14cbf4d11e8600e5cbb7519518c1b51ee950b5057d9b3a78d297656689e031aa1cb7805f8ffeecc195cbc93a517c04f925db550ac79bbb9f75b47d3e5d7f366c
  languageName: node
  linkType: hard

"@aws-sdk/protocol-http@npm:^3.374.0":
  version: 3.374.0
  resolution: "@aws-sdk/protocol-http@npm:3.374.0"
  dependencies:
    "@smithy/protocol-http": "npm:^1.1.0"
    tslib: "npm:^2.5.0"
  checksum: 10c0/80770a166a1c85b69f80c43bb648c2892a5d649db048c2fe9a45d7b2c0ced6e4991b211279b02c0628a706504638656d95b962d4cca6c75f7e937cc504ec8807
  languageName: node
  linkType: hard

"@aws-sdk/region-config-resolver@npm:3.821.0":
  version: 3.821.0
  resolution: "@aws-sdk/region-config-resolver@npm:3.821.0"
  dependencies:
    "@aws-sdk/types": "npm:3.821.0"
    "@smithy/node-config-provider": "npm:^4.1.3"
    "@smithy/types": "npm:^4.3.1"
    "@smithy/util-config-provider": "npm:^4.0.0"
    "@smithy/util-middleware": "npm:^4.0.4"
    tslib: "npm:^2.6.2"
  checksum: 10c0/b0f8a22541db5f4d5eee79320fe3d97dfa9cd7c37130771b354771c811243d458e18f22dfead70802ade8ce7537376eea4931479922df15a2e93378cf210ec30
  languageName: node
  linkType: hard

"@aws-sdk/signature-v4@npm:^3.374.0":
  version: 3.374.0
  resolution: "@aws-sdk/signature-v4@npm:3.374.0"
  dependencies:
    "@smithy/signature-v4": "npm:^1.0.1"
    tslib: "npm:^2.5.0"
  checksum: 10c0/11d1175e25f16f2d3ca1c0abfddd0da3acb2e0a4733ebb3d2c309e92f62aa0ac2f5891e8fa7d0f753fde9eca2f5f32a74727a6458c9818f8dcdf847eb68eeb0f
  languageName: node
  linkType: hard

"@aws-sdk/token-providers@npm:3.830.0":
  version: 3.830.0
  resolution: "@aws-sdk/token-providers@npm:3.830.0"
  dependencies:
    "@aws-sdk/core": "npm:3.826.0"
    "@aws-sdk/nested-clients": "npm:3.830.0"
    "@aws-sdk/types": "npm:3.821.0"
    "@smithy/property-provider": "npm:^4.0.4"
    "@smithy/shared-ini-file-loader": "npm:^4.0.4"
    "@smithy/types": "npm:^4.3.1"
    tslib: "npm:^2.6.2"
  checksum: 10c0/2a541d1b329b5a285a69d25695051c1069a266902d5996d4fdd1b6d4f43bbd53780ab48e27eda0625e35a3c58b4d6ee6ddf97249bf0b73e0c4d43879a6c9159a
  languageName: node
  linkType: hard

"@aws-sdk/types@npm:3.821.0, @aws-sdk/types@npm:^3.222.0":
  version: 3.821.0
  resolution: "@aws-sdk/types@npm:3.821.0"
  dependencies:
    "@smithy/types": "npm:^4.3.1"
    tslib: "npm:^2.6.2"
  checksum: 10c0/6202b2c0db1dd5ee78e6dc45c51f8b19deff0ee400dd5a7a15d089cc5493a2db6a6e0553ff32742e8bc810d428b36599534e14c1b466695550aef1b1d87f043d
  languageName: node
  linkType: hard

"@aws-sdk/util-endpoints@npm:3.828.0":
  version: 3.828.0
  resolution: "@aws-sdk/util-endpoints@npm:3.828.0"
  dependencies:
    "@aws-sdk/types": "npm:3.821.0"
    "@smithy/types": "npm:^4.3.1"
    "@smithy/util-endpoints": "npm:^3.0.6"
    tslib: "npm:^2.6.2"
  checksum: 10c0/650a8bd823f22c6fb1ebc21bf418af9de99c969a8b856721dcadf563b7699971db475ce1c725b9e5423dd8c30801c1cd9e0a5cbe17bb71b5fe74740669c213cf
  languageName: node
  linkType: hard

"@aws-sdk/util-locate-window@npm:^3.0.0":
  version: 3.804.0
  resolution: "@aws-sdk/util-locate-window@npm:3.804.0"
  dependencies:
    tslib: "npm:^2.6.2"
  checksum: 10c0/a0ceaf6531f188751fea7e829b730650689fa2196e0b3f870dde3888bcb840fe0852e10488699d4d9683db0765cd7f7060ca8ac216348991996b6d794f9957ab
  languageName: node
  linkType: hard

"@aws-sdk/util-user-agent-browser@npm:3.821.0":
  version: 3.821.0
  resolution: "@aws-sdk/util-user-agent-browser@npm:3.821.0"
  dependencies:
    "@aws-sdk/types": "npm:3.821.0"
    "@smithy/types": "npm:^4.3.1"
    bowser: "npm:^2.11.0"
    tslib: "npm:^2.6.2"
  checksum: 10c0/e0045e9d3798c96f9e4da949a79a54a725ad5806129691a967fd0fad4bcea4c65cd7a962c71eab9f61417d804c281faa42c5cd461cca151ea931349ee4cf2a2b
  languageName: node
  linkType: hard

"@aws-sdk/util-user-agent-node@npm:3.828.0":
  version: 3.828.0
  resolution: "@aws-sdk/util-user-agent-node@npm:3.828.0"
  dependencies:
    "@aws-sdk/middleware-user-agent": "npm:3.828.0"
    "@aws-sdk/types": "npm:3.821.0"
    "@smithy/node-config-provider": "npm:^4.1.3"
    "@smithy/types": "npm:^4.3.1"
    tslib: "npm:^2.6.2"
  peerDependencies:
    aws-crt: ">=1.0.0"
  peerDependenciesMeta:
    aws-crt:
      optional: true
  checksum: 10c0/19997223540d49bbeaeae5aafc7b0d81bb9ae4af83405e66cd19beb57a93f35cb8866216ae699da2c9e99fff3986557e77679267338cf40380f8a49e767e6dbd
  languageName: node
  linkType: hard

"@aws-sdk/util-utf8-browser@npm:^3.0.0":
  version: 3.259.0
  resolution: "@aws-sdk/util-utf8-browser@npm:3.259.0"
  dependencies:
    tslib: "npm:^2.3.1"
  checksum: 10c0/ff56ff252c0ea22b760b909ba5bbe9ca59a447066097e73b1e2ae50a6d366631ba560c373ec4e83b3e225d16238eeaf8def210fdbf135070b3dd3ceb1cc2ef9a
  languageName: node
  linkType: hard

"@aws-sdk/xml-builder@npm:3.821.0":
  version: 3.821.0
  resolution: "@aws-sdk/xml-builder@npm:3.821.0"
  dependencies:
    "@smithy/types": "npm:^4.3.1"
    tslib: "npm:^2.6.2"
  checksum: 10c0/316e0eb04bcec0bb0897f67718629deab29adb9664ce78743ad854df772472c02332ab12627d74b96ebe2205adc51b1cb7fb01fcb4251e80a7af405e56cfa135
  languageName: node
  linkType: hard

"@babel/code-frame@npm:^7.0.0, @babel/code-frame@npm:^7.12.13, @babel/code-frame@npm:^7.27.1":
  version: 7.27.1
  resolution: "@babel/code-frame@npm:7.27.1"
  dependencies:
    "@babel/helper-validator-identifier": "npm:^7.27.1"
    js-tokens: "npm:^4.0.0"
    picocolors: "npm:^1.1.1"
  checksum: 10c0/5dd9a18baa5fce4741ba729acc3a3272c49c25cb8736c4b18e113099520e7ef7b545a4096a26d600e4416157e63e87d66db46aa3fbf0a5f2286da2705c12da00
  languageName: node
  linkType: hard

"@babel/compat-data@npm:^7.27.2":
  version: 7.27.5
  resolution: "@babel/compat-data@npm:7.27.5"
  checksum: 10c0/da2751fcd0b58eea958f2b2f7ff7d6de1280712b709fa1ad054b73dc7d31f589e353bb50479b9dc96007935f3ed3cada68ac5b45ce93086b7122ddc32e60dc00
  languageName: node
  linkType: hard

"@babel/core@npm:^7.11.6, @babel/core@npm:^7.12.3, @babel/core@npm:^7.23.9, @babel/core@npm:^7.27.4":
  version: 7.27.4
  resolution: "@babel/core@npm:7.27.4"
  dependencies:
    "@ampproject/remapping": "npm:^2.2.0"
    "@babel/code-frame": "npm:^7.27.1"
    "@babel/generator": "npm:^7.27.3"
    "@babel/helper-compilation-targets": "npm:^7.27.2"
    "@babel/helper-module-transforms": "npm:^7.27.3"
    "@babel/helpers": "npm:^7.27.4"
    "@babel/parser": "npm:^7.27.4"
    "@babel/template": "npm:^7.27.2"
    "@babel/traverse": "npm:^7.27.4"
    "@babel/types": "npm:^7.27.3"
    convert-source-map: "npm:^2.0.0"
    debug: "npm:^4.1.0"
    gensync: "npm:^1.0.0-beta.2"
    json5: "npm:^2.2.3"
    semver: "npm:^6.3.1"
  checksum: 10c0/d2d17b106a8d91d3eda754bb3f26b53a12eb7646df73c2b2d2e9b08d90529186bc69e3823f70a96ec6e5719dc2372fb54e14ad499da47ceeb172d2f7008787b5
  languageName: node
  linkType: hard

"@babel/generator@npm:^7.27.3, @babel/generator@npm:^7.7.2":
  version: 7.27.5
  resolution: "@babel/generator@npm:7.27.5"
  dependencies:
    "@babel/parser": "npm:^7.27.5"
    "@babel/types": "npm:^7.27.3"
    "@jridgewell/gen-mapping": "npm:^0.3.5"
    "@jridgewell/trace-mapping": "npm:^0.3.25"
    jsesc: "npm:^3.0.2"
  checksum: 10c0/8f649ef4cd81765c832bb11de4d6064b035ffebdecde668ba7abee68a7b0bce5c9feabb5dc5bb8aeba5bd9e5c2afa3899d852d2bd9ca77a711ba8c8379f416f0
  languageName: node
  linkType: hard

"@babel/helper-compilation-targets@npm:^7.27.2":
  version: 7.27.2
  resolution: "@babel/helper-compilation-targets@npm:7.27.2"
  dependencies:
    "@babel/compat-data": "npm:^7.27.2"
    "@babel/helper-validator-option": "npm:^7.27.1"
    browserslist: "npm:^4.24.0"
    lru-cache: "npm:^5.1.1"
    semver: "npm:^6.3.1"
  checksum: 10c0/f338fa00dcfea931804a7c55d1a1c81b6f0a09787e528ec580d5c21b3ecb3913f6cb0f361368973ce953b824d910d3ac3e8a8ee15192710d3563826447193ad1
  languageName: node
  linkType: hard

"@babel/helper-module-imports@npm:^7.27.1":
  version: 7.27.1
  resolution: "@babel/helper-module-imports@npm:7.27.1"
  dependencies:
    "@babel/traverse": "npm:^7.27.1"
    "@babel/types": "npm:^7.27.1"
  checksum: 10c0/e00aace096e4e29290ff8648455c2bc4ed982f0d61dbf2db1b5e750b9b98f318bf5788d75a4f974c151bd318fd549e81dbcab595f46b14b81c12eda3023f51e8
  languageName: node
  linkType: hard

"@babel/helper-module-transforms@npm:^7.27.3":
  version: 7.27.3
  resolution: "@babel/helper-module-transforms@npm:7.27.3"
  dependencies:
    "@babel/helper-module-imports": "npm:^7.27.1"
    "@babel/helper-validator-identifier": "npm:^7.27.1"
    "@babel/traverse": "npm:^7.27.3"
  peerDependencies:
    "@babel/core": ^7.0.0
  checksum: 10c0/fccb4f512a13b4c069af51e1b56b20f54024bcf1591e31e978a30f3502567f34f90a80da6a19a6148c249216292a8074a0121f9e52602510ef0f32dbce95ca01
  languageName: node
  linkType: hard

"@babel/helper-plugin-utils@npm:^7.0.0, @babel/helper-plugin-utils@npm:^7.10.4, @babel/helper-plugin-utils@npm:^7.12.13, @babel/helper-plugin-utils@npm:^7.14.5, @babel/helper-plugin-utils@npm:^7.27.1, @babel/helper-plugin-utils@npm:^7.8.0":
  version: 7.27.1
  resolution: "@babel/helper-plugin-utils@npm:7.27.1"
  checksum: 10c0/94cf22c81a0c11a09b197b41ab488d416ff62254ce13c57e62912c85700dc2e99e555225787a4099ff6bae7a1812d622c80fbaeda824b79baa10a6c5ac4cf69b
  languageName: node
  linkType: hard

"@babel/helper-string-parser@npm:^7.27.1":
  version: 7.27.1
  resolution: "@babel/helper-string-parser@npm:7.27.1"
  checksum: 10c0/8bda3448e07b5583727c103560bcf9c4c24b3c1051a4c516d4050ef69df37bb9a4734a585fe12725b8c2763de0a265aa1e909b485a4e3270b7cfd3e4dbe4b602
  languageName: node
  linkType: hard

"@babel/helper-validator-identifier@npm:^7.27.1":
  version: 7.27.1
  resolution: "@babel/helper-validator-identifier@npm:7.27.1"
  checksum: 10c0/c558f11c4871d526498e49d07a84752d1800bf72ac0d3dad100309a2eaba24efbf56ea59af5137ff15e3a00280ebe588560534b0e894a4750f8b1411d8f78b84
  languageName: node
  linkType: hard

"@babel/helper-validator-option@npm:^7.27.1":
  version: 7.27.1
  resolution: "@babel/helper-validator-option@npm:7.27.1"
  checksum: 10c0/6fec5f006eba40001a20f26b1ef5dbbda377b7b68c8ad518c05baa9af3f396e780bdfded24c4eef95d14bb7b8fd56192a6ed38d5d439b97d10efc5f1a191d148
  languageName: node
  linkType: hard

"@babel/helpers@npm:^7.27.4":
  version: 7.27.6
  resolution: "@babel/helpers@npm:7.27.6"
  dependencies:
    "@babel/template": "npm:^7.27.2"
    "@babel/types": "npm:^7.27.6"
  checksum: 10c0/448bac96ef8b0f21f2294a826df9de6bf4026fd023f8a6bb6c782fe3e61946801ca24381490b8e58d861fee75cd695a1882921afbf1f53b0275ee68c938bd6d3
  languageName: node
  linkType: hard

"@babel/parser@npm:^7.1.0, @babel/parser@npm:^7.14.7, @babel/parser@npm:^7.20.7, @babel/parser@npm:^7.23.9, @babel/parser@npm:^7.27.2, @babel/parser@npm:^7.27.4, @babel/parser@npm:^7.27.5":
  version: 7.27.5
  resolution: "@babel/parser@npm:7.27.5"
  dependencies:
    "@babel/types": "npm:^7.27.3"
  bin:
    parser: ./bin/babel-parser.js
  checksum: 10c0/f7faaebf21cc1f25d9ca8ac02c447ed38ef3460ea95be7ea760916dcf529476340d72a5a6010c6641d9ed9d12ad827c8424840277ec2295c5b082ba0f291220a
  languageName: node
  linkType: hard

"@babel/plugin-syntax-async-generators@npm:^7.8.4":
  version: 7.8.4
  resolution: "@babel/plugin-syntax-async-generators@npm:7.8.4"
  dependencies:
    "@babel/helper-plugin-utils": "npm:^7.8.0"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 10c0/d13efb282838481348c71073b6be6245b35d4f2f964a8f71e4174f235009f929ef7613df25f8d2338e2d3e44bc4265a9f8638c6aaa136d7a61fe95985f9725c8
  languageName: node
  linkType: hard

"@babel/plugin-syntax-bigint@npm:^7.8.3":
  version: 7.8.3
  resolution: "@babel/plugin-syntax-bigint@npm:7.8.3"
  dependencies:
    "@babel/helper-plugin-utils": "npm:^7.8.0"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 10c0/686891b81af2bc74c39013655da368a480f17dd237bf9fbc32048e5865cb706d5a8f65438030da535b332b1d6b22feba336da8fa931f663b6b34e13147d12dde
  languageName: node
  linkType: hard

"@babel/plugin-syntax-class-properties@npm:^7.12.13":
  version: 7.12.13
  resolution: "@babel/plugin-syntax-class-properties@npm:7.12.13"
  dependencies:
    "@babel/helper-plugin-utils": "npm:^7.12.13"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 10c0/95168fa186416195280b1264fb18afcdcdcea780b3515537b766cb90de6ce042d42dd6a204a39002f794ae5845b02afb0fd4861a3308a861204a55e68310a120
  languageName: node
  linkType: hard

"@babel/plugin-syntax-class-static-block@npm:^7.14.5":
  version: 7.14.5
  resolution: "@babel/plugin-syntax-class-static-block@npm:7.14.5"
  dependencies:
    "@babel/helper-plugin-utils": "npm:^7.14.5"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 10c0/4464bf9115f4a2d02ce1454411baf9cfb665af1da53709c5c56953e5e2913745b0fcce82982a00463d6facbdd93445c691024e310b91431a1e2f024b158f6371
  languageName: node
  linkType: hard

"@babel/plugin-syntax-import-attributes@npm:^7.24.7":
  version: 7.27.1
  resolution: "@babel/plugin-syntax-import-attributes@npm:7.27.1"
  dependencies:
    "@babel/helper-plugin-utils": "npm:^7.27.1"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 10c0/e66f7a761b8360419bbb93ab67d87c8a97465ef4637a985ff682ce7ba6918b34b29d81190204cf908d0933058ee7b42737423cd8a999546c21b3aabad4affa9a
  languageName: node
  linkType: hard

"@babel/plugin-syntax-import-meta@npm:^7.10.4":
  version: 7.10.4
  resolution: "@babel/plugin-syntax-import-meta@npm:7.10.4"
  dependencies:
    "@babel/helper-plugin-utils": "npm:^7.10.4"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 10c0/0b08b5e4c3128523d8e346f8cfc86824f0da2697b1be12d71af50a31aff7a56ceb873ed28779121051475010c28d6146a6bfea8518b150b71eeb4e46190172ee
  languageName: node
  linkType: hard

"@babel/plugin-syntax-json-strings@npm:^7.8.3":
  version: 7.8.3
  resolution: "@babel/plugin-syntax-json-strings@npm:7.8.3"
  dependencies:
    "@babel/helper-plugin-utils": "npm:^7.8.0"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 10c0/e98f31b2ec406c57757d115aac81d0336e8434101c224edd9a5c93cefa53faf63eacc69f3138960c8b25401315af03df37f68d316c151c4b933136716ed6906e
  languageName: node
  linkType: hard

"@babel/plugin-syntax-jsx@npm:^7.7.2":
  version: 7.27.1
  resolution: "@babel/plugin-syntax-jsx@npm:7.27.1"
  dependencies:
    "@babel/helper-plugin-utils": "npm:^7.27.1"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 10c0/bc5afe6a458d5f0492c02a54ad98c5756a0c13bd6d20609aae65acd560a9e141b0876da5f358dce34ea136f271c1016df58b461184d7ae9c4321e0f98588bc84
  languageName: node
  linkType: hard

"@babel/plugin-syntax-logical-assignment-operators@npm:^7.10.4":
  version: 7.10.4
  resolution: "@babel/plugin-syntax-logical-assignment-operators@npm:7.10.4"
  dependencies:
    "@babel/helper-plugin-utils": "npm:^7.10.4"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 10c0/2594cfbe29411ad5bc2ad4058de7b2f6a8c5b86eda525a993959438615479e59c012c14aec979e538d60a584a1a799b60d1b8942c3b18468cb9d99b8fd34cd0b
  languageName: node
  linkType: hard

"@babel/plugin-syntax-nullish-coalescing-operator@npm:^7.8.3":
  version: 7.8.3
  resolution: "@babel/plugin-syntax-nullish-coalescing-operator@npm:7.8.3"
  dependencies:
    "@babel/helper-plugin-utils": "npm:^7.8.0"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 10c0/2024fbb1162899094cfc81152449b12bd0cc7053c6d4bda8ac2852545c87d0a851b1b72ed9560673cbf3ef6248257262c3c04aabf73117215c1b9cc7dd2542ce
  languageName: node
  linkType: hard

"@babel/plugin-syntax-numeric-separator@npm:^7.10.4":
  version: 7.10.4
  resolution: "@babel/plugin-syntax-numeric-separator@npm:7.10.4"
  dependencies:
    "@babel/helper-plugin-utils": "npm:^7.10.4"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 10c0/c55a82b3113480942c6aa2fcbe976ff9caa74b7b1109ff4369641dfbc88d1da348aceb3c31b6ed311c84d1e7c479440b961906c735d0ab494f688bf2fd5b9bb9
  languageName: node
  linkType: hard

"@babel/plugin-syntax-object-rest-spread@npm:^7.8.3":
  version: 7.8.3
  resolution: "@babel/plugin-syntax-object-rest-spread@npm:7.8.3"
  dependencies:
    "@babel/helper-plugin-utils": "npm:^7.8.0"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 10c0/ee1eab52ea6437e3101a0a7018b0da698545230015fc8ab129d292980ec6dff94d265e9e90070e8ae5fed42f08f1622c14c94552c77bcac784b37f503a82ff26
  languageName: node
  linkType: hard

"@babel/plugin-syntax-optional-catch-binding@npm:^7.8.3":
  version: 7.8.3
  resolution: "@babel/plugin-syntax-optional-catch-binding@npm:7.8.3"
  dependencies:
    "@babel/helper-plugin-utils": "npm:^7.8.0"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 10c0/27e2493ab67a8ea6d693af1287f7e9acec206d1213ff107a928e85e173741e1d594196f99fec50e9dde404b09164f39dec5864c767212154ffe1caa6af0bc5af
  languageName: node
  linkType: hard

"@babel/plugin-syntax-optional-chaining@npm:^7.8.3":
  version: 7.8.3
  resolution: "@babel/plugin-syntax-optional-chaining@npm:7.8.3"
  dependencies:
    "@babel/helper-plugin-utils": "npm:^7.8.0"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 10c0/46edddf2faa6ebf94147b8e8540dfc60a5ab718e2de4d01b2c0bdf250a4d642c2bd47cbcbb739febcb2bf75514dbcefad3c52208787994b8d0f8822490f55e81
  languageName: node
  linkType: hard

"@babel/plugin-syntax-private-property-in-object@npm:^7.14.5":
  version: 7.14.5
  resolution: "@babel/plugin-syntax-private-property-in-object@npm:7.14.5"
  dependencies:
    "@babel/helper-plugin-utils": "npm:^7.14.5"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 10c0/69822772561706c87f0a65bc92d0772cea74d6bc0911537904a676d5ff496a6d3ac4e05a166d8125fce4a16605bace141afc3611074e170a994e66e5397787f3
  languageName: node
  linkType: hard

"@babel/plugin-syntax-top-level-await@npm:^7.14.5":
  version: 7.14.5
  resolution: "@babel/plugin-syntax-top-level-await@npm:7.14.5"
  dependencies:
    "@babel/helper-plugin-utils": "npm:^7.14.5"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 10c0/14bf6e65d5bc1231ffa9def5f0ef30b19b51c218fcecaa78cd1bdf7939dfdf23f90336080b7f5196916368e399934ce5d581492d8292b46a2fb569d8b2da106f
  languageName: node
  linkType: hard

"@babel/plugin-syntax-typescript@npm:^7.7.2":
  version: 7.27.1
  resolution: "@babel/plugin-syntax-typescript@npm:7.27.1"
  dependencies:
    "@babel/helper-plugin-utils": "npm:^7.27.1"
  peerDependencies:
    "@babel/core": ^7.0.0-0
  checksum: 10c0/11589b4c89c66ef02d57bf56c6246267851ec0c361f58929327dc3e070b0dab644be625bbe7fb4c4df30c3634bfdfe31244e1f517be397d2def1487dbbe3c37d
  languageName: node
  linkType: hard

"@babel/runtime@npm:^7.21.0":
  version: 7.27.6
  resolution: "@babel/runtime@npm:7.27.6"
  checksum: 10c0/89726be83f356f511dcdb74d3ea4d873a5f0cf0017d4530cb53aa27380c01ca102d573eff8b8b77815e624b1f8c24e7f0311834ad4fb632c90a770fda00bd4c8
  languageName: node
  linkType: hard

"@babel/template@npm:^7.27.2, @babel/template@npm:^7.3.3":
  version: 7.27.2
  resolution: "@babel/template@npm:7.27.2"
  dependencies:
    "@babel/code-frame": "npm:^7.27.1"
    "@babel/parser": "npm:^7.27.2"
    "@babel/types": "npm:^7.27.1"
  checksum: 10c0/ed9e9022651e463cc5f2cc21942f0e74544f1754d231add6348ff1b472985a3b3502041c0be62dc99ed2d12cfae0c51394bf827452b98a2f8769c03b87aadc81
  languageName: node
  linkType: hard

"@babel/traverse@npm:^7.27.1, @babel/traverse@npm:^7.27.3, @babel/traverse@npm:^7.27.4":
  version: 7.27.4
  resolution: "@babel/traverse@npm:7.27.4"
  dependencies:
    "@babel/code-frame": "npm:^7.27.1"
    "@babel/generator": "npm:^7.27.3"
    "@babel/parser": "npm:^7.27.4"
    "@babel/template": "npm:^7.27.2"
    "@babel/types": "npm:^7.27.3"
    debug: "npm:^4.3.1"
    globals: "npm:^11.1.0"
  checksum: 10c0/6de8aa2a0637a6ee6d205bf48b9e923928a02415771fdec60085ed754dcdf605e450bb3315c2552fa51c31a4662275b45d5ae4ad527ce55a7db9acebdbbbb8ed
  languageName: node
  linkType: hard

"@babel/types@npm:^7.0.0, @babel/types@npm:^7.20.7, @babel/types@npm:^7.27.1, @babel/types@npm:^7.27.3, @babel/types@npm:^7.27.6, @babel/types@npm:^7.3.3":
  version: 7.27.6
  resolution: "@babel/types@npm:7.27.6"
  dependencies:
    "@babel/helper-string-parser": "npm:^7.27.1"
    "@babel/helper-validator-identifier": "npm:^7.27.1"
  checksum: 10c0/39d556be114f2a6d874ea25ad39826a9e3a0e98de0233ae6d932f6d09a4b222923a90a7274c635ed61f1ba49bbd345329226678800900ad1c8d11afabd573aaf
  languageName: node
  linkType: hard

"@bcoe/v8-coverage@npm:^0.2.3":
  version: 0.2.3
  resolution: "@bcoe/v8-coverage@npm:0.2.3"
  checksum: 10c0/6b80ae4cb3db53f486da2dc63b6e190a74c8c3cca16bb2733f234a0b6a9382b09b146488ae08e2b22cf00f6c83e20f3e040a2f7894f05c045c946d6a090b1d52
  languageName: node
  linkType: hard

"@bufbuild/protobuf@npm:^2.2.5":
  version: 2.5.2
  resolution: "@bufbuild/protobuf@npm:2.5.2"
  checksum: 10c0/30f0ede04b5318eda502759044329f44af27e0bebd9853d7d9baf5bcb4f4b17f813eb8904d98718d991bd56d33565ed18f8c9c65067626c3d4e55a4e039fe9b6
  languageName: node
  linkType: hard

"@bull-board/api@npm:6.10.1, @bull-board/api@npm:^6.10.1":
  version: 6.10.1
  resolution: "@bull-board/api@npm:6.10.1"
  dependencies:
    redis-info: "npm:^3.1.0"
  peerDependencies:
    "@bull-board/ui": 6.10.1
  checksum: 10c0/cbb25b5d7f2c6ca8cd7ef204fcac26a2ef4103a455573d31db5d1c6f7e46ce671da8ac5ef1ff22b6c464fb00f4ded0bca1b723c181159f694d9df68df4f484b2
  languageName: node
  linkType: hard

"@bull-board/express@npm:^6.10.1":
  version: 6.10.1
  resolution: "@bull-board/express@npm:6.10.1"
  dependencies:
    "@bull-board/api": "npm:6.10.1"
    "@bull-board/ui": "npm:6.10.1"
    ejs: "npm:^3.1.10"
    express: "npm:^4.21.1 || ^5.0.0"
  checksum: 10c0/b0771d64be001142df2915191e03cedf20ce471731b31410d71057bd4812df8f2807a17b09cbc0d7654e24a9fbfc0979339640c8337fbc0ba73eb846b5c0292b
  languageName: node
  linkType: hard

"@bull-board/ui@npm:6.10.1, @bull-board/ui@npm:^6.10.1":
  version: 6.10.1
  resolution: "@bull-board/ui@npm:6.10.1"
  dependencies:
    "@bull-board/api": "npm:6.10.1"
  checksum: 10c0/a759d6da87ee2d50176ce62eac0695348056a2eb3f9284912f9aa8005607dbba1861b3a24baebf2a94b4736027948632664040fd9b2d43d0b70cee4a81ececcc
  languageName: node
  linkType: hard

"@clack/core@npm:0.5.0":
  version: 0.5.0
  resolution: "@clack/core@npm:0.5.0"
  dependencies:
    picocolors: "npm:^1.0.0"
    sisteransi: "npm:^1.0.5"
  checksum: 10c0/ef55dce4b0a4802171b71fe595865a6452c7cf823d162df7fa9afe2ea5a594b9d97e0b8e2880c2a805f2ce1d2f782cb1637d9f8d2ab8b99010af3a20816fae5a
  languageName: node
  linkType: hard

"@clack/prompts@npm:^0.11.0":
  version: 0.11.0
  resolution: "@clack/prompts@npm:0.11.0"
  dependencies:
    "@clack/core": "npm:0.5.0"
    picocolors: "npm:^1.0.0"
    sisteransi: "npm:^1.0.5"
  checksum: 10c0/4c573f2adec3b9109fe861e36312be8ae7cc6e80a5128aa784b9aeafeda5001b23f66c08eca50f4491119b435d9587ec9862956be8c5be472ec3373275003ba8
  languageName: node
  linkType: hard

"@colors/colors@npm:1.5.0":
  version: 1.5.0
  resolution: "@colors/colors@npm:1.5.0"
  checksum: 10c0/eb42729851adca56d19a08e48d5a1e95efd2a32c55ae0323de8119052be0510d4b7a1611f2abcbf28c044a6c11e6b7d38f99fccdad7429300c37a8ea5fb95b44
  languageName: node
  linkType: hard

"@cspotcode/source-map-support@npm:^0.8.0":
  version: 0.8.1
  resolution: "@cspotcode/source-map-support@npm:0.8.1"
  dependencies:
    "@jridgewell/trace-mapping": "npm:0.3.9"
  checksum: 10c0/05c5368c13b662ee4c122c7bfbe5dc0b613416672a829f3e78bc49a357a197e0218d6e74e7c66cfcd04e15a179acab080bd3c69658c9fbefd0e1ccd950a07fc6
  languageName: node
  linkType: hard

"@electric-sql/client@npm:1.0.0-beta.1":
  version: 1.0.0-beta.1
  resolution: "@electric-sql/client@npm:1.0.0-beta.1"
  dependencies:
    "@rollup/rollup-darwin-arm64": "npm:^4.18.1"
  dependenciesMeta:
    "@rollup/rollup-darwin-arm64":
      optional: true
  checksum: 10c0/29f6e2833b10d73ae1365ba10af197991d61a14f2795b6a3033b2cd7aad42c66005c79317c17f7689163573babcef8000a42e975fb11ae1cf90224c9932a5cfd
  languageName: node
  linkType: hard

"@esbuild/aix-ppc64@npm:0.25.5":
  version: 0.25.5
  resolution: "@esbuild/aix-ppc64@npm:0.25.5"
  conditions: os=aix & cpu=ppc64
  languageName: node
  linkType: hard

"@esbuild/android-arm64@npm:0.25.5":
  version: 0.25.5
  resolution: "@esbuild/android-arm64@npm:0.25.5"
  conditions: os=android & cpu=arm64
  languageName: node
  linkType: hard

"@esbuild/android-arm@npm:0.25.5":
  version: 0.25.5
  resolution: "@esbuild/android-arm@npm:0.25.5"
  conditions: os=android & cpu=arm
  languageName: node
  linkType: hard

"@esbuild/android-x64@npm:0.25.5":
  version: 0.25.5
  resolution: "@esbuild/android-x64@npm:0.25.5"
  conditions: os=android & cpu=x64
  languageName: node
  linkType: hard

"@esbuild/darwin-arm64@npm:0.25.5":
  version: 0.25.5
  resolution: "@esbuild/darwin-arm64@npm:0.25.5"
  conditions: os=darwin & cpu=arm64
  languageName: node
  linkType: hard

"@esbuild/darwin-x64@npm:0.25.5":
  version: 0.25.5
  resolution: "@esbuild/darwin-x64@npm:0.25.5"
  conditions: os=darwin & cpu=x64
  languageName: node
  linkType: hard

"@esbuild/freebsd-arm64@npm:0.25.5":
  version: 0.25.5
  resolution: "@esbuild/freebsd-arm64@npm:0.25.5"
  conditions: os=freebsd & cpu=arm64
  languageName: node
  linkType: hard

"@esbuild/freebsd-x64@npm:0.25.5":
  version: 0.25.5
  resolution: "@esbuild/freebsd-x64@npm:0.25.5"
  conditions: os=freebsd & cpu=x64
  languageName: node
  linkType: hard

"@esbuild/linux-arm64@npm:0.25.5":
  version: 0.25.5
  resolution: "@esbuild/linux-arm64@npm:0.25.5"
  conditions: os=linux & cpu=arm64
  languageName: node
  linkType: hard

"@esbuild/linux-arm@npm:0.25.5":
  version: 0.25.5
  resolution: "@esbuild/linux-arm@npm:0.25.5"
  conditions: os=linux & cpu=arm
  languageName: node
  linkType: hard

"@esbuild/linux-ia32@npm:0.25.5":
  version: 0.25.5
  resolution: "@esbuild/linux-ia32@npm:0.25.5"
  conditions: os=linux & cpu=ia32
  languageName: node
  linkType: hard

"@esbuild/linux-loong64@npm:0.25.5":
  version: 0.25.5
  resolution: "@esbuild/linux-loong64@npm:0.25.5"
  conditions: os=linux & cpu=loong64
  languageName: node
  linkType: hard

"@esbuild/linux-mips64el@npm:0.25.5":
  version: 0.25.5
  resolution: "@esbuild/linux-mips64el@npm:0.25.5"
  conditions: os=linux & cpu=mips64el
  languageName: node
  linkType: hard

"@esbuild/linux-ppc64@npm:0.25.5":
  version: 0.25.5
  resolution: "@esbuild/linux-ppc64@npm:0.25.5"
  conditions: os=linux & cpu=ppc64
  languageName: node
  linkType: hard

"@esbuild/linux-riscv64@npm:0.25.5":
  version: 0.25.5
  resolution: "@esbuild/linux-riscv64@npm:0.25.5"
  conditions: os=linux & cpu=riscv64
  languageName: node
  linkType: hard

"@esbuild/linux-s390x@npm:0.25.5":
  version: 0.25.5
  resolution: "@esbuild/linux-s390x@npm:0.25.5"
  conditions: os=linux & cpu=s390x
  languageName: node
  linkType: hard

"@esbuild/linux-x64@npm:0.25.5":
  version: 0.25.5
  resolution: "@esbuild/linux-x64@npm:0.25.5"
  conditions: os=linux & cpu=x64
  languageName: node
  linkType: hard

"@esbuild/netbsd-arm64@npm:0.25.5":
  version: 0.25.5
  resolution: "@esbuild/netbsd-arm64@npm:0.25.5"
  conditions: os=netbsd & cpu=arm64
  languageName: node
  linkType: hard

"@esbuild/netbsd-x64@npm:0.25.5":
  version: 0.25.5
  resolution: "@esbuild/netbsd-x64@npm:0.25.5"
  conditions: os=netbsd & cpu=x64
  languageName: node
  linkType: hard

"@esbuild/openbsd-arm64@npm:0.25.5":
  version: 0.25.5
  resolution: "@esbuild/openbsd-arm64@npm:0.25.5"
  conditions: os=openbsd & cpu=arm64
  languageName: node
  linkType: hard

"@esbuild/openbsd-x64@npm:0.25.5":
  version: 0.25.5
  resolution: "@esbuild/openbsd-x64@npm:0.25.5"
  conditions: os=openbsd & cpu=x64
  languageName: node
  linkType: hard

"@esbuild/sunos-x64@npm:0.25.5":
  version: 0.25.5
  resolution: "@esbuild/sunos-x64@npm:0.25.5"
  conditions: os=sunos & cpu=x64
  languageName: node
  linkType: hard

"@esbuild/win32-arm64@npm:0.25.5":
  version: 0.25.5
  resolution: "@esbuild/win32-arm64@npm:0.25.5"
  conditions: os=win32 & cpu=arm64
  languageName: node
  linkType: hard

"@esbuild/win32-ia32@npm:0.25.5":
  version: 0.25.5
  resolution: "@esbuild/win32-ia32@npm:0.25.5"
  conditions: os=win32 & cpu=ia32
  languageName: node
  linkType: hard

"@esbuild/win32-x64@npm:0.25.5":
  version: 0.25.5
  resolution: "@esbuild/win32-x64@npm:0.25.5"
  conditions: os=win32 & cpu=x64
  languageName: node
  linkType: hard

"@google-cloud/precise-date@npm:^4.0.0":
  version: 4.0.0
  resolution: "@google-cloud/precise-date@npm:4.0.0"
  checksum: 10c0/8788bec6bb5db3fcc9cf72f346dc7af35d0ad1c9457d40f800e580dc58631568589b6795b48bef88b958b718c81cd326b0ccfe9d0ef9e7d7e85f45c1375e9c14
  languageName: node
  linkType: hard

"@graphql-typed-document-node/core@npm:^3.2.0":
  version: 3.2.0
  resolution: "@graphql-typed-document-node/core@npm:3.2.0"
  peerDependencies:
    graphql: ^0.8.0 || ^0.9.0 || ^0.10.0 || ^0.11.0 || ^0.12.0 || ^0.13.0 || ^14.0.0 || ^15.0.0 || ^16.0.0 || ^17.0.0
  checksum: 10c0/94e9d75c1f178bbae8d874f5a9361708a3350c8def7eaeb6920f2c820e82403b7d4f55b3735856d68e145e86c85cbfe2adc444fdc25519cd51f108697e99346c
  languageName: node
  linkType: hard

"@grpc/grpc-js@npm:^1.13.1, @grpc/grpc-js@npm:^1.7.1":
  version: 1.13.4
  resolution: "@grpc/grpc-js@npm:1.13.4"
  dependencies:
    "@grpc/proto-loader": "npm:^0.7.13"
    "@js-sdsl/ordered-map": "npm:^4.4.2"
  checksum: 10c0/ecdb99efbe540d8b261ca53e4be224fb4683fb22c6ab1b575d2f4ca34471fc7f221b58f718001a6d157c54237cc482514766233968f5de50e358f061600a885b
  languageName: node
  linkType: hard

"@grpc/proto-loader@npm:^0.7.13":
  version: 0.7.15
  resolution: "@grpc/proto-loader@npm:0.7.15"
  dependencies:
    lodash.camelcase: "npm:^4.3.0"
    long: "npm:^5.0.0"
    protobufjs: "npm:^7.2.5"
    yargs: "npm:^17.7.2"
  bin:
    proto-loader-gen-types: build/bin/proto-loader-gen-types.js
  checksum: 10c0/514a134a724b56d73d0a202b7e02c84479da21e364547bacb2f4995ebc0d52412a1a21653add9f004ebd146c1e6eb4bcb0b8846fdfe1bfa8a98ed8f3d203da4a
  languageName: node
  linkType: hard

"@ioredis/commands@npm:^1.1.1":
  version: 1.2.0
  resolution: "@ioredis/commands@npm:1.2.0"
  checksum: 10c0/a5d3c29dd84d8a28b7c67a441ac1715cbd7337a7b88649c0f17c345d89aa218578d2b360760017c48149ef8a70f44b051af9ac0921a0622c2b479614c4f65b36
  languageName: node
  linkType: hard

"@isaacs/cliui@npm:^8.0.2":
  version: 8.0.2
  resolution: "@isaacs/cliui@npm:8.0.2"
  dependencies:
    string-width: "npm:^5.1.2"
    string-width-cjs: "npm:string-width@^4.2.0"
    strip-ansi: "npm:^7.0.1"
    strip-ansi-cjs: "npm:strip-ansi@^6.0.1"
    wrap-ansi: "npm:^8.1.0"
    wrap-ansi-cjs: "npm:wrap-ansi@^7.0.0"
  checksum: 10c0/b1bf42535d49f11dc137f18d5e4e63a28c5569de438a221c369483731e9dac9fb797af554e8bf02b6192d1e5eba6e6402cf93900c3d0ac86391d00d04876789e
  languageName: node
  linkType: hard

"@isaacs/fs-minipass@npm:^4.0.0":
  version: 4.0.1
  resolution: "@isaacs/fs-minipass@npm:4.0.1"
  dependencies:
    minipass: "npm:^7.0.4"
  checksum: 10c0/c25b6dc1598790d5b55c0947a9b7d111cfa92594db5296c3b907e2f533c033666f692a3939eadac17b1c7c40d362d0b0635dc874cbfe3e70db7c2b07cc97a5d2
  languageName: node
  linkType: hard

"@istanbuljs/load-nyc-config@npm:^1.0.0":
  version: 1.1.0
  resolution: "@istanbuljs/load-nyc-config@npm:1.1.0"
  dependencies:
    camelcase: "npm:^5.3.1"
    find-up: "npm:^4.1.0"
    get-package-type: "npm:^0.1.0"
    js-yaml: "npm:^3.13.1"
    resolve-from: "npm:^5.0.0"
  checksum: 10c0/dd2a8b094887da5a1a2339543a4933d06db2e63cbbc2e288eb6431bd832065df0c099d091b6a67436e71b7d6bf85f01ce7c15f9253b4cbebcc3b9a496165ba42
  languageName: node
  linkType: hard

"@istanbuljs/schema@npm:^0.1.2, @istanbuljs/schema@npm:^0.1.3":
  version: 0.1.3
  resolution: "@istanbuljs/schema@npm:0.1.3"
  checksum: 10c0/61c5286771676c9ca3eb2bd8a7310a9c063fb6e0e9712225c8471c582d157392c88f5353581c8c9adbe0dff98892317d2fdfc56c3499aa42e0194405206a963a
  languageName: node
  linkType: hard

"@jest/console@npm:^29.7.0":
  version: 29.7.0
  resolution: "@jest/console@npm:29.7.0"
  dependencies:
    "@jest/types": "npm:^29.6.3"
    "@types/node": "npm:*"
    chalk: "npm:^4.0.0"
    jest-message-util: "npm:^29.7.0"
    jest-util: "npm:^29.7.0"
    slash: "npm:^3.0.0"
  checksum: 10c0/7be408781d0a6f657e969cbec13b540c329671819c2f57acfad0dae9dbfe2c9be859f38fe99b35dba9ff1536937dc6ddc69fdcd2794812fa3c647a1619797f6c
  languageName: node
  linkType: hard

"@jest/core@npm:^29.7.0":
  version: 29.7.0
  resolution: "@jest/core@npm:29.7.0"
  dependencies:
    "@jest/console": "npm:^29.7.0"
    "@jest/reporters": "npm:^29.7.0"
    "@jest/test-result": "npm:^29.7.0"
    "@jest/transform": "npm:^29.7.0"
    "@jest/types": "npm:^29.6.3"
    "@types/node": "npm:*"
    ansi-escapes: "npm:^4.2.1"
    chalk: "npm:^4.0.0"
    ci-info: "npm:^3.2.0"
    exit: "npm:^0.1.2"
    graceful-fs: "npm:^4.2.9"
    jest-changed-files: "npm:^29.7.0"
    jest-config: "npm:^29.7.0"
    jest-haste-map: "npm:^29.7.0"
    jest-message-util: "npm:^29.7.0"
    jest-regex-util: "npm:^29.6.3"
    jest-resolve: "npm:^29.7.0"
    jest-resolve-dependencies: "npm:^29.7.0"
    jest-runner: "npm:^29.7.0"
    jest-runtime: "npm:^29.7.0"
    jest-snapshot: "npm:^29.7.0"
    jest-util: "npm:^29.7.0"
    jest-validate: "npm:^29.7.0"
    jest-watcher: "npm:^29.7.0"
    micromatch: "npm:^4.0.4"
    pretty-format: "npm:^29.7.0"
    slash: "npm:^3.0.0"
    strip-ansi: "npm:^6.0.0"
  peerDependencies:
    node-notifier: ^8.0.1 || ^9.0.0 || ^10.0.0
  peerDependenciesMeta:
    node-notifier:
      optional: true
  checksum: 10c0/934f7bf73190f029ac0f96662c85cd276ec460d407baf6b0dbaec2872e157db4d55a7ee0b1c43b18874602f662b37cb973dda469a4e6d88b4e4845b521adeeb2
  languageName: node
  linkType: hard

"@jest/environment@npm:^29.7.0":
  version: 29.7.0
  resolution: "@jest/environment@npm:29.7.0"
  dependencies:
    "@jest/fake-timers": "npm:^29.7.0"
    "@jest/types": "npm:^29.6.3"
    "@types/node": "npm:*"
    jest-mock: "npm:^29.7.0"
  checksum: 10c0/c7b1b40c618f8baf4d00609022d2afa086d9c6acc706f303a70bb4b67275868f620ad2e1a9efc5edd418906157337cce50589a627a6400bbdf117d351b91ef86
  languageName: node
  linkType: hard

"@jest/expect-utils@npm:^29.7.0":
  version: 29.7.0
  resolution: "@jest/expect-utils@npm:29.7.0"
  dependencies:
    jest-get-type: "npm:^29.6.3"
  checksum: 10c0/60b79d23a5358dc50d9510d726443316253ecda3a7fb8072e1526b3e0d3b14f066ee112db95699b7a43ad3f0b61b750c72e28a5a1cac361d7a2bb34747fa938a
  languageName: node
  linkType: hard

"@jest/expect@npm:^29.7.0":
  version: 29.7.0
  resolution: "@jest/expect@npm:29.7.0"
  dependencies:
    expect: "npm:^29.7.0"
    jest-snapshot: "npm:^29.7.0"
  checksum: 10c0/b41f193fb697d3ced134349250aed6ccea075e48c4f803159db102b826a4e473397c68c31118259868fd69a5cba70e97e1c26d2c2ff716ca39dc73a2ccec037e
  languageName: node
  linkType: hard

"@jest/fake-timers@npm:^29.7.0":
  version: 29.7.0
  resolution: "@jest/fake-timers@npm:29.7.0"
  dependencies:
    "@jest/types": "npm:^29.6.3"
    "@sinonjs/fake-timers": "npm:^10.0.2"
    "@types/node": "npm:*"
    jest-message-util: "npm:^29.7.0"
    jest-mock: "npm:^29.7.0"
    jest-util: "npm:^29.7.0"
  checksum: 10c0/cf0a8bcda801b28dc2e2b2ba36302200ee8104a45ad7a21e6c234148932f826cb3bc57c8df3b7b815aeea0861d7b6ca6f0d4778f93b9219398ef28749e03595c
  languageName: node
  linkType: hard

"@jest/globals@npm:^29.7.0":
  version: 29.7.0
  resolution: "@jest/globals@npm:29.7.0"
  dependencies:
    "@jest/environment": "npm:^29.7.0"
    "@jest/expect": "npm:^29.7.0"
    "@jest/types": "npm:^29.6.3"
    jest-mock: "npm:^29.7.0"
  checksum: 10c0/a385c99396878fe6e4460c43bd7bb0a5cc52befb462cc6e7f2a3810f9e7bcce7cdeb51908fd530391ee452dc856c98baa2c5f5fa8a5b30b071d31ef7f6955cea
  languageName: node
  linkType: hard

"@jest/reporters@npm:^29.7.0":
  version: 29.7.0
  resolution: "@jest/reporters@npm:29.7.0"
  dependencies:
    "@bcoe/v8-coverage": "npm:^0.2.3"
    "@jest/console": "npm:^29.7.0"
    "@jest/test-result": "npm:^29.7.0"
    "@jest/transform": "npm:^29.7.0"
    "@jest/types": "npm:^29.6.3"
    "@jridgewell/trace-mapping": "npm:^0.3.18"
    "@types/node": "npm:*"
    chalk: "npm:^4.0.0"
    collect-v8-coverage: "npm:^1.0.0"
    exit: "npm:^0.1.2"
    glob: "npm:^7.1.3"
    graceful-fs: "npm:^4.2.9"
    istanbul-lib-coverage: "npm:^3.0.0"
    istanbul-lib-instrument: "npm:^6.0.0"
    istanbul-lib-report: "npm:^3.0.0"
    istanbul-lib-source-maps: "npm:^4.0.0"
    istanbul-reports: "npm:^3.1.3"
    jest-message-util: "npm:^29.7.0"
    jest-util: "npm:^29.7.0"
    jest-worker: "npm:^29.7.0"
    slash: "npm:^3.0.0"
    string-length: "npm:^4.0.1"
    strip-ansi: "npm:^6.0.0"
    v8-to-istanbul: "npm:^9.0.1"
  peerDependencies:
    node-notifier: ^8.0.1 || ^9.0.0 || ^10.0.0
  peerDependenciesMeta:
    node-notifier:
      optional: true
  checksum: 10c0/a754402a799541c6e5aff2c8160562525e2a47e7d568f01ebfc4da66522de39cbb809bbb0a841c7052e4270d79214e70aec3c169e4eae42a03bc1a8a20cb9fa2
  languageName: node
  linkType: hard

"@jest/schemas@npm:^29.6.3":
  version: 29.6.3
  resolution: "@jest/schemas@npm:29.6.3"
  dependencies:
    "@sinclair/typebox": "npm:^0.27.8"
  checksum: 10c0/b329e89cd5f20b9278ae1233df74016ebf7b385e0d14b9f4c1ad18d096c4c19d1e687aa113a9c976b16ec07f021ae53dea811fb8c1248a50ac34fbe009fdf6be
  languageName: node
  linkType: hard

"@jest/source-map@npm:^29.6.3":
  version: 29.6.3
  resolution: "@jest/source-map@npm:29.6.3"
  dependencies:
    "@jridgewell/trace-mapping": "npm:^0.3.18"
    callsites: "npm:^3.0.0"
    graceful-fs: "npm:^4.2.9"
  checksum: 10c0/a2f177081830a2e8ad3f2e29e20b63bd40bade294880b595acf2fc09ec74b6a9dd98f126a2baa2bf4941acd89b13a4ade5351b3885c224107083a0059b60a219
  languageName: node
  linkType: hard

"@jest/test-result@npm:^29.7.0":
  version: 29.7.0
  resolution: "@jest/test-result@npm:29.7.0"
  dependencies:
    "@jest/console": "npm:^29.7.0"
    "@jest/types": "npm:^29.6.3"
    "@types/istanbul-lib-coverage": "npm:^2.0.0"
    collect-v8-coverage: "npm:^1.0.0"
  checksum: 10c0/7de54090e54a674ca173470b55dc1afdee994f2d70d185c80236003efd3fa2b753fff51ffcdda8e2890244c411fd2267529d42c4a50a8303755041ee493e6a04
  languageName: node
  linkType: hard

"@jest/test-sequencer@npm:^29.7.0":
  version: 29.7.0
  resolution: "@jest/test-sequencer@npm:29.7.0"
  dependencies:
    "@jest/test-result": "npm:^29.7.0"
    graceful-fs: "npm:^4.2.9"
    jest-haste-map: "npm:^29.7.0"
    slash: "npm:^3.0.0"
  checksum: 10c0/593a8c4272797bb5628984486080cbf57aed09c7cfdc0a634e8c06c38c6bef329c46c0016e84555ee55d1cd1f381518cf1890990ff845524c1123720c8c1481b
  languageName: node
  linkType: hard

"@jest/transform@npm:^29.7.0":
  version: 29.7.0
  resolution: "@jest/transform@npm:29.7.0"
  dependencies:
    "@babel/core": "npm:^7.11.6"
    "@jest/types": "npm:^29.6.3"
    "@jridgewell/trace-mapping": "npm:^0.3.18"
    babel-plugin-istanbul: "npm:^6.1.1"
    chalk: "npm:^4.0.0"
    convert-source-map: "npm:^2.0.0"
    fast-json-stable-stringify: "npm:^2.1.0"
    graceful-fs: "npm:^4.2.9"
    jest-haste-map: "npm:^29.7.0"
    jest-regex-util: "npm:^29.6.3"
    jest-util: "npm:^29.7.0"
    micromatch: "npm:^4.0.4"
    pirates: "npm:^4.0.4"
    slash: "npm:^3.0.0"
    write-file-atomic: "npm:^4.0.2"
  checksum: 10c0/7f4a7f73dcf45dfdf280c7aa283cbac7b6e5a904813c3a93ead7e55873761fc20d5c4f0191d2019004fac6f55f061c82eb3249c2901164ad80e362e7a7ede5a6
  languageName: node
  linkType: hard

"@jest/types@npm:^29.6.3":
  version: 29.6.3
  resolution: "@jest/types@npm:29.6.3"
  dependencies:
    "@jest/schemas": "npm:^29.6.3"
    "@types/istanbul-lib-coverage": "npm:^2.0.0"
    "@types/istanbul-reports": "npm:^3.0.0"
    "@types/node": "npm:*"
    "@types/yargs": "npm:^17.0.8"
    chalk: "npm:^4.0.0"
  checksum: 10c0/ea4e493dd3fb47933b8ccab201ae573dcc451f951dc44ed2a86123cd8541b82aa9d2b1031caf9b1080d6673c517e2dcc25a44b2dc4f3fbc37bfc965d444888c0
  languageName: node
  linkType: hard

"@jridgewell/gen-mapping@npm:^0.3.5":
  version: 0.3.8
  resolution: "@jridgewell/gen-mapping@npm:0.3.8"
  dependencies:
    "@jridgewell/set-array": "npm:^1.2.1"
    "@jridgewell/sourcemap-codec": "npm:^1.4.10"
    "@jridgewell/trace-mapping": "npm:^0.3.24"
  checksum: 10c0/c668feaf86c501d7c804904a61c23c67447b2137b813b9ce03eca82cb9d65ac7006d766c218685d76e3d72828279b6ee26c347aa1119dab23fbaf36aed51585a
  languageName: node
  linkType: hard

"@jridgewell/resolve-uri@npm:^3.0.3, @jridgewell/resolve-uri@npm:^3.1.0":
  version: 3.1.2
  resolution: "@jridgewell/resolve-uri@npm:3.1.2"
  checksum: 10c0/d502e6fb516b35032331406d4e962c21fe77cdf1cbdb49c6142bcbd9e30507094b18972778a6e27cbad756209cfe34b1a27729e6fa08a2eb92b33943f680cf1e
  languageName: node
  linkType: hard

"@jridgewell/set-array@npm:^1.2.1":
  version: 1.2.1
  resolution: "@jridgewell/set-array@npm:1.2.1"
  checksum: 10c0/2a5aa7b4b5c3464c895c802d8ae3f3d2b92fcbe84ad12f8d0bfbb1f5ad006717e7577ee1fd2eac00c088abe486c7adb27976f45d2941ff6b0b92b2c3302c60f4
  languageName: node
  linkType: hard

"@jridgewell/sourcemap-codec@npm:^1.4.10, @jridgewell/sourcemap-codec@npm:^1.4.14, @jridgewell/sourcemap-codec@npm:^1.5.0":
  version: 1.5.0
  resolution: "@jridgewell/sourcemap-codec@npm:1.5.0"
  checksum: 10c0/2eb864f276eb1096c3c11da3e9bb518f6d9fc0023c78344cdc037abadc725172c70314bdb360f2d4b7bffec7f5d657ce006816bc5d4ecb35e61b66132db00c18
  languageName: node
  linkType: hard

"@jridgewell/trace-mapping@npm:0.3.9":
  version: 0.3.9
  resolution: "@jridgewell/trace-mapping@npm:0.3.9"
  dependencies:
    "@jridgewell/resolve-uri": "npm:^3.0.3"
    "@jridgewell/sourcemap-codec": "npm:^1.4.10"
  checksum: 10c0/fa425b606d7c7ee5bfa6a31a7b050dd5814b4082f318e0e4190f991902181b4330f43f4805db1dd4f2433fd0ed9cc7a7b9c2683f1deeab1df1b0a98b1e24055b
  languageName: node
  linkType: hard

"@jridgewell/trace-mapping@npm:^0.3.12, @jridgewell/trace-mapping@npm:^0.3.18, @jridgewell/trace-mapping@npm:^0.3.24, @jridgewell/trace-mapping@npm:^0.3.25":
  version: 0.3.25
  resolution: "@jridgewell/trace-mapping@npm:0.3.25"
  dependencies:
    "@jridgewell/resolve-uri": "npm:^3.1.0"
    "@jridgewell/sourcemap-codec": "npm:^1.4.14"
  checksum: 10c0/3d1ce6ebc69df9682a5a8896b414c6537e428a1d68b02fcc8363b04284a8ca0df04d0ee3013132252ab14f2527bc13bea6526a912ecb5658f0e39fd2860b4df4
  languageName: node
  linkType: hard

"@js-sdsl/ordered-map@npm:^4.4.2":
  version: 4.4.2
  resolution: "@js-sdsl/ordered-map@npm:4.4.2"
  checksum: 10c0/cc7e15dc4acf6d9ef663757279600bab70533d847dcc1ab01332e9e680bd30b77cdf9ad885cc774276f51d98b05a013571c940e5b360985af5eb798dc1a2ee2b
  languageName: node
  linkType: hard

"@jsdevtools/ono@npm:^7.1.3":
  version: 7.1.3
  resolution: "@jsdevtools/ono@npm:7.1.3"
  checksum: 10c0/a9f7e3e8e3bc315a34959934a5e2f874c423cf4eae64377d3fc9de0400ed9f36cb5fd5ebce3300d2e8f4085f557c4a8b591427a583729a87841fda46e6c216b9
  languageName: node
  linkType: hard

"@jsonhero/path@npm:^1.0.21":
  version: 1.0.21
  resolution: "@jsonhero/path@npm:1.0.21"
  checksum: 10c0/eb71bbb706938f1528f6d5e3877eeb2891cd36e5a5eeafed29a8cfeeb988b2701c752cc34b6369e03f3f8297b8693bf58dd5f69261165e2e2901f1c5ad746937
  languageName: node
  linkType: hard

"@lukeed/csprng@npm:^1.1.0":
  version: 1.1.0
  resolution: "@lukeed/csprng@npm:1.1.0"
  checksum: 10c0/5d6dcf478af732972083ab2889c294b57f1028fa13c2c240d7a4aaa079c2c75df7ef0dcbdda5419147fc6704b4adf96b2de92f1a9a72ac21c6350c4014fffe6c
  languageName: node
  linkType: hard

"@lukeed/uuid@npm:^2.0.1":
  version: 2.0.1
  resolution: "@lukeed/uuid@npm:2.0.1"
  dependencies:
    "@lukeed/csprng": "npm:^1.1.0"
  checksum: 10c0/f9cc0385021f352f444d96dd101afd2a0efd3b2e85a61ac67deb8220409f75a6a426ed6525d297d97746f7931e3079ac6218777551a7c82686de7d292220cb1f
  languageName: node
  linkType: hard

"@mastra/client-js@npm:^0.10.5":
  version: 0.10.5
  resolution: "@mastra/client-js@npm:0.10.5"
  dependencies:
    "@ag-ui/client": "npm:^0.0.27"
    "@ai-sdk/ui-utils": "npm:^1.2.11"
    "@mastra/core": "npm:0.10.6"
    json-schema: "npm:^0.4.0"
    rxjs: "npm:7.8.1"
    zod: "npm:^3.25.57"
    zod-to-json-schema: "npm:^3.24.5"
  peerDependencies:
    zod: ^3.0.0
  checksum: 10c0/2dfcec93125471daaa2a7381b3b6bef814c453bc521582020a4330f922dc294618f73cc88ba5732da4137d0e9e81203e1f0cf19fe8f39547a0de190c0c736365
  languageName: node
  linkType: hard

"@mastra/core@npm:0.10.6":
  version: 0.10.6
  resolution: "@mastra/core@npm:0.10.6"
  dependencies:
    "@ai-sdk/provider": "npm:^1.1.3"
    "@ai-sdk/provider-utils": "npm:^2.2.8"
    "@ai-sdk/ui-utils": "npm:^1.2.11"
    "@mastra/schema-compat": "npm:0.10.2"
    "@opentelemetry/api": "npm:^1.9.0"
    "@opentelemetry/auto-instrumentations-node": "npm:^0.59.0"
    "@opentelemetry/core": "npm:^2.0.1"
    "@opentelemetry/exporter-trace-otlp-grpc": "npm:^0.201.1"
    "@opentelemetry/exporter-trace-otlp-http": "npm:^0.201.1"
    "@opentelemetry/otlp-exporter-base": "npm:^0.201.1"
    "@opentelemetry/otlp-transformer": "npm:^0.201.1"
    "@opentelemetry/resources": "npm:^2.0.1"
    "@opentelemetry/sdk-metrics": "npm:^2.0.1"
    "@opentelemetry/sdk-node": "npm:^0.201.1"
    "@opentelemetry/sdk-trace-base": "npm:^2.0.1"
    "@opentelemetry/sdk-trace-node": "npm:^2.0.1"
    "@opentelemetry/semantic-conventions": "npm:^1.34.0"
    "@sindresorhus/slugify": "npm:^2.2.1"
    ai: "npm:^4.3.16"
    cohere-ai: "npm:^7.17.1"
    date-fns: "npm:^3.6.0"
    dotenv: "npm:^16.5.0"
    hono: "npm:^4.7.11"
    hono-openapi: "npm:^0.4.8"
    json-schema: "npm:^0.4.0"
    json-schema-to-zod: "npm:^2.6.1"
    pino: "npm:^9.7.0"
    pino-pretty: "npm:^13.0.0"
    radash: "npm:^12.1.0"
    sift: "npm:^17.1.3"
    xstate: "npm:^5.19.4"
  peerDependencies:
    zod: ^3.0.0
  checksum: 10c0/66474a1fa7cec6a10e8fc67913b139a90f0d00da0871e4cdcaecbc13c9fd6d2fef6188945a32e89a003e21574a1d8e35e3f277f1dc22bdfe6da308f12feabbde
  languageName: node
  linkType: hard

"@mastra/core@npm:^0.10.8":
  version: 0.10.8
  resolution: "@mastra/core@npm:0.10.8"
  dependencies:
    "@ai-sdk/provider": "npm:^1.1.3"
    "@ai-sdk/provider-utils": "npm:^2.2.8"
    "@ai-sdk/ui-utils": "npm:^1.2.11"
    "@mastra/schema-compat": "npm:0.10.3"
    "@opentelemetry/api": "npm:^1.9.0"
    "@opentelemetry/auto-instrumentations-node": "npm:^0.59.0"
    "@opentelemetry/core": "npm:^2.0.1"
    "@opentelemetry/exporter-trace-otlp-grpc": "npm:^0.201.1"
    "@opentelemetry/exporter-trace-otlp-http": "npm:^0.201.1"
    "@opentelemetry/otlp-exporter-base": "npm:^0.201.1"
    "@opentelemetry/otlp-transformer": "npm:^0.201.1"
    "@opentelemetry/resources": "npm:^2.0.1"
    "@opentelemetry/sdk-metrics": "npm:^2.0.1"
    "@opentelemetry/sdk-node": "npm:^0.201.1"
    "@opentelemetry/sdk-trace-base": "npm:^2.0.1"
    "@opentelemetry/sdk-trace-node": "npm:^2.0.1"
    "@opentelemetry/semantic-conventions": "npm:^1.34.0"
    "@sindresorhus/slugify": "npm:^2.2.1"
    ai: "npm:^4.3.16"
    cohere-ai: "npm:^7.17.1"
    date-fns: "npm:^3.6.0"
    dotenv: "npm:^16.5.0"
    hono: "npm:^4.7.11"
    hono-openapi: "npm:^0.4.8"
    json-schema: "npm:^0.4.0"
    json-schema-to-zod: "npm:^2.6.1"
    pino: "npm:^9.7.0"
    pino-pretty: "npm:^13.0.0"
    radash: "npm:^12.1.0"
    sift: "npm:^17.1.3"
    xstate: "npm:^5.19.4"
    zod-to-json-schema: "npm:^3.24.5"
  peerDependencies:
    zod: ^3.0.0
  checksum: 10c0/7cc47e9c24165dfc3232b94ff8fae3a9a59a55c53fb574b5ebe5c3f7631a2c7db1e6814574963d653c67bd3719cf73f16554561f34a8bd23a6031a06c144cd64
  languageName: node
  linkType: hard

"@mastra/deployer@npm:^0.10.6":
  version: 0.10.6
  resolution: "@mastra/deployer@npm:0.10.6"
  dependencies:
    "@babel/core": "npm:^7.27.4"
    "@babel/helper-module-imports": "npm:^7.27.1"
    "@mastra/server": "npm:^0.10.6"
    "@neon-rs/load": "npm:^0.1.82"
    "@rollup/plugin-alias": "npm:^5.1.1"
    "@rollup/plugin-commonjs": "npm:^28.0.5"
    "@rollup/plugin-json": "npm:^6.1.0"
    "@rollup/plugin-node-resolve": "npm:^16.0.1"
    "@rollup/plugin-virtual": "npm:^3.0.2"
    "@sindresorhus/slugify": "npm:^2.2.1"
    builtins: "npm:^5.1.0"
    detect-libc: "npm:^2.0.4"
    dotenv: "npm:^16.5.0"
    esbuild: "npm:^0.25.5"
    find-workspaces: "npm:^0.3.1"
    fs-extra: "npm:^11.3.0"
    globby: "npm:^14.1.0"
    hono: "npm:^4.7.11"
    resolve-from: "npm:^5.0.0"
    rollup: "npm:^4.43.0"
    rollup-plugin-esbuild: "npm:^6.2.1"
    rollup-plugin-node-externals: "npm:^8.0.0"
    typescript-paths: "npm:^1.5.1"
    zod: "npm:^3.25.57"
  peerDependencies:
    "@mastra/core": ^0.10.2-alpha.0
  checksum: 10c0/cfdc3a45437a3bb19a61e5f3fde1a1b269ce5d9ec88ec34f0f048567e40f4ab2e200ed82e483bdff87ae9e253ccfcda1318d997a2cabcd25d1a2eae1e5f2c3db
  languageName: node
  linkType: hard

"@mastra/loggers@npm:^0.10.2":
  version: 0.10.2
  resolution: "@mastra/loggers@npm:0.10.2"
  dependencies:
    pino: "npm:^9.7.0"
    pino-pretty: "npm:^13.0.0"
  peerDependencies:
    "@mastra/core": ">=0.10.4-0 <0.11.0"
  checksum: 10c0/ce5edc5365937872c2d0e93be264c5b74a7574d26f116db38c707df1b8537f34046864c14d6742208f1217a461200d298e7e369c2e4be5046184d96922b06aec
  languageName: node
  linkType: hard

"@mastra/mcp@npm:^0.10.4":
  version: 0.10.4
  resolution: "@mastra/mcp@npm:0.10.4"
  dependencies:
    "@modelcontextprotocol/sdk": "npm:^1.12.1"
    date-fns: "npm:^4.1.0"
    exit-hook: "npm:^4.0.0"
    fast-deep-equal: "npm:^3.1.3"
    hono: "npm:^4.7.11"
    uuid: "npm:^11.1.0"
    zod-from-json-schema: "npm:^0.0.5"
  peerDependencies:
    "@mastra/core": ^0.10.2-alpha.0
    zod: ^3.0.0
  checksum: 10c0/986b7819e1f93c1f724a25f1c886a6356da040880738ab687027acb8c5a62dbc18b0bfd51771f8c1dcd85f5621517b0ff46a5a5904c0dad138e713a2fdcd7d81
  languageName: node
  linkType: hard

"@mastra/memory@npm:^0.11.0":
  version: 0.11.0
  resolution: "@mastra/memory@npm:0.11.0"
  dependencies:
    "@upstash/redis": "npm:^1.35.0"
    ai: "npm:^4.3.16"
    js-tiktoken: "npm:^1.0.20"
    pg: "npm:^8.16.0"
    pg-pool: "npm:^3.10.0"
    postgres: "npm:^3.4.7"
    redis: "npm:^4.7.1"
    xxhash-wasm: "npm:^1.1.0"
    zod: "npm:^3.25.67"
    zod-to-json-schema: "npm:^3.24.5"
  peerDependencies:
    "@mastra/core": ">=0.10.7-0 <0.11.0-0"
  checksum: 10c0/3cbf7a8753611582335995daf5eaa71e1d5390ac061075ecd2ebaa3be8c46c42f7cad503c0bcb8f33c340aaf0e88328486dd5151a4ed1c7e579e00871d068401
  languageName: node
  linkType: hard

"@mastra/pg@npm:^0.12.0":
  version: 0.12.0
  resolution: "@mastra/pg@npm:0.12.0"
  dependencies:
    async-mutex: "npm:^0.5.0"
    pg: "npm:^8.16.0"
    pg-promise: "npm:^11.14.0"
    xxhash-wasm: "npm:^1.1.0"
  peerDependencies:
    "@mastra/core": ">=0.10.7-0 <0.11.0-0"
  checksum: 10c0/121b8767533bc1e0e6293a1f452233e2d55be0f25a7ec88006735d20b5dc7736ea37986900013c18c55614584afbf4a425fc43be0975e17b96cfec5bee27efb5
  languageName: node
  linkType: hard

"@mastra/schema-compat@npm:0.10.2":
  version: 0.10.2
  resolution: "@mastra/schema-compat@npm:0.10.2"
  dependencies:
    json-schema: "npm:^0.4.0"
    zod-from-json-schema: "npm:^0.0.5"
    zod-to-json-schema: "npm:^3.24.5"
  peerDependencies:
    ai: ^4.0.0
    zod: ^3.0.0
  checksum: 10c0/6020179e74f9901260807e0b12f838689644189065dfa664249489e759537b421e0501b4d9f71496cc0424a51a94208b314b59e1202de4b4f8a76daf23d269e0
  languageName: node
  linkType: hard

"@mastra/schema-compat@npm:0.10.3":
  version: 0.10.3
  resolution: "@mastra/schema-compat@npm:0.10.3"
  dependencies:
    json-schema: "npm:^0.4.0"
    zod-from-json-schema: "npm:^0.0.5"
    zod-to-json-schema: "npm:^3.24.5"
  peerDependencies:
    ai: ^4.0.0
    zod: ^3.0.0
  checksum: 10c0/fdc8d8cec7f1a9387daec7d9ffd52c109b1bafa5c9fbb364da3c631a452b47fff01bc449aa2661baeb11d0d6f997a9ec0acae7869efd90dbdfe403d39cbc90fc
  languageName: node
  linkType: hard

"@mastra/server@npm:^0.10.6":
  version: 0.10.6
  resolution: "@mastra/server@npm:0.10.6"
  peerDependencies:
    "@mastra/core": ^0.10.2-alpha.0
    zod: ^3.0.0
  checksum: 10c0/ea93ab173c10004824f6572c9d85c14ab84463105a20ba30032d7d4aab6ecdb967682f20298fd626df29a2122f1024e5f7c5a61fc4ab5a15d4d51e222632524b
  languageName: node
  linkType: hard

"@modelcontextprotocol/sdk@npm:^1.12.1":
  version: 1.13.0
  resolution: "@modelcontextprotocol/sdk@npm:1.13.0"
  dependencies:
    ajv: "npm:^6.12.6"
    content-type: "npm:^1.0.5"
    cors: "npm:^2.8.5"
    cross-spawn: "npm:^7.0.5"
    eventsource: "npm:^3.0.2"
    express: "npm:^5.0.1"
    express-rate-limit: "npm:^7.5.0"
    pkce-challenge: "npm:^5.0.0"
    raw-body: "npm:^3.0.0"
    zod: "npm:^3.23.8"
    zod-to-json-schema: "npm:^3.24.1"
  checksum: 10c0/bbc07460659b5b4b4ecf19befd8bb8dde2aeee20123f388a0d58142da9a7a22e292fbb11b1d99ea54ae514652db129c3ec3b35fc5e9e61bf49d3bb9b221657cc
  languageName: node
  linkType: hard

"@msgpackr-extract/msgpackr-extract-darwin-arm64@npm:3.0.3":
  version: 3.0.3
  resolution: "@msgpackr-extract/msgpackr-extract-darwin-arm64@npm:3.0.3"
  conditions: os=darwin & cpu=arm64
  languageName: node
  linkType: hard

"@msgpackr-extract/msgpackr-extract-darwin-x64@npm:3.0.3":
  version: 3.0.3
  resolution: "@msgpackr-extract/msgpackr-extract-darwin-x64@npm:3.0.3"
  conditions: os=darwin & cpu=x64
  languageName: node
  linkType: hard

"@msgpackr-extract/msgpackr-extract-linux-arm64@npm:3.0.3":
  version: 3.0.3
  resolution: "@msgpackr-extract/msgpackr-extract-linux-arm64@npm:3.0.3"
  conditions: os=linux & cpu=arm64
  languageName: node
  linkType: hard

"@msgpackr-extract/msgpackr-extract-linux-arm@npm:3.0.3":
  version: 3.0.3
  resolution: "@msgpackr-extract/msgpackr-extract-linux-arm@npm:3.0.3"
  conditions: os=linux & cpu=arm
  languageName: node
  linkType: hard

"@msgpackr-extract/msgpackr-extract-linux-x64@npm:3.0.3":
  version: 3.0.3
  resolution: "@msgpackr-extract/msgpackr-extract-linux-x64@npm:3.0.3"
  conditions: os=linux & cpu=x64
  languageName: node
  linkType: hard

"@msgpackr-extract/msgpackr-extract-win32-x64@npm:3.0.3":
  version: 3.0.3
  resolution: "@msgpackr-extract/msgpackr-extract-win32-x64@npm:3.0.3"
  conditions: os=win32 & cpu=x64
  languageName: node
  linkType: hard

"@neon-rs/load@npm:^0.1.82":
  version: 0.1.82
  resolution: "@neon-rs/load@npm:0.1.82"
  checksum: 10c0/649fa4a484d67bd49c38354d1b0833300e6b80801accf7c0ad65b5623462d7c5141253635392440f0bafb88f6306fd5ee4cd24e0d06970586dbc151cd59892c2
  languageName: node
  linkType: hard

"@noble/hashes@npm:^1.1.5":
  version: 1.8.0
  resolution: "@noble/hashes@npm:1.8.0"
  checksum: 10c0/06a0b52c81a6fa7f04d67762e08b2c476a00285858150caeaaff4037356dd5e119f45b2a530f638b77a5eeca013168ec1b655db41bae3236cb2e9d511484fc77
  languageName: node
  linkType: hard

"@nodelib/fs.scandir@npm:2.1.5":
  version: 2.1.5
  resolution: "@nodelib/fs.scandir@npm:2.1.5"
  dependencies:
    "@nodelib/fs.stat": "npm:2.0.5"
    run-parallel: "npm:^1.1.9"
  checksum: 10c0/732c3b6d1b1e967440e65f284bd06e5821fedf10a1bea9ed2bb75956ea1f30e08c44d3def9d6a230666574edbaf136f8cfd319c14fd1f87c66e6a44449afb2eb
  languageName: node
  linkType: hard

"@nodelib/fs.stat@npm:2.0.5, @nodelib/fs.stat@npm:^2.0.2":
  version: 2.0.5
  resolution: "@nodelib/fs.stat@npm:2.0.5"
  checksum: 10c0/88dafe5e3e29a388b07264680dc996c17f4bda48d163a9d4f5c1112979f0ce8ec72aa7116122c350b4e7976bc5566dc3ddb579be1ceaacc727872eb4ed93926d
  languageName: node
  linkType: hard

"@nodelib/fs.walk@npm:^1.2.3":
  version: 1.2.8
  resolution: "@nodelib/fs.walk@npm:1.2.8"
  dependencies:
    "@nodelib/fs.scandir": "npm:2.1.5"
    fastq: "npm:^1.6.0"
  checksum: 10c0/db9de047c3bb9b51f9335a7bb46f4fcfb6829fb628318c12115fbaf7d369bfce71c15b103d1fc3b464812d936220ee9bc1c8f762d032c9f6be9acc99249095b1
  languageName: node
  linkType: hard

"@npmcli/agent@npm:^3.0.0":
  version: 3.0.0
  resolution: "@npmcli/agent@npm:3.0.0"
  dependencies:
    agent-base: "npm:^7.1.0"
    http-proxy-agent: "npm:^7.0.0"
    https-proxy-agent: "npm:^7.0.1"
    lru-cache: "npm:^10.0.1"
    socks-proxy-agent: "npm:^8.0.3"
  checksum: 10c0/efe37b982f30740ee77696a80c196912c274ecd2cb243bc6ae7053a50c733ce0f6c09fda085145f33ecf453be19654acca74b69e81eaad4c90f00ccffe2f9271
  languageName: node
  linkType: hard

"@npmcli/fs@npm:^4.0.0":
  version: 4.0.0
  resolution: "@npmcli/fs@npm:4.0.0"
  dependencies:
    semver: "npm:^7.3.5"
  checksum: 10c0/c90935d5ce670c87b6b14fab04a965a3b8137e585f8b2a6257263bd7f97756dd736cb165bb470e5156a9e718ecd99413dccc54b1138c1a46d6ec7cf325982fe5
  languageName: node
  linkType: hard

"@openrouter/ai-sdk-provider@npm:^0.4.5":
  version: 0.4.6
  resolution: "@openrouter/ai-sdk-provider@npm:0.4.6"
  dependencies:
    "@ai-sdk/provider": "npm:1.0.9"
    "@ai-sdk/provider-utils": "npm:2.1.10"
  peerDependencies:
    zod: ^3.0.0
  checksum: 10c0/ed813b0cdaeb9dd3b96cfad6788ffe2ffd5e89e5db491610c3645c9ff383c944214a2bf8fda52f82d051869d55a0caee8af6a6c695ae7c34f81ee7a136e3ed94
  languageName: node
  linkType: hard

"@opentelemetry/api-logs@npm:0.201.1, @opentelemetry/api-logs@npm:^0.201.0":
  version: 0.201.1
  resolution: "@opentelemetry/api-logs@npm:0.201.1"
  dependencies:
    "@opentelemetry/api": "npm:^1.3.0"
  checksum: 10c0/f5652a7d1dbfcba9d9a9b0e53a1debb428c99f859e833f14c09e471c0f01614245558bf3a4c0e9bda39c2031c10fa172ba3a564509aad56e5c8a30550a488170
  languageName: node
  linkType: hard

"@opentelemetry/api-logs@npm:0.202.0":
  version: 0.202.0
  resolution: "@opentelemetry/api-logs@npm:0.202.0"
  dependencies:
    "@opentelemetry/api": "npm:^1.3.0"
  checksum: 10c0/2b6fd961c9303c5581367e7bef16a0cc7e7fc9a22ffae417ac7812151238760a9c0db20aad2c222c492cd8afc8353a611076bcac9272168d04b64e6386e71a66
  languageName: node
  linkType: hard

"@opentelemetry/api-logs@npm:0.52.1":
  version: 0.52.1
  resolution: "@opentelemetry/api-logs@npm:0.52.1"
  dependencies:
    "@opentelemetry/api": "npm:^1.0.0"
  checksum: 10c0/fddecb2211f987bf1a7f104594e58227655c887a6a22b41e9ead5ed925a4594b56186b38fca8e24db33058a924d8b54ddd6b315eca915c469f9653ce7813c31a
  languageName: node
  linkType: hard

"@opentelemetry/api@npm:1.9.0, @opentelemetry/api@npm:^1.0.0, @opentelemetry/api@npm:^1.3.0, @opentelemetry/api@npm:^1.8.0, @opentelemetry/api@npm:^1.9.0":
  version: 1.9.0
  resolution: "@opentelemetry/api@npm:1.9.0"
  checksum: 10c0/9aae2fe6e8a3a3eeb6c1fdef78e1939cf05a0f37f8a4fae4d6bf2e09eb1e06f966ece85805626e01ba5fab48072b94f19b835449e58b6d26720ee19a58298add
  languageName: node
  linkType: hard

"@opentelemetry/auto-instrumentations-node@npm:^0.59.0":
  version: 0.59.0
  resolution: "@opentelemetry/auto-instrumentations-node@npm:0.59.0"
  dependencies:
    "@opentelemetry/instrumentation": "npm:^0.201.0"
    "@opentelemetry/instrumentation-amqplib": "npm:^0.48.0"
    "@opentelemetry/instrumentation-aws-lambda": "npm:^0.52.0"
    "@opentelemetry/instrumentation-aws-sdk": "npm:^0.53.0"
    "@opentelemetry/instrumentation-bunyan": "npm:^0.47.0"
    "@opentelemetry/instrumentation-cassandra-driver": "npm:^0.47.0"
    "@opentelemetry/instrumentation-connect": "npm:^0.45.0"
    "@opentelemetry/instrumentation-cucumber": "npm:^0.16.0"
    "@opentelemetry/instrumentation-dataloader": "npm:^0.18.0"
    "@opentelemetry/instrumentation-dns": "npm:^0.45.0"
    "@opentelemetry/instrumentation-express": "npm:^0.50.0"
    "@opentelemetry/instrumentation-fastify": "npm:^0.46.0"
    "@opentelemetry/instrumentation-fs": "npm:^0.21.0"
    "@opentelemetry/instrumentation-generic-pool": "npm:^0.45.0"
    "@opentelemetry/instrumentation-graphql": "npm:^0.49.0"
    "@opentelemetry/instrumentation-grpc": "npm:^0.201.0"
    "@opentelemetry/instrumentation-hapi": "npm:^0.47.0"
    "@opentelemetry/instrumentation-http": "npm:^0.201.0"
    "@opentelemetry/instrumentation-ioredis": "npm:^0.49.0"
    "@opentelemetry/instrumentation-kafkajs": "npm:^0.10.0"
    "@opentelemetry/instrumentation-knex": "npm:^0.46.0"
    "@opentelemetry/instrumentation-koa": "npm:^0.49.0"
    "@opentelemetry/instrumentation-lru-memoizer": "npm:^0.46.0"
    "@opentelemetry/instrumentation-memcached": "npm:^0.45.0"
    "@opentelemetry/instrumentation-mongodb": "npm:^0.54.0"
    "@opentelemetry/instrumentation-mongoose": "npm:^0.48.0"
    "@opentelemetry/instrumentation-mysql": "npm:^0.47.0"
    "@opentelemetry/instrumentation-mysql2": "npm:^0.47.0"
    "@opentelemetry/instrumentation-nestjs-core": "npm:^0.47.0"
    "@opentelemetry/instrumentation-net": "npm:^0.45.0"
    "@opentelemetry/instrumentation-oracledb": "npm:^0.27.0"
    "@opentelemetry/instrumentation-pg": "npm:^0.53.0"
    "@opentelemetry/instrumentation-pino": "npm:^0.48.0"
    "@opentelemetry/instrumentation-redis": "npm:^0.48.0"
    "@opentelemetry/instrumentation-redis-4": "npm:^0.48.0"
    "@opentelemetry/instrumentation-restify": "npm:^0.47.0"
    "@opentelemetry/instrumentation-router": "npm:^0.46.0"
    "@opentelemetry/instrumentation-runtime-node": "npm:^0.15.0"
    "@opentelemetry/instrumentation-socket.io": "npm:^0.48.0"
    "@opentelemetry/instrumentation-tedious": "npm:^0.20.0"
    "@opentelemetry/instrumentation-undici": "npm:^0.12.0"
    "@opentelemetry/instrumentation-winston": "npm:^0.46.0"
    "@opentelemetry/resource-detector-alibaba-cloud": "npm:^0.31.1"
    "@opentelemetry/resource-detector-aws": "npm:^2.1.0"
    "@opentelemetry/resource-detector-azure": "npm:^0.8.0"
    "@opentelemetry/resource-detector-container": "npm:^0.7.1"
    "@opentelemetry/resource-detector-gcp": "npm:^0.35.0"
    "@opentelemetry/resources": "npm:^2.0.0"
    "@opentelemetry/sdk-node": "npm:^0.201.0"
  peerDependencies:
    "@opentelemetry/api": ^1.4.1
    "@opentelemetry/core": ^2.0.0
  checksum: 10c0/20faf792ee6c4dd5a9872ad39287657c8f5b7d0d06c7c298b812fd47fb053d25e2182f8008e612f022b807d375861142ddf8d3f0b34792dd55d69495089fbea1
  languageName: node
  linkType: hard

"@opentelemetry/context-async-hooks@npm:1.25.1":
  version: 1.25.1
  resolution: "@opentelemetry/context-async-hooks@npm:1.25.1"
  peerDependencies:
    "@opentelemetry/api": ">=1.0.0 <1.10.0"
  checksum: 10c0/bdea47675fe7ca7363b548ca86e724baa102bbb68d92702a20c281615dbae040aad907ff08f553f0e4985868f99a762aadac04f07ad51915ef512c5c817d7976
  languageName: node
  linkType: hard

"@opentelemetry/context-async-hooks@npm:2.0.1":
  version: 2.0.1
  resolution: "@opentelemetry/context-async-hooks@npm:2.0.1"
  peerDependencies:
    "@opentelemetry/api": ">=1.0.0 <1.10.0"
  checksum: 10c0/75b06f33b9c3dccb8d9802c14badcc3b9a497b21c77bf0344fc6231041ea1bf6a2bcc195cc27fafd5914bffcc7fa160b9f4480c06a37e86e876c98bf1a533a0d
  languageName: node
  linkType: hard

"@opentelemetry/core@npm:1.25.1":
  version: 1.25.1
  resolution: "@opentelemetry/core@npm:1.25.1"
  dependencies:
    "@opentelemetry/semantic-conventions": "npm:1.25.1"
  peerDependencies:
    "@opentelemetry/api": ">=1.0.0 <1.10.0"
  checksum: 10c0/37270396fe3546e454f5a6e8cab0e5777e49a8e4e56ef05644c4e458b3ba7c662f57ad1ba2dd936ddaef54cbe985abd7cee0d3e9188dfdc0e3b3d446c3484337
  languageName: node
  linkType: hard

"@opentelemetry/core@npm:2.0.1, @opentelemetry/core@npm:^2.0.0, @opentelemetry/core@npm:^2.0.1":
  version: 2.0.1
  resolution: "@opentelemetry/core@npm:2.0.1"
  dependencies:
    "@opentelemetry/semantic-conventions": "npm:^1.29.0"
  peerDependencies:
    "@opentelemetry/api": ">=1.0.0 <1.10.0"
  checksum: 10c0/d587b1289559757d80da98039f9f57612f84f72ec608cd665dc467c7c6c5ce3a987dfcc2c63b521c7c86ce984a2552b3ead15a0dc458de1cf6bde5cdfe4ca9d8
  languageName: node
  linkType: hard

"@opentelemetry/exporter-logs-otlp-grpc@npm:0.201.1":
  version: 0.201.1
  resolution: "@opentelemetry/exporter-logs-otlp-grpc@npm:0.201.1"
  dependencies:
    "@grpc/grpc-js": "npm:^1.7.1"
    "@opentelemetry/core": "npm:2.0.1"
    "@opentelemetry/otlp-exporter-base": "npm:0.201.1"
    "@opentelemetry/otlp-grpc-exporter-base": "npm:0.201.1"
    "@opentelemetry/otlp-transformer": "npm:0.201.1"
    "@opentelemetry/sdk-logs": "npm:0.201.1"
  peerDependencies:
    "@opentelemetry/api": ^1.3.0
  checksum: 10c0/ed9fddc7c2bc86476c2420a4fe3556bf4eab289ce1d2f2f5f3016387186031178eef7292b0d0e0d3e676ffb8db07e8a5526f515c5ba1b135f101b7c372eb29da
  languageName: node
  linkType: hard

"@opentelemetry/exporter-logs-otlp-http@npm:0.201.1":
  version: 0.201.1
  resolution: "@opentelemetry/exporter-logs-otlp-http@npm:0.201.1"
  dependencies:
    "@opentelemetry/api-logs": "npm:0.201.1"
    "@opentelemetry/core": "npm:2.0.1"
    "@opentelemetry/otlp-exporter-base": "npm:0.201.1"
    "@opentelemetry/otlp-transformer": "npm:0.201.1"
    "@opentelemetry/sdk-logs": "npm:0.201.1"
  peerDependencies:
    "@opentelemetry/api": ^1.3.0
  checksum: 10c0/17e068cdc30ef8c6666580656fae791e83083150fb0ca69ed15887156e4c1595005d8f7798c4772b532bec5dbc9ac4f4be2190a1eaea9c6087a366bcf121f87b
  languageName: node
  linkType: hard

"@opentelemetry/exporter-logs-otlp-http@npm:0.52.1":
  version: 0.52.1
  resolution: "@opentelemetry/exporter-logs-otlp-http@npm:0.52.1"
  dependencies:
    "@opentelemetry/api-logs": "npm:0.52.1"
    "@opentelemetry/core": "npm:1.25.1"
    "@opentelemetry/otlp-exporter-base": "npm:0.52.1"
    "@opentelemetry/otlp-transformer": "npm:0.52.1"
    "@opentelemetry/sdk-logs": "npm:0.52.1"
  peerDependencies:
    "@opentelemetry/api": ^1.0.0
  checksum: 10c0/f8b94ab56eed59de883fa6fa225fef1c5a172a9093d8862a30df8387efa69a2a004817a79636ca8c416f07bec13f459c1df2b658cf02768323454b5a95286b64
  languageName: node
  linkType: hard

"@opentelemetry/exporter-logs-otlp-proto@npm:0.201.1":
  version: 0.201.1
  resolution: "@opentelemetry/exporter-logs-otlp-proto@npm:0.201.1"
  dependencies:
    "@opentelemetry/api-logs": "npm:0.201.1"
    "@opentelemetry/core": "npm:2.0.1"
    "@opentelemetry/otlp-exporter-base": "npm:0.201.1"
    "@opentelemetry/otlp-transformer": "npm:0.201.1"
    "@opentelemetry/resources": "npm:2.0.1"
    "@opentelemetry/sdk-logs": "npm:0.201.1"
    "@opentelemetry/sdk-trace-base": "npm:2.0.1"
  peerDependencies:
    "@opentelemetry/api": ^1.3.0
  checksum: 10c0/2871c716cb50534939803b95100904a767e24c3ebd7759fa59015561a3304772bae7cf3a698157c16b224c588db66c1806760e569c862dfb907a3118b8ddd709
  languageName: node
  linkType: hard

"@opentelemetry/exporter-metrics-otlp-grpc@npm:0.201.1":
  version: 0.201.1
  resolution: "@opentelemetry/exporter-metrics-otlp-grpc@npm:0.201.1"
  dependencies:
    "@grpc/grpc-js": "npm:^1.7.1"
    "@opentelemetry/core": "npm:2.0.1"
    "@opentelemetry/exporter-metrics-otlp-http": "npm:0.201.1"
    "@opentelemetry/otlp-exporter-base": "npm:0.201.1"
    "@opentelemetry/otlp-grpc-exporter-base": "npm:0.201.1"
    "@opentelemetry/otlp-transformer": "npm:0.201.1"
    "@opentelemetry/resources": "npm:2.0.1"
    "@opentelemetry/sdk-metrics": "npm:2.0.1"
  peerDependencies:
    "@opentelemetry/api": ^1.3.0
  checksum: 10c0/41f31849b0dc9d3a5cabf49334fb3e85d6cfc35f74a17914607961495869a3d894ed466f78f4b396799ee9356b215a473f4a044667c3fac7e19806f2bb2a10cb
  languageName: node
  linkType: hard

"@opentelemetry/exporter-metrics-otlp-http@npm:0.201.1":
  version: 0.201.1
  resolution: "@opentelemetry/exporter-metrics-otlp-http@npm:0.201.1"
  dependencies:
    "@opentelemetry/core": "npm:2.0.1"
    "@opentelemetry/otlp-exporter-base": "npm:0.201.1"
    "@opentelemetry/otlp-transformer": "npm:0.201.1"
    "@opentelemetry/resources": "npm:2.0.1"
    "@opentelemetry/sdk-metrics": "npm:2.0.1"
  peerDependencies:
    "@opentelemetry/api": ^1.3.0
  checksum: 10c0/7dbbfe02e594034772369ad5a10328c51a27e4afbdc5d12594bf362f7a3085ac3d85e454d7091bcf5f12062f5a4d51eb8c372c261980be08644365d17217bb0e
  languageName: node
  linkType: hard

"@opentelemetry/exporter-metrics-otlp-proto@npm:0.201.1":
  version: 0.201.1
  resolution: "@opentelemetry/exporter-metrics-otlp-proto@npm:0.201.1"
  dependencies:
    "@opentelemetry/core": "npm:2.0.1"
    "@opentelemetry/exporter-metrics-otlp-http": "npm:0.201.1"
    "@opentelemetry/otlp-exporter-base": "npm:0.201.1"
    "@opentelemetry/otlp-transformer": "npm:0.201.1"
    "@opentelemetry/resources": "npm:2.0.1"
    "@opentelemetry/sdk-metrics": "npm:2.0.1"
  peerDependencies:
    "@opentelemetry/api": ^1.3.0
  checksum: 10c0/1290d43c1d05ef4e36cd58dff32c1c40e55b6ec62820ac8b4bf05a538824b0e265cd3b39b4a172bd50bd255083774e6e291a6e7f6174030dc3d6243d5dc5ed84
  languageName: node
  linkType: hard

"@opentelemetry/exporter-prometheus@npm:0.201.1":
  version: 0.201.1
  resolution: "@opentelemetry/exporter-prometheus@npm:0.201.1"
  dependencies:
    "@opentelemetry/core": "npm:2.0.1"
    "@opentelemetry/resources": "npm:2.0.1"
    "@opentelemetry/sdk-metrics": "npm:2.0.1"
  peerDependencies:
    "@opentelemetry/api": ^1.3.0
  checksum: 10c0/4df3a0778063827ebf1c6c5e422c8527aba098d878e980c544331939eeee195ab6944264e86e5dd42f246fcace09198c1b91d8568191e4990489248e6bc94236
  languageName: node
  linkType: hard

"@opentelemetry/exporter-trace-otlp-grpc@npm:0.201.1, @opentelemetry/exporter-trace-otlp-grpc@npm:^0.201.1":
  version: 0.201.1
  resolution: "@opentelemetry/exporter-trace-otlp-grpc@npm:0.201.1"
  dependencies:
    "@grpc/grpc-js": "npm:^1.7.1"
    "@opentelemetry/core": "npm:2.0.1"
    "@opentelemetry/otlp-exporter-base": "npm:0.201.1"
    "@opentelemetry/otlp-grpc-exporter-base": "npm:0.201.1"
    "@opentelemetry/otlp-transformer": "npm:0.201.1"
    "@opentelemetry/resources": "npm:2.0.1"
    "@opentelemetry/sdk-trace-base": "npm:2.0.1"
  peerDependencies:
    "@opentelemetry/api": ^1.3.0
  checksum: 10c0/12de0a60f4251e0b832924aa6f5c3b3558181ee469c59f5ca84ab868acd21cea3a52573676c619aecef4a8d626d82852cb2d6a422adbe726e7842494663a342d
  languageName: node
  linkType: hard

"@opentelemetry/exporter-trace-otlp-grpc@npm:0.52.1":
  version: 0.52.1
  resolution: "@opentelemetry/exporter-trace-otlp-grpc@npm:0.52.1"
  dependencies:
    "@grpc/grpc-js": "npm:^1.7.1"
    "@opentelemetry/core": "npm:1.25.1"
    "@opentelemetry/otlp-grpc-exporter-base": "npm:0.52.1"
    "@opentelemetry/otlp-transformer": "npm:0.52.1"
    "@opentelemetry/resources": "npm:1.25.1"
    "@opentelemetry/sdk-trace-base": "npm:1.25.1"
  peerDependencies:
    "@opentelemetry/api": ^1.0.0
  checksum: 10c0/88bba51e128c049ce52091edce770a3a2aeade93ab0a630d498acd8c462fbbd1860a57f17eb11cbfe483a9b53e7b8bbfec7e1e9a111491c7044e6a27bd532824
  languageName: node
  linkType: hard

"@opentelemetry/exporter-trace-otlp-http@npm:0.201.1, @opentelemetry/exporter-trace-otlp-http@npm:^0.201.1":
  version: 0.201.1
  resolution: "@opentelemetry/exporter-trace-otlp-http@npm:0.201.1"
  dependencies:
    "@opentelemetry/core": "npm:2.0.1"
    "@opentelemetry/otlp-exporter-base": "npm:0.201.1"
    "@opentelemetry/otlp-transformer": "npm:0.201.1"
    "@opentelemetry/resources": "npm:2.0.1"
    "@opentelemetry/sdk-trace-base": "npm:2.0.1"
  peerDependencies:
    "@opentelemetry/api": ^1.3.0
  checksum: 10c0/4577663406cb2f6f65735dad61eb95a482f2a60b2b9b7b7574350aa80a1a29872113673b5b3c3d2949e2af93f601eb033498e160c329216d6912da679c2c455c
  languageName: node
  linkType: hard

"@opentelemetry/exporter-trace-otlp-http@npm:0.52.1":
  version: 0.52.1
  resolution: "@opentelemetry/exporter-trace-otlp-http@npm:0.52.1"
  dependencies:
    "@opentelemetry/core": "npm:1.25.1"
    "@opentelemetry/otlp-exporter-base": "npm:0.52.1"
    "@opentelemetry/otlp-transformer": "npm:0.52.1"
    "@opentelemetry/resources": "npm:1.25.1"
    "@opentelemetry/sdk-trace-base": "npm:1.25.1"
  peerDependencies:
    "@opentelemetry/api": ^1.0.0
  checksum: 10c0/7b9514ebb62e8bbca29148cc2dc207a70be83a2f7a2c2822904fa289f13ab39c94e6ecd576720ec9ef0e321083fe648c0c124f783c83e52179b39ac6d60452ea
  languageName: node
  linkType: hard

"@opentelemetry/exporter-trace-otlp-proto@npm:0.201.1":
  version: 0.201.1
  resolution: "@opentelemetry/exporter-trace-otlp-proto@npm:0.201.1"
  dependencies:
    "@opentelemetry/core": "npm:2.0.1"
    "@opentelemetry/otlp-exporter-base": "npm:0.201.1"
    "@opentelemetry/otlp-transformer": "npm:0.201.1"
    "@opentelemetry/resources": "npm:2.0.1"
    "@opentelemetry/sdk-trace-base": "npm:2.0.1"
  peerDependencies:
    "@opentelemetry/api": ^1.3.0
  checksum: 10c0/fed645348bac18a0c6d2aa4c0a0060186eb2112c7f22a0a495bfa9e0e2759582aabd40b1a46e873d1447b697e0292b708b2c02b1feb531c2e1e8a48216a9f6f6
  languageName: node
  linkType: hard

"@opentelemetry/exporter-trace-otlp-proto@npm:0.52.1":
  version: 0.52.1
  resolution: "@opentelemetry/exporter-trace-otlp-proto@npm:0.52.1"
  dependencies:
    "@opentelemetry/core": "npm:1.25.1"
    "@opentelemetry/otlp-exporter-base": "npm:0.52.1"
    "@opentelemetry/otlp-transformer": "npm:0.52.1"
    "@opentelemetry/resources": "npm:1.25.1"
    "@opentelemetry/sdk-trace-base": "npm:1.25.1"
  peerDependencies:
    "@opentelemetry/api": ^1.0.0
  checksum: 10c0/7106f707404a36acb028ea98420d819267908db10aa82f6128f64dfaab905ee4d14b5f0fcc879ea66e74248650cec3c07599b1fa21cac844c2d2a33f106a4848
  languageName: node
  linkType: hard

"@opentelemetry/exporter-zipkin@npm:1.25.1":
  version: 1.25.1
  resolution: "@opentelemetry/exporter-zipkin@npm:1.25.1"
  dependencies:
    "@opentelemetry/core": "npm:1.25.1"
    "@opentelemetry/resources": "npm:1.25.1"
    "@opentelemetry/sdk-trace-base": "npm:1.25.1"
    "@opentelemetry/semantic-conventions": "npm:1.25.1"
  peerDependencies:
    "@opentelemetry/api": ^1.0.0
  checksum: 10c0/f32fb4be2ad19cb75924f966d563a8efd9a56d8f4d73143017ff0c0792355096767a4b74e6a6108f38d19b34c189f7986d66d302cdcdf6df1f25b6ad3e911192
  languageName: node
  linkType: hard

"@opentelemetry/exporter-zipkin@npm:2.0.1":
  version: 2.0.1
  resolution: "@opentelemetry/exporter-zipkin@npm:2.0.1"
  dependencies:
    "@opentelemetry/core": "npm:2.0.1"
    "@opentelemetry/resources": "npm:2.0.1"
    "@opentelemetry/sdk-trace-base": "npm:2.0.1"
    "@opentelemetry/semantic-conventions": "npm:^1.29.0"
  peerDependencies:
    "@opentelemetry/api": ^1.0.0
  checksum: 10c0/10e0ad1dfb967c951d26bc64647e2f7d0705fdcf82449473308f277e1866552a07d7636bcf198e21662ada93df2366c4f24aec2d329d18e59f3d09ddcf65969d
  languageName: node
  linkType: hard

"@opentelemetry/instrumentation-amqplib@npm:^0.48.0":
  version: 0.48.0
  resolution: "@opentelemetry/instrumentation-amqplib@npm:0.48.0"
  dependencies:
    "@opentelemetry/core": "npm:^2.0.0"
    "@opentelemetry/instrumentation": "npm:^0.201.0"
    "@opentelemetry/semantic-conventions": "npm:^1.27.0"
  peerDependencies:
    "@opentelemetry/api": ^1.3.0
  checksum: 10c0/fdb3f8b167b101f5d9284dcf641b2d69fceb773aa5a6b12e90d185d0922109164e7a95048e6aa3b43738d77ee1a5cff51034f1e741408244e5b6e3d2c286be77
  languageName: node
  linkType: hard

"@opentelemetry/instrumentation-aws-lambda@npm:^0.52.0":
  version: 0.52.0
  resolution: "@opentelemetry/instrumentation-aws-lambda@npm:0.52.0"
  dependencies:
    "@opentelemetry/instrumentation": "npm:^0.201.0"
    "@opentelemetry/semantic-conventions": "npm:^1.27.0"
    "@types/aws-lambda": "npm:8.10.147"
  peerDependencies:
    "@opentelemetry/api": ^1.3.0
  checksum: 10c0/8779e8fcf7ad60f7fb4d662497b479bdf039df54cb114304eaf082961689024dd1cbefd258b5d28ded9369625a59965ca5f4607084e6b0680a271239588e846b
  languageName: node
  linkType: hard

"@opentelemetry/instrumentation-aws-sdk@npm:^0.53.0":
  version: 0.53.0
  resolution: "@opentelemetry/instrumentation-aws-sdk@npm:0.53.0"
  dependencies:
    "@opentelemetry/core": "npm:^2.0.0"
    "@opentelemetry/instrumentation": "npm:^0.201.0"
    "@opentelemetry/propagation-utils": "npm:^0.31.1"
    "@opentelemetry/semantic-conventions": "npm:^1.31.0"
  peerDependencies:
    "@opentelemetry/api": ^1.3.0
  checksum: 10c0/877c140e3394c505aee34bfd6fcaabf2eb4e7f0b5e6d945715f947f13d0c9eebe221cbcfa9b5eb9e559d8696bf955569a12ee0205c06ac4913ef95e8dbb7cea6
  languageName: node
  linkType: hard

"@opentelemetry/instrumentation-bunyan@npm:^0.47.0":
  version: 0.47.0
  resolution: "@opentelemetry/instrumentation-bunyan@npm:0.47.0"
  dependencies:
    "@opentelemetry/api-logs": "npm:^0.201.0"
    "@opentelemetry/instrumentation": "npm:^0.201.0"
    "@types/bunyan": "npm:1.8.11"
  peerDependencies:
    "@opentelemetry/api": ^1.3.0
  checksum: 10c0/be058cdf97ebc427ae9338360e834a2c2b4b80dff7290b5eeb905b7dac8b166dc12fd045fade4c67cd0784ce7574fb5616c0eb90cdc95d6c6495250d5458a6fc
  languageName: node
  linkType: hard

"@opentelemetry/instrumentation-cassandra-driver@npm:^0.47.0":
  version: 0.47.0
  resolution: "@opentelemetry/instrumentation-cassandra-driver@npm:0.47.0"
  dependencies:
    "@opentelemetry/instrumentation": "npm:^0.201.0"
    "@opentelemetry/semantic-conventions": "npm:^1.27.0"
  peerDependencies:
    "@opentelemetry/api": ^1.3.0
  checksum: 10c0/0c2ef7c73859bb83e22ab0cfbf70591e0ec4007b355f967c617f36dc150b4f1d84a3a0d20b5ed0b64273a26e4fe98608276ae5a5584c4af0b46166174779ce64
  languageName: node
  linkType: hard

"@opentelemetry/instrumentation-connect@npm:^0.45.0":
  version: 0.45.0
  resolution: "@opentelemetry/instrumentation-connect@npm:0.45.0"
  dependencies:
    "@opentelemetry/core": "npm:^2.0.0"
    "@opentelemetry/instrumentation": "npm:^0.201.0"
    "@opentelemetry/semantic-conventions": "npm:^1.27.0"
    "@types/connect": "npm:3.4.38"
  peerDependencies:
    "@opentelemetry/api": ^1.3.0
  checksum: 10c0/6fdd3dededfb0a2e82fb13ab1612cd4eef51ef5b4a078888d054867561aedd0458792e357b53db285e43aa70ce3e913f3ff9332b7468aa0454e5c01abadb797e
  languageName: node
  linkType: hard

"@opentelemetry/instrumentation-cucumber@npm:^0.16.0":
  version: 0.16.0
  resolution: "@opentelemetry/instrumentation-cucumber@npm:0.16.0"
  dependencies:
    "@opentelemetry/instrumentation": "npm:^0.201.0"
    "@opentelemetry/semantic-conventions": "npm:^1.27.0"
  peerDependencies:
    "@opentelemetry/api": ^1.0.0
  checksum: 10c0/f3625947b14f30c1bf8f99d453b0bfe2352ba54d9b3f3f0d2bab9151f49d28a63918e252916c3a7d87bb56b9adf1fe9697e45560345cafe0ad286457f118afd6
  languageName: node
  linkType: hard

"@opentelemetry/instrumentation-dataloader@npm:^0.18.0":
  version: 0.18.0
  resolution: "@opentelemetry/instrumentation-dataloader@npm:0.18.0"
  dependencies:
    "@opentelemetry/instrumentation": "npm:^0.201.0"
  peerDependencies:
    "@opentelemetry/api": ^1.3.0
  checksum: 10c0/a827f9d1188007b76c065fc45ca39e58a88ebd82825dd05bc351a15c0f4f18433c999bdecf685c13a9071f264745b2ee42fdd238cdea51b578ecabca0a9da041
  languageName: node
  linkType: hard

"@opentelemetry/instrumentation-dns@npm:^0.45.0":
  version: 0.45.0
  resolution: "@opentelemetry/instrumentation-dns@npm:0.45.0"
  dependencies:
    "@opentelemetry/instrumentation": "npm:^0.201.0"
  peerDependencies:
    "@opentelemetry/api": ^1.3.0
  checksum: 10c0/49807d5c794ba2781c1a1628f09d11320736b14a1c089c564103a9e951514bc636d9b822fb1dfa646f0d1974ae95efb55aa936b0132c6a0530a2a919069830d1
  languageName: node
  linkType: hard

"@opentelemetry/instrumentation-express@npm:^0.50.0":
  version: 0.50.0
  resolution: "@opentelemetry/instrumentation-express@npm:0.50.0"
  dependencies:
    "@opentelemetry/core": "npm:^2.0.0"
    "@opentelemetry/instrumentation": "npm:^0.201.0"
    "@opentelemetry/semantic-conventions": "npm:^1.27.0"
  peerDependencies:
    "@opentelemetry/api": ^1.3.0
  checksum: 10c0/26ecb3c504f13efe34ed3bad3c6db022628e688da66e68ea6adfc8358dd8c8aef025b71b8b57e048560f88a69cd9ad0eef6419410849b614214ec4156240cb38
  languageName: node
  linkType: hard

"@opentelemetry/instrumentation-fastify@npm:^0.46.0":
  version: 0.46.0
  resolution: "@opentelemetry/instrumentation-fastify@npm:0.46.0"
  dependencies:
    "@opentelemetry/core": "npm:^2.0.0"
    "@opentelemetry/instrumentation": "npm:^0.201.0"
    "@opentelemetry/semantic-conventions": "npm:^1.27.0"
  peerDependencies:
    "@opentelemetry/api": ^1.3.0
  checksum: 10c0/46c7accc9cb739156d616f187fe382c112f0d437327aaeaa0333bb196067d1511720ddf8c61b6fc8653c8f32cf2096ffac41e2fdb546d645b22fa85345e2ca2f
  languageName: node
  linkType: hard

"@opentelemetry/instrumentation-fs@npm:^0.21.0":
  version: 0.21.0
  resolution: "@opentelemetry/instrumentation-fs@npm:0.21.0"
  dependencies:
    "@opentelemetry/core": "npm:^2.0.0"
    "@opentelemetry/instrumentation": "npm:^0.201.0"
  peerDependencies:
    "@opentelemetry/api": ^1.3.0
  checksum: 10c0/fc070a6baf96e820d860c1847e1636373d9d8915e7b6032bb42f2dd7333a823aaf02dc2ca20f579c707cedd06d8483ed394c7261e667214faefec59637263148
  languageName: node
  linkType: hard

"@opentelemetry/instrumentation-generic-pool@npm:^0.45.0":
  version: 0.45.0
  resolution: "@opentelemetry/instrumentation-generic-pool@npm:0.45.0"
  dependencies:
    "@opentelemetry/instrumentation": "npm:^0.201.0"
  peerDependencies:
    "@opentelemetry/api": ^1.3.0
  checksum: 10c0/025b7c4043f8c5e2dc3cf98b7ef49ae56ac8fb7fccc80f754ad0c6bcf6e177c7ae5c0b0fbf4f09c45bbbb36a123ac654e5b753dea13ae128c7821833dcacd15a
  languageName: node
  linkType: hard

"@opentelemetry/instrumentation-graphql@npm:^0.49.0":
  version: 0.49.0
  resolution: "@opentelemetry/instrumentation-graphql@npm:0.49.0"
  dependencies:
    "@opentelemetry/instrumentation": "npm:^0.201.0"
  peerDependencies:
    "@opentelemetry/api": ^1.3.0
  checksum: 10c0/4b30f94e11553328cd314daf09c06f8445b3077b8e2e15aa87897e17d54ebd8b685652a180221721026e407ce3e1294571514433203844de2722911ec9486f6b
  languageName: node
  linkType: hard

"@opentelemetry/instrumentation-grpc@npm:^0.201.0":
  version: 0.201.1
  resolution: "@opentelemetry/instrumentation-grpc@npm:0.201.1"
  dependencies:
    "@opentelemetry/instrumentation": "npm:0.201.1"
    "@opentelemetry/semantic-conventions": "npm:^1.29.0"
  peerDependencies:
    "@opentelemetry/api": ^1.3.0
  checksum: 10c0/3adc412297c94e253688391307c9903ba5f6498b65d9162683c87a34ad4bf407c22086561a989a2f5d1b219cdfd78856f11e2cdb64727e85482b1029fa769bce
  languageName: node
  linkType: hard

"@opentelemetry/instrumentation-hapi@npm:^0.47.0":
  version: 0.47.0
  resolution: "@opentelemetry/instrumentation-hapi@npm:0.47.0"
  dependencies:
    "@opentelemetry/core": "npm:^2.0.0"
    "@opentelemetry/instrumentation": "npm:^0.201.0"
    "@opentelemetry/semantic-conventions": "npm:^1.27.0"
  peerDependencies:
    "@opentelemetry/api": ^1.3.0
  checksum: 10c0/424a4f9cd62c3a72885a2c8078646bca2af468e09fd48b6cd4e5ef1ec4580d6066e8445c7a5757d6c342140acee2a50b78cb3ad8533223d729fc9f1217ba26cb
  languageName: node
  linkType: hard

"@opentelemetry/instrumentation-http@npm:^0.201.0":
  version: 0.201.1
  resolution: "@opentelemetry/instrumentation-http@npm:0.201.1"
  dependencies:
    "@opentelemetry/core": "npm:2.0.1"
    "@opentelemetry/instrumentation": "npm:0.201.1"
    "@opentelemetry/semantic-conventions": "npm:^1.29.0"
    forwarded-parse: "npm:2.1.2"
  peerDependencies:
    "@opentelemetry/api": ^1.3.0
  checksum: 10c0/019661bfbb7fc89b598b3a8251c40e2c5889c0c558c6f8a757b570dce99ce719ecfaee60faa012eb4e81878ced6959be9e69a6047f3589c83cc3174b4bcd62c5
  languageName: node
  linkType: hard

"@opentelemetry/instrumentation-ioredis@npm:^0.49.0":
  version: 0.49.0
  resolution: "@opentelemetry/instrumentation-ioredis@npm:0.49.0"
  dependencies:
    "@opentelemetry/instrumentation": "npm:^0.201.0"
    "@opentelemetry/redis-common": "npm:^0.37.0"
    "@opentelemetry/semantic-conventions": "npm:^1.27.0"
  peerDependencies:
    "@opentelemetry/api": ^1.3.0
  checksum: 10c0/1e0dd0db49472b9507e974d881f176dd30d2f5967d4dd6a0582cd40d9483d2f9e88885192c1c69b2efb4988d177b270be61fb97229ec4df989ade9c5df27943d
  languageName: node
  linkType: hard

"@opentelemetry/instrumentation-kafkajs@npm:^0.10.0":
  version: 0.10.0
  resolution: "@opentelemetry/instrumentation-kafkajs@npm:0.10.0"
  dependencies:
    "@opentelemetry/instrumentation": "npm:^0.201.0"
    "@opentelemetry/semantic-conventions": "npm:^1.30.0"
  peerDependencies:
    "@opentelemetry/api": ^1.3.0
  checksum: 10c0/f8d0280d27f6d29c441e52f73ce3590dae5f673ff4a35037e50599726c277d7ccd956b0ae65621b4d73c6f97667373b5d67792706d45aa8e8de288493ec2022a
  languageName: node
  linkType: hard

"@opentelemetry/instrumentation-knex@npm:^0.46.0":
  version: 0.46.0
  resolution: "@opentelemetry/instrumentation-knex@npm:0.46.0"
  dependencies:
    "@opentelemetry/instrumentation": "npm:^0.201.0"
    "@opentelemetry/semantic-conventions": "npm:^1.27.0"
  peerDependencies:
    "@opentelemetry/api": ^1.3.0
  checksum: 10c0/e95739f84aaaa3e525f126e5167bbccf88c7fa9d7c304d4904869a6fca9a22b140d647d9fc324c4990b51267df1edbb35e2983405d75ef60add885040216bbd8
  languageName: node
  linkType: hard

"@opentelemetry/instrumentation-koa@npm:^0.49.0":
  version: 0.49.0
  resolution: "@opentelemetry/instrumentation-koa@npm:0.49.0"
  dependencies:
    "@opentelemetry/core": "npm:^2.0.0"
    "@opentelemetry/instrumentation": "npm:^0.201.0"
    "@opentelemetry/semantic-conventions": "npm:^1.27.0"
  peerDependencies:
    "@opentelemetry/api": ^1.3.0
  checksum: 10c0/4c65866368e9ff6643774a44f873e0788e80f6febd5aa8208fde1cf9981aab5f9fbf188fb26f976376c6a2dccb24d4e2ec62a53194fd757a21dd14c326ebd153
  languageName: node
  linkType: hard

"@opentelemetry/instrumentation-lru-memoizer@npm:^0.46.0":
  version: 0.46.0
  resolution: "@opentelemetry/instrumentation-lru-memoizer@npm:0.46.0"
  dependencies:
    "@opentelemetry/instrumentation": "npm:^0.201.0"
  peerDependencies:
    "@opentelemetry/api": ^1.3.0
  checksum: 10c0/24a7e922b9243f041bbd3d8aec406f34dc9751ac7eb28fbb23dd08f24228817b1072e6ac69a4038480170c1dadd126e5a55ef3c7b8baa801d6e9759b94737a50
  languageName: node
  linkType: hard

"@opentelemetry/instrumentation-memcached@npm:^0.45.0":
  version: 0.45.0
  resolution: "@opentelemetry/instrumentation-memcached@npm:0.45.0"
  dependencies:
    "@opentelemetry/instrumentation": "npm:^0.201.0"
    "@opentelemetry/semantic-conventions": "npm:^1.27.0"
    "@types/memcached": "npm:^2.2.6"
  peerDependencies:
    "@opentelemetry/api": ^1.3.0
  checksum: 10c0/6c18f63b9e6fa081b8d9f0fc064f0136ae1b3f19bb6fc52c209cfd452395f66215042113967a6bed0fdbe26ec98578ff0a440df441c49bb0d44027988d90ec6c
  languageName: node
  linkType: hard

"@opentelemetry/instrumentation-mongodb@npm:^0.54.0":
  version: 0.54.0
  resolution: "@opentelemetry/instrumentation-mongodb@npm:0.54.0"
  dependencies:
    "@opentelemetry/instrumentation": "npm:^0.201.0"
    "@opentelemetry/semantic-conventions": "npm:^1.27.0"
  peerDependencies:
    "@opentelemetry/api": ^1.3.0
  checksum: 10c0/8a78103154a5c3f33355824fbf25b9354880c95cf2105b1158ff3dc6d7f0281259f92b534d814a9517c2b805ba7225c1fe3c0ea05b9c650f3314f4fe433abdad
  languageName: node
  linkType: hard

"@opentelemetry/instrumentation-mongoose@npm:^0.48.0":
  version: 0.48.0
  resolution: "@opentelemetry/instrumentation-mongoose@npm:0.48.0"
  dependencies:
    "@opentelemetry/core": "npm:^2.0.0"
    "@opentelemetry/instrumentation": "npm:^0.201.0"
    "@opentelemetry/semantic-conventions": "npm:^1.27.0"
  peerDependencies:
    "@opentelemetry/api": ^1.3.0
  checksum: 10c0/aea3445b6dc0b2936c4b1e0a0643a70dc620e86a48a6f954555d1129bdf34038db231fe1a80087fd4c21948500fe02f7b209f111b781c871d5fef3c77f9c9ace
  languageName: node
  linkType: hard

"@opentelemetry/instrumentation-mysql2@npm:^0.47.0":
  version: 0.47.0
  resolution: "@opentelemetry/instrumentation-mysql2@npm:0.47.0"
  dependencies:
    "@opentelemetry/instrumentation": "npm:^0.201.0"
    "@opentelemetry/semantic-conventions": "npm:^1.27.0"
    "@opentelemetry/sql-common": "npm:^0.41.0"
  peerDependencies:
    "@opentelemetry/api": ^1.3.0
  checksum: 10c0/339bf7f7aef81160b34bfb4f59aac9f377dc18c560b457ccc39a960f7cbb7135fa417b4bceb1cdc0715321e9b2a04cafde054a33279930416d989b30ea65ea65
  languageName: node
  linkType: hard

"@opentelemetry/instrumentation-mysql@npm:^0.47.0":
  version: 0.47.0
  resolution: "@opentelemetry/instrumentation-mysql@npm:0.47.0"
  dependencies:
    "@opentelemetry/instrumentation": "npm:^0.201.0"
    "@opentelemetry/semantic-conventions": "npm:^1.27.0"
    "@types/mysql": "npm:2.15.26"
  peerDependencies:
    "@opentelemetry/api": ^1.3.0
  checksum: 10c0/e4c8d1e66c14a6aea3b5d54e166852a637edd33a5fa06c18cab6b4a66659750701111c8a6720eecbe883090308d1b96237c6839074051964f2822f0d2277c06f
  languageName: node
  linkType: hard

"@opentelemetry/instrumentation-nestjs-core@npm:^0.47.0":
  version: 0.47.0
  resolution: "@opentelemetry/instrumentation-nestjs-core@npm:0.47.0"
  dependencies:
    "@opentelemetry/instrumentation": "npm:^0.201.0"
    "@opentelemetry/semantic-conventions": "npm:^1.30.0"
  peerDependencies:
    "@opentelemetry/api": ^1.3.0
  checksum: 10c0/88473904928011921287e232c8701a254d98f0a7e5bde381b6e630605a5deecb58d87686fa8b711dc545cb5037b33805232234764a60be1be3804fb1d556ed57
  languageName: node
  linkType: hard

"@opentelemetry/instrumentation-net@npm:^0.45.0":
  version: 0.45.0
  resolution: "@opentelemetry/instrumentation-net@npm:0.45.0"
  dependencies:
    "@opentelemetry/instrumentation": "npm:^0.201.0"
    "@opentelemetry/semantic-conventions": "npm:^1.27.0"
  peerDependencies:
    "@opentelemetry/api": ^1.3.0
  checksum: 10c0/7df3a7717293a87db73c3ba33afeefcb71c208a79dbf6e69307d0174e3e1f2154b777635168887e82d45524a019d3377557ba56f50ec54c2c7852e2db76bf5a6
  languageName: node
  linkType: hard

"@opentelemetry/instrumentation-oracledb@npm:^0.27.0":
  version: 0.27.0
  resolution: "@opentelemetry/instrumentation-oracledb@npm:0.27.0"
  dependencies:
    "@opentelemetry/instrumentation": "npm:^0.201.0"
    "@opentelemetry/semantic-conventions": "npm:^1.27.0"
    "@types/oracledb": "npm:6.5.2"
  peerDependencies:
    "@opentelemetry/api": ^1.3.0
  checksum: 10c0/73450408d5c88d8a93965e71a42af512349bae077eaa84ee48b0689561a9cb34158fc179503ccd5706978cb4f97cf6b3212edc056d2e016dfaadb72186516fca
  languageName: node
  linkType: hard

"@opentelemetry/instrumentation-pg@npm:^0.53.0":
  version: 0.53.0
  resolution: "@opentelemetry/instrumentation-pg@npm:0.53.0"
  dependencies:
    "@opentelemetry/core": "npm:^2.0.0"
    "@opentelemetry/instrumentation": "npm:^0.201.0"
    "@opentelemetry/semantic-conventions": "npm:^1.27.0"
    "@opentelemetry/sql-common": "npm:^0.41.0"
    "@types/pg": "npm:8.6.1"
    "@types/pg-pool": "npm:2.0.6"
  peerDependencies:
    "@opentelemetry/api": ^1.3.0
  checksum: 10c0/c3c575f4d9a4dacc21ae919ac6d4bda7ab525b08e0b675253c269f05c92d99c29b875d0dcde31110b2fc22c00159ad715991d16094122002735dbef5091040ba
  languageName: node
  linkType: hard

"@opentelemetry/instrumentation-pino@npm:^0.48.0":
  version: 0.48.0
  resolution: "@opentelemetry/instrumentation-pino@npm:0.48.0"
  dependencies:
    "@opentelemetry/api-logs": "npm:^0.201.0"
    "@opentelemetry/core": "npm:^2.0.0"
    "@opentelemetry/instrumentation": "npm:^0.201.0"
  peerDependencies:
    "@opentelemetry/api": ^1.3.0
  checksum: 10c0/c3c43009c5522fde0faae50a79aca6884d91cc2babc2e14c47ed2699bde46dde6f124c33641f7c4b15ef1eaf148c037cbfa44107b91b41e44852ac09db609d84
  languageName: node
  linkType: hard

"@opentelemetry/instrumentation-redis-4@npm:^0.48.0":
  version: 0.48.0
  resolution: "@opentelemetry/instrumentation-redis-4@npm:0.48.0"
  dependencies:
    "@opentelemetry/instrumentation": "npm:^0.201.0"
    "@opentelemetry/redis-common": "npm:^0.37.0"
    "@opentelemetry/semantic-conventions": "npm:^1.27.0"
  peerDependencies:
    "@opentelemetry/api": ^1.3.0
  checksum: 10c0/a88aa2264b8f96321c4363391a419fb4d9efad99792271f87ac10b5bf8b923cfc76ef86460403c31c7f2373ccfbc682692c779244b53e83ca63ce6a4fd56424a
  languageName: node
  linkType: hard

"@opentelemetry/instrumentation-redis@npm:^0.48.0":
  version: 0.48.0
  resolution: "@opentelemetry/instrumentation-redis@npm:0.48.0"
  dependencies:
    "@opentelemetry/instrumentation": "npm:^0.201.0"
    "@opentelemetry/redis-common": "npm:^0.37.0"
    "@opentelemetry/semantic-conventions": "npm:^1.27.0"
  peerDependencies:
    "@opentelemetry/api": ^1.3.0
  checksum: 10c0/a736af20042a0917259f5208471d711516899bd891724d8453ed64b941397754d7571697b15858c4c0642d40dad46b7245d4ea99dc34eacd3c7d0aca4ac6140b
  languageName: node
  linkType: hard

"@opentelemetry/instrumentation-restify@npm:^0.47.0":
  version: 0.47.0
  resolution: "@opentelemetry/instrumentation-restify@npm:0.47.0"
  dependencies:
    "@opentelemetry/core": "npm:^2.0.0"
    "@opentelemetry/instrumentation": "npm:^0.201.0"
    "@opentelemetry/semantic-conventions": "npm:^1.27.0"
  peerDependencies:
    "@opentelemetry/api": ^1.3.0
  checksum: 10c0/a3935b0e6772c451e8da2808595e9c00e8eccc884464a87173553b52fedf587beb685441ad2001c42c10ad5e71971a776dfffe929ad3669b1ea0a9145d2a1c90
  languageName: node
  linkType: hard

"@opentelemetry/instrumentation-router@npm:^0.46.0":
  version: 0.46.0
  resolution: "@opentelemetry/instrumentation-router@npm:0.46.0"
  dependencies:
    "@opentelemetry/instrumentation": "npm:^0.201.0"
    "@opentelemetry/semantic-conventions": "npm:^1.27.0"
  peerDependencies:
    "@opentelemetry/api": ^1.3.0
  checksum: 10c0/b29daa4f9fb6409e76386718bbbd13f24c4273d420bfa640b0e777e1e96fc69cf9f76ee7905ffb750cd7f5c7eeae3e49fedbf114e4f6a57216445401512731e3
  languageName: node
  linkType: hard

"@opentelemetry/instrumentation-runtime-node@npm:^0.15.0":
  version: 0.15.0
  resolution: "@opentelemetry/instrumentation-runtime-node@npm:0.15.0"
  dependencies:
    "@opentelemetry/instrumentation": "npm:^0.201.0"
  peerDependencies:
    "@opentelemetry/api": ^1.3.0
  checksum: 10c0/7b9e730830744984e01fb81d552313f18d1878a13875e8275f3aa50ba03231b1e6a8deebf71186abad8cfe91f169950b05e7ca528daca8ef3d7c24a876029115
  languageName: node
  linkType: hard

"@opentelemetry/instrumentation-socket.io@npm:^0.48.0":
  version: 0.48.0
  resolution: "@opentelemetry/instrumentation-socket.io@npm:0.48.0"
  dependencies:
    "@opentelemetry/instrumentation": "npm:^0.201.0"
    "@opentelemetry/semantic-conventions": "npm:^1.27.0"
  peerDependencies:
    "@opentelemetry/api": ^1.3.0
  checksum: 10c0/96294f67d57dfbbd69797bf3fb21c4c9770cb1319004cdc648a09b8336a547ca2edcd9c666b475f1796c0a0969d1bf67b373b75662ef9b4680b4f5aa1cccf266
  languageName: node
  linkType: hard

"@opentelemetry/instrumentation-tedious@npm:^0.20.0":
  version: 0.20.0
  resolution: "@opentelemetry/instrumentation-tedious@npm:0.20.0"
  dependencies:
    "@opentelemetry/instrumentation": "npm:^0.201.0"
    "@opentelemetry/semantic-conventions": "npm:^1.27.0"
    "@types/tedious": "npm:^4.0.14"
  peerDependencies:
    "@opentelemetry/api": ^1.3.0
  checksum: 10c0/90c4d99db79ca4c0fe22318c41e2f271e3e8ea6a614e6d06d18d6c53d66ee6b6fb577c0b2d4508bba51526e1196bf05b6b4edf71131641dcb469df93f26156b1
  languageName: node
  linkType: hard

"@opentelemetry/instrumentation-undici@npm:^0.12.0":
  version: 0.12.0
  resolution: "@opentelemetry/instrumentation-undici@npm:0.12.0"
  dependencies:
    "@opentelemetry/core": "npm:^2.0.0"
    "@opentelemetry/instrumentation": "npm:^0.201.0"
  peerDependencies:
    "@opentelemetry/api": ^1.7.0
  checksum: 10c0/97411490bae3d508332d8a71eed688bc4e56530502031e8aa92b60f1663c9be66ddd6c88b6b3d5dbc40822d4d64bc67d869fc8626442c2f1905d147fcc0dd22b
  languageName: node
  linkType: hard

"@opentelemetry/instrumentation-winston@npm:^0.46.0":
  version: 0.46.0
  resolution: "@opentelemetry/instrumentation-winston@npm:0.46.0"
  dependencies:
    "@opentelemetry/api-logs": "npm:^0.201.0"
    "@opentelemetry/instrumentation": "npm:^0.201.0"
  peerDependencies:
    "@opentelemetry/api": ^1.3.0
  checksum: 10c0/b6576d03e0a8711e0a5fcb726791ccbfdeab24923eea82ba905bf00f33bbcf560c2b07c556ec66ef8c5bc07d6de2d5e1605a11740050d632d7bdc5473ef530ce
  languageName: node
  linkType: hard

"@opentelemetry/instrumentation@npm:0.201.1, @opentelemetry/instrumentation@npm:^0.201.0":
  version: 0.201.1
  resolution: "@opentelemetry/instrumentation@npm:0.201.1"
  dependencies:
    "@opentelemetry/api-logs": "npm:0.201.1"
    "@types/shimmer": "npm:^1.2.0"
    import-in-the-middle: "npm:^1.8.1"
    require-in-the-middle: "npm:^7.1.1"
    shimmer: "npm:^1.2.1"
  peerDependencies:
    "@opentelemetry/api": ^1.3.0
  checksum: 10c0/b78e4e4225c7eb62f5ba8b5f70c078dc2e3a8d3685286ae3e025365c1ea69de520df2770a96510958be58e6fc93549085fd6431df38865ee5805de56d732d359
  languageName: node
  linkType: hard

"@opentelemetry/instrumentation@npm:0.52.1":
  version: 0.52.1
  resolution: "@opentelemetry/instrumentation@npm:0.52.1"
  dependencies:
    "@opentelemetry/api-logs": "npm:0.52.1"
    "@types/shimmer": "npm:^1.0.2"
    import-in-the-middle: "npm:^1.8.1"
    require-in-the-middle: "npm:^7.1.1"
    semver: "npm:^7.5.2"
    shimmer: "npm:^1.2.1"
  peerDependencies:
    "@opentelemetry/api": ^1.3.0
  checksum: 10c0/1d4946b470ac31358ba8d81a9f9653a1d705db96ffb8958fef873340c3d3c9699cfd8ff617c313ea8c6a8ece51aa7cf8af37d87a60813c31ed2207e5c14a33ba
  languageName: node
  linkType: hard

"@opentelemetry/instrumentation@npm:^0.202.0":
  version: 0.202.0
  resolution: "@opentelemetry/instrumentation@npm:0.202.0"
  dependencies:
    "@opentelemetry/api-logs": "npm:0.202.0"
    import-in-the-middle: "npm:^1.8.1"
    require-in-the-middle: "npm:^7.1.1"
  peerDependencies:
    "@opentelemetry/api": ^1.3.0
  checksum: 10c0/3edac99d3093841bcd6008c19353f989042231485bcfe93c069e6ac41f35ab11800998275c0e3a85873656a21af982b8fd8721da974288a4ac653ecae8407641
  languageName: node
  linkType: hard

"@opentelemetry/otlp-exporter-base@npm:0.201.1, @opentelemetry/otlp-exporter-base@npm:^0.201.1":
  version: 0.201.1
  resolution: "@opentelemetry/otlp-exporter-base@npm:0.201.1"
  dependencies:
    "@opentelemetry/core": "npm:2.0.1"
    "@opentelemetry/otlp-transformer": "npm:0.201.1"
  peerDependencies:
    "@opentelemetry/api": ^1.3.0
  checksum: 10c0/3bdabddd24623e9545607d250dd772579a436cde2782aa4edcedc2184a619f3d3a9740dc41bf24b97f4b24ef00d1d51c8b8496ba91432fcb75760edaf447b123
  languageName: node
  linkType: hard

"@opentelemetry/otlp-exporter-base@npm:0.52.1":
  version: 0.52.1
  resolution: "@opentelemetry/otlp-exporter-base@npm:0.52.1"
  dependencies:
    "@opentelemetry/core": "npm:1.25.1"
    "@opentelemetry/otlp-transformer": "npm:0.52.1"
  peerDependencies:
    "@opentelemetry/api": ^1.0.0
  checksum: 10c0/840f0e74ed2e10b48b28a3802c58eeedd41a127590fd600db95c903df3a5d23aaf5707017e9f9d89ea5b4fd1985e146e7a06864dca2878821cf60bc5e5e7bd35
  languageName: node
  linkType: hard

"@opentelemetry/otlp-grpc-exporter-base@npm:0.201.1":
  version: 0.201.1
  resolution: "@opentelemetry/otlp-grpc-exporter-base@npm:0.201.1"
  dependencies:
    "@grpc/grpc-js": "npm:^1.7.1"
    "@opentelemetry/core": "npm:2.0.1"
    "@opentelemetry/otlp-exporter-base": "npm:0.201.1"
    "@opentelemetry/otlp-transformer": "npm:0.201.1"
  peerDependencies:
    "@opentelemetry/api": ^1.3.0
  checksum: 10c0/3e77e4f6fd3df73f32d7e2a558fffd377ae9afeb24976d4fe1a16cab6a445348450593f5098bf6cdcd5ed98cf777a9f711410eefc796a9b2de14fa5c6c49c760
  languageName: node
  linkType: hard

"@opentelemetry/otlp-grpc-exporter-base@npm:0.52.1":
  version: 0.52.1
  resolution: "@opentelemetry/otlp-grpc-exporter-base@npm:0.52.1"
  dependencies:
    "@grpc/grpc-js": "npm:^1.7.1"
    "@opentelemetry/core": "npm:1.25.1"
    "@opentelemetry/otlp-exporter-base": "npm:0.52.1"
    "@opentelemetry/otlp-transformer": "npm:0.52.1"
  peerDependencies:
    "@opentelemetry/api": ^1.0.0
  checksum: 10c0/8600be0c830d6ba3e956e1a5d001484156d256d0c4ba98ddcb757beca75c0f708a71f9eac52d1890a042ab4cb12f521b227b1bb6a6399c249f4424dd35e32d90
  languageName: node
  linkType: hard

"@opentelemetry/otlp-transformer@npm:0.201.1, @opentelemetry/otlp-transformer@npm:^0.201.1":
  version: 0.201.1
  resolution: "@opentelemetry/otlp-transformer@npm:0.201.1"
  dependencies:
    "@opentelemetry/api-logs": "npm:0.201.1"
    "@opentelemetry/core": "npm:2.0.1"
    "@opentelemetry/resources": "npm:2.0.1"
    "@opentelemetry/sdk-logs": "npm:0.201.1"
    "@opentelemetry/sdk-metrics": "npm:2.0.1"
    "@opentelemetry/sdk-trace-base": "npm:2.0.1"
    protobufjs: "npm:^7.3.0"
  peerDependencies:
    "@opentelemetry/api": ^1.3.0
  checksum: 10c0/c92eaca64c2d2c5299fa3778674e1fc06d58686bbb3e61c80a4dfc190d2d4fed37720e88e9301c5f0bea1c29eaa2e91a0a221d7c48ab0271b924483857e3f679
  languageName: node
  linkType: hard

"@opentelemetry/otlp-transformer@npm:0.52.1":
  version: 0.52.1
  resolution: "@opentelemetry/otlp-transformer@npm:0.52.1"
  dependencies:
    "@opentelemetry/api-logs": "npm:0.52.1"
    "@opentelemetry/core": "npm:1.25.1"
    "@opentelemetry/resources": "npm:1.25.1"
    "@opentelemetry/sdk-logs": "npm:0.52.1"
    "@opentelemetry/sdk-metrics": "npm:1.25.1"
    "@opentelemetry/sdk-trace-base": "npm:1.25.1"
    protobufjs: "npm:^7.3.0"
  peerDependencies:
    "@opentelemetry/api": ">=1.3.0 <1.10.0"
  checksum: 10c0/d391be7652221f85618bc47c0ef33093086e6bf9321a4d5825ae104514f02c4a1d19b63641e33067b0843e16972b0d8c063a6be0a392fec33d9dbf9d39c1c01b
  languageName: node
  linkType: hard

"@opentelemetry/propagation-utils@npm:^0.31.1":
  version: 0.31.2
  resolution: "@opentelemetry/propagation-utils@npm:0.31.2"
  peerDependencies:
    "@opentelemetry/api": ^1.0.0
  checksum: 10c0/c184fdb11199759575c9afff4babeabf36de4be4748cf8649828aefdfa4de6a8df11c302605896005c34130b2272c233e58e176ff2bf63db1cb5482e4f881b12
  languageName: node
  linkType: hard

"@opentelemetry/propagator-b3@npm:1.25.1":
  version: 1.25.1
  resolution: "@opentelemetry/propagator-b3@npm:1.25.1"
  dependencies:
    "@opentelemetry/core": "npm:1.25.1"
  peerDependencies:
    "@opentelemetry/api": ">=1.0.0 <1.10.0"
  checksum: 10c0/3fb91d2d26bdc99188cdfd31f176a38e950292d425a3c1ecd17f55d4ac4926d00da756a123592b3971a5309d7f48c9348183e269f4f8fe7f70c7418ab38dd920
  languageName: node
  linkType: hard

"@opentelemetry/propagator-b3@npm:2.0.1":
  version: 2.0.1
  resolution: "@opentelemetry/propagator-b3@npm:2.0.1"
  dependencies:
    "@opentelemetry/core": "npm:2.0.1"
  peerDependencies:
    "@opentelemetry/api": ">=1.0.0 <1.10.0"
  checksum: 10c0/79dbfaaa867f4f71a22ab640848f797ef9789fd94fc824ca4e38f298968a3f559a895fc228a17f09b1e06ec88cbf0b1f3cadc480ea76848504c7364693fd30ca
  languageName: node
  linkType: hard

"@opentelemetry/propagator-jaeger@npm:1.25.1":
  version: 1.25.1
  resolution: "@opentelemetry/propagator-jaeger@npm:1.25.1"
  dependencies:
    "@opentelemetry/core": "npm:1.25.1"
  peerDependencies:
    "@opentelemetry/api": ">=1.0.0 <1.10.0"
  checksum: 10c0/2fd240fc5e26b986b27f6c140081b13b84a1bc2e6ad8ebfa69d4fda4c2a0f6dfc404ff11965585ddd229550c780650e3e18f56ab6976a79efc0ae0efdcb855a4
  languageName: node
  linkType: hard

"@opentelemetry/propagator-jaeger@npm:2.0.1":
  version: 2.0.1
  resolution: "@opentelemetry/propagator-jaeger@npm:2.0.1"
  dependencies:
    "@opentelemetry/core": "npm:2.0.1"
  peerDependencies:
    "@opentelemetry/api": ">=1.0.0 <1.10.0"
  checksum: 10c0/e21df109b831a7efffe54459bb5da35be05eeb72581017f0ce40dee2ab98b3e8063602894a477f6c593ad1bd3a1ead36adfceee21eb2472ca88050d49f056154
  languageName: node
  linkType: hard

"@opentelemetry/redis-common@npm:^0.37.0":
  version: 0.37.0
  resolution: "@opentelemetry/redis-common@npm:0.37.0"
  checksum: 10c0/8e754164c800c509504f07bb38b8bbf2890eafde65bb5a5ffb8227e1b234cf84bc294671416f27504d90bfd22f334f671db2d7cd146f852f0b6dc7c8d36798c0
  languageName: node
  linkType: hard

"@opentelemetry/resource-detector-alibaba-cloud@npm:^0.31.1":
  version: 0.31.2
  resolution: "@opentelemetry/resource-detector-alibaba-cloud@npm:0.31.2"
  dependencies:
    "@opentelemetry/core": "npm:^2.0.0"
    "@opentelemetry/resources": "npm:^2.0.0"
    "@opentelemetry/semantic-conventions": "npm:^1.27.0"
  peerDependencies:
    "@opentelemetry/api": ^1.0.0
  checksum: 10c0/46974743ec5f731057b5e524ce01fcbaaa34775ece76bb7eedca79ce7dae5a87737208f58712bca2cf67772640a711b80a13a08bf186cb38baf61c9a3b6cf9c9
  languageName: node
  linkType: hard

"@opentelemetry/resource-detector-aws@npm:^2.1.0":
  version: 2.2.0
  resolution: "@opentelemetry/resource-detector-aws@npm:2.2.0"
  dependencies:
    "@opentelemetry/core": "npm:^2.0.0"
    "@opentelemetry/resources": "npm:^2.0.0"
    "@opentelemetry/semantic-conventions": "npm:^1.27.0"
  peerDependencies:
    "@opentelemetry/api": ^1.0.0
  checksum: 10c0/67a21804281b3106a75e029aa1016d8db993eeb857024b1a65841d2c6ae8692b733fe6a75f16131fe5d75b08a517ba5eac0f07d026c068b09ec1c29bbd9c36b7
  languageName: node
  linkType: hard

"@opentelemetry/resource-detector-azure@npm:^0.8.0":
  version: 0.8.0
  resolution: "@opentelemetry/resource-detector-azure@npm:0.8.0"
  dependencies:
    "@opentelemetry/core": "npm:^2.0.0"
    "@opentelemetry/resources": "npm:^2.0.0"
    "@opentelemetry/semantic-conventions": "npm:^1.27.0"
  peerDependencies:
    "@opentelemetry/api": ^1.0.0
  checksum: 10c0/eda387938b09e8199c088c27aae771c728c5303057bb45be97f6aa5c46ac1200eef0472a2592dd89851af6090ae2dc4fb284ccede5e0802721ed3f6215834971
  languageName: node
  linkType: hard

"@opentelemetry/resource-detector-container@npm:^0.7.1":
  version: 0.7.2
  resolution: "@opentelemetry/resource-detector-container@npm:0.7.2"
  dependencies:
    "@opentelemetry/core": "npm:^2.0.0"
    "@opentelemetry/resources": "npm:^2.0.0"
    "@opentelemetry/semantic-conventions": "npm:^1.27.0"
  peerDependencies:
    "@opentelemetry/api": ^1.0.0
  checksum: 10c0/298d2c3955f7f6f604ccfd1ad34fde2c4f50b1cdf1e916033c114c8001c02bc3746791395c6c1aedc024aec45973b0ff95518e743ff041af21d1b0c81d3ffc8a
  languageName: node
  linkType: hard

"@opentelemetry/resource-detector-gcp@npm:^0.35.0":
  version: 0.35.0
  resolution: "@opentelemetry/resource-detector-gcp@npm:0.35.0"
  dependencies:
    "@opentelemetry/core": "npm:^2.0.0"
    "@opentelemetry/resources": "npm:^2.0.0"
    "@opentelemetry/semantic-conventions": "npm:^1.27.0"
    gcp-metadata: "npm:^6.0.0"
  peerDependencies:
    "@opentelemetry/api": ^1.0.0
  checksum: 10c0/410a5b0657c92e1fbece3f7c378121497ac8ff57cfd22f5661617f6aa33ef3b656057b795a07fe873776ada6b6f95bbd1a8726fa16096c21f91bbc35549ebf48
  languageName: node
  linkType: hard

"@opentelemetry/resources@npm:1.25.1":
  version: 1.25.1
  resolution: "@opentelemetry/resources@npm:1.25.1"
  dependencies:
    "@opentelemetry/core": "npm:1.25.1"
    "@opentelemetry/semantic-conventions": "npm:1.25.1"
  peerDependencies:
    "@opentelemetry/api": ">=1.0.0 <1.10.0"
  checksum: 10c0/4edbf04945c7647b9af847f2f8abccabb54f4f8935fd75c199dc22879f8b7927ac50fac8e877ef48e81c586a08d63bbfe41c345caf94a8ce2c623fa99bb8e999
  languageName: node
  linkType: hard

"@opentelemetry/resources@npm:2.0.1, @opentelemetry/resources@npm:^2.0.0, @opentelemetry/resources@npm:^2.0.1":
  version: 2.0.1
  resolution: "@opentelemetry/resources@npm:2.0.1"
  dependencies:
    "@opentelemetry/core": "npm:2.0.1"
    "@opentelemetry/semantic-conventions": "npm:^1.29.0"
  peerDependencies:
    "@opentelemetry/api": ">=1.3.0 <1.10.0"
  checksum: 10c0/96532b7553b26607a7a892d72f6b03ad12bd542dc23c95135a8ae40362da9c883c21a4cff3d2296d9e0e9bd899a5977e325ed52d83142621a8ffe81d08d99341
  languageName: node
  linkType: hard

"@opentelemetry/sdk-logs@npm:0.201.1":
  version: 0.201.1
  resolution: "@opentelemetry/sdk-logs@npm:0.201.1"
  dependencies:
    "@opentelemetry/api-logs": "npm:0.201.1"
    "@opentelemetry/core": "npm:2.0.1"
    "@opentelemetry/resources": "npm:2.0.1"
  peerDependencies:
    "@opentelemetry/api": ">=1.4.0 <1.10.0"
  checksum: 10c0/10ebac2d27802c6708cd38517dba847a629236fb32edf6a1f984b60580e884c7b4be8c6e70def6c3a328d581f5f780fbf09b934eba4e9aabfe9248be619f7341
  languageName: node
  linkType: hard

"@opentelemetry/sdk-logs@npm:0.52.1":
  version: 0.52.1
  resolution: "@opentelemetry/sdk-logs@npm:0.52.1"
  dependencies:
    "@opentelemetry/api-logs": "npm:0.52.1"
    "@opentelemetry/core": "npm:1.25.1"
    "@opentelemetry/resources": "npm:1.25.1"
  peerDependencies:
    "@opentelemetry/api": ">=1.4.0 <1.10.0"
  checksum: 10c0/4d373c1e8db867ab2670f04b8463a9f7a1d5a38ffd5c6c0e08c415df6d0a7b36747e37df7c36f700ed0fba3083ecaaa7b9e705a4ff5fe178e0f3e5fd90e8278d
  languageName: node
  linkType: hard

"@opentelemetry/sdk-metrics@npm:1.25.1":
  version: 1.25.1
  resolution: "@opentelemetry/sdk-metrics@npm:1.25.1"
  dependencies:
    "@opentelemetry/core": "npm:1.25.1"
    "@opentelemetry/resources": "npm:1.25.1"
    lodash.merge: "npm:^4.6.2"
  peerDependencies:
    "@opentelemetry/api": ">=1.3.0 <1.10.0"
  checksum: 10c0/e27d693e2e34dfeadc4632f771a2f7aca7266f7be6d159bb488bb9cdd68edd5a3fca1ecb0cc3a703a61f0f95fbf806d48e5711052519d50d7d235eedb9ce22ae
  languageName: node
  linkType: hard

"@opentelemetry/sdk-metrics@npm:2.0.1, @opentelemetry/sdk-metrics@npm:^2.0.1":
  version: 2.0.1
  resolution: "@opentelemetry/sdk-metrics@npm:2.0.1"
  dependencies:
    "@opentelemetry/core": "npm:2.0.1"
    "@opentelemetry/resources": "npm:2.0.1"
  peerDependencies:
    "@opentelemetry/api": ">=1.9.0 <1.10.0"
  checksum: 10c0/fcf7ae23d459e5da7cb6fe150064b6dc4e11e47925b08980c3b357bd5534ad388898bbacd0ff8befef6801f43b35142dc7123f028ffde2d0fe2bd72177d07639
  languageName: node
  linkType: hard

"@opentelemetry/sdk-node@npm:0.52.1":
  version: 0.52.1
  resolution: "@opentelemetry/sdk-node@npm:0.52.1"
  dependencies:
    "@opentelemetry/api-logs": "npm:0.52.1"
    "@opentelemetry/core": "npm:1.25.1"
    "@opentelemetry/exporter-trace-otlp-grpc": "npm:0.52.1"
    "@opentelemetry/exporter-trace-otlp-http": "npm:0.52.1"
    "@opentelemetry/exporter-trace-otlp-proto": "npm:0.52.1"
    "@opentelemetry/exporter-zipkin": "npm:1.25.1"
    "@opentelemetry/instrumentation": "npm:0.52.1"
    "@opentelemetry/resources": "npm:1.25.1"
    "@opentelemetry/sdk-logs": "npm:0.52.1"
    "@opentelemetry/sdk-metrics": "npm:1.25.1"
    "@opentelemetry/sdk-trace-base": "npm:1.25.1"
    "@opentelemetry/sdk-trace-node": "npm:1.25.1"
    "@opentelemetry/semantic-conventions": "npm:1.25.1"
  peerDependencies:
    "@opentelemetry/api": ">=1.3.0 <1.10.0"
  checksum: 10c0/9bdeb5ec6d63d918e49641eaa4670191763ac88d61a323ccf4b1bee009bbf4408dff822cc72f5b471d650eed30d142caa322de20ee5bef2e8a9eef230e8255d3
  languageName: node
  linkType: hard

"@opentelemetry/sdk-node@npm:^0.201.0, @opentelemetry/sdk-node@npm:^0.201.1":
  version: 0.201.1
  resolution: "@opentelemetry/sdk-node@npm:0.201.1"
  dependencies:
    "@opentelemetry/api-logs": "npm:0.201.1"
    "@opentelemetry/core": "npm:2.0.1"
    "@opentelemetry/exporter-logs-otlp-grpc": "npm:0.201.1"
    "@opentelemetry/exporter-logs-otlp-http": "npm:0.201.1"
    "@opentelemetry/exporter-logs-otlp-proto": "npm:0.201.1"
    "@opentelemetry/exporter-metrics-otlp-grpc": "npm:0.201.1"
    "@opentelemetry/exporter-metrics-otlp-http": "npm:0.201.1"
    "@opentelemetry/exporter-metrics-otlp-proto": "npm:0.201.1"
    "@opentelemetry/exporter-prometheus": "npm:0.201.1"
    "@opentelemetry/exporter-trace-otlp-grpc": "npm:0.201.1"
    "@opentelemetry/exporter-trace-otlp-http": "npm:0.201.1"
    "@opentelemetry/exporter-trace-otlp-proto": "npm:0.201.1"
    "@opentelemetry/exporter-zipkin": "npm:2.0.1"
    "@opentelemetry/instrumentation": "npm:0.201.1"
    "@opentelemetry/propagator-b3": "npm:2.0.1"
    "@opentelemetry/propagator-jaeger": "npm:2.0.1"
    "@opentelemetry/resources": "npm:2.0.1"
    "@opentelemetry/sdk-logs": "npm:0.201.1"
    "@opentelemetry/sdk-metrics": "npm:2.0.1"
    "@opentelemetry/sdk-trace-base": "npm:2.0.1"
    "@opentelemetry/sdk-trace-node": "npm:2.0.1"
    "@opentelemetry/semantic-conventions": "npm:^1.29.0"
  peerDependencies:
    "@opentelemetry/api": ">=1.3.0 <1.10.0"
  checksum: 10c0/002d5a79ae6b0f93244d0234d41872596642e40f1beba4a079f27ffbcf5d995dbfe79306b60c22f39b7ff3c7d4f1a3cd5547da24ecc02d94ea25ce75d29bd704
  languageName: node
  linkType: hard

"@opentelemetry/sdk-trace-base@npm:1.25.1":
  version: 1.25.1
  resolution: "@opentelemetry/sdk-trace-base@npm:1.25.1"
  dependencies:
    "@opentelemetry/core": "npm:1.25.1"
    "@opentelemetry/resources": "npm:1.25.1"
    "@opentelemetry/semantic-conventions": "npm:1.25.1"
  peerDependencies:
    "@opentelemetry/api": ">=1.0.0 <1.10.0"
  checksum: 10c0/bcbc5de75edb8f36a05c7d21699782b4aa100482588d89e318d3f35944d45e776f50f7b353273a0925bc0b3b6e82cbf294cba4cb0792d951148b4ee105280aa2
  languageName: node
  linkType: hard

"@opentelemetry/sdk-trace-base@npm:2.0.1, @opentelemetry/sdk-trace-base@npm:^2.0.1":
  version: 2.0.1
  resolution: "@opentelemetry/sdk-trace-base@npm:2.0.1"
  dependencies:
    "@opentelemetry/core": "npm:2.0.1"
    "@opentelemetry/resources": "npm:2.0.1"
    "@opentelemetry/semantic-conventions": "npm:^1.29.0"
  peerDependencies:
    "@opentelemetry/api": ">=1.3.0 <1.10.0"
  checksum: 10c0/4e3c733296012b758d007e9c0d8a5b175edbe9a680c73ec75303476e7982b73ad4209f1a2791c1a94c428e5a53eba6c2a72faa430c70336005aa58744d6cb37b
  languageName: node
  linkType: hard

"@opentelemetry/sdk-trace-node@npm:1.25.1":
  version: 1.25.1
  resolution: "@opentelemetry/sdk-trace-node@npm:1.25.1"
  dependencies:
    "@opentelemetry/context-async-hooks": "npm:1.25.1"
    "@opentelemetry/core": "npm:1.25.1"
    "@opentelemetry/propagator-b3": "npm:1.25.1"
    "@opentelemetry/propagator-jaeger": "npm:1.25.1"
    "@opentelemetry/sdk-trace-base": "npm:1.25.1"
    semver: "npm:^7.5.2"
  peerDependencies:
    "@opentelemetry/api": ">=1.0.0 <1.10.0"
  checksum: 10c0/d54cdab39429bd5ddb4d3dcb27e2423039680f09c3059b406348289ad8ed2856cf0a6db592d55e5a45afde233dd1867ad5daf5ac47e5f080562a561634a1565a
  languageName: node
  linkType: hard

"@opentelemetry/sdk-trace-node@npm:2.0.1, @opentelemetry/sdk-trace-node@npm:^2.0.1":
  version: 2.0.1
  resolution: "@opentelemetry/sdk-trace-node@npm:2.0.1"
  dependencies:
    "@opentelemetry/context-async-hooks": "npm:2.0.1"
    "@opentelemetry/core": "npm:2.0.1"
    "@opentelemetry/sdk-trace-base": "npm:2.0.1"
  peerDependencies:
    "@opentelemetry/api": ">=1.0.0 <1.10.0"
  checksum: 10c0/b237efc219dc10c33746c05461c8c8741edbe7558eaf7f2dab01a3e75af4788bfd0633a049cd5dc7ecf015a2de7aa948c3989c0131d1f140109fb5e7b0313d7a
  languageName: node
  linkType: hard

"@opentelemetry/semantic-conventions@npm:1.25.1":
  version: 1.25.1
  resolution: "@opentelemetry/semantic-conventions@npm:1.25.1"
  checksum: 10c0/fb1d6349e91f142c82931e89e0242215be8248e77919b6faa7e259757e499183546c9b4046de72b053b5222453bc74fff70280d2b4d1229484ba7b2c07f16a3a
  languageName: node
  linkType: hard

"@opentelemetry/semantic-conventions@npm:^1.27.0, @opentelemetry/semantic-conventions@npm:^1.29.0, @opentelemetry/semantic-conventions@npm:^1.30.0, @opentelemetry/semantic-conventions@npm:^1.31.0, @opentelemetry/semantic-conventions@npm:^1.34.0":
  version: 1.34.0
  resolution: "@opentelemetry/semantic-conventions@npm:1.34.0"
  checksum: 10c0/a51a32a5cf5c803bd2125a680d0abacbff632f3b255d0fe52379dac191114a0e8d72a34f9c46c5483ccfe91c4061c309f3cf61a19d11347e2a69779e82cfefd0
  languageName: node
  linkType: hard

"@opentelemetry/sql-common@npm:^0.41.0":
  version: 0.41.0
  resolution: "@opentelemetry/sql-common@npm:0.41.0"
  dependencies:
    "@opentelemetry/core": "npm:^2.0.0"
  peerDependencies:
    "@opentelemetry/api": ^1.1.0
  checksum: 10c0/3611fa2766be809d3f19f1c032373349962c3203cbc77839ea59d78a4ce2bf6954da9b163efdde386f3f4a8ec77c50c985f9e5b8b3df6e7fccd2990cf861bca4
  languageName: node
  linkType: hard

"@paralleldrive/cuid2@npm:^2.2.2":
  version: 2.2.2
  resolution: "@paralleldrive/cuid2@npm:2.2.2"
  dependencies:
    "@noble/hashes": "npm:^1.1.5"
  checksum: 10c0/af5826df93de437121308f4f4ce0b2eeb89b60bb57a1a6592fb89c0d40d311ad1d9f3f6a4db2cce6f2bcf572de1aa3f85704254e89b18ce61c41ebb06564c4ee
  languageName: node
  linkType: hard

"@pkgjs/parseargs@npm:^0.11.0":
  version: 0.11.0
  resolution: "@pkgjs/parseargs@npm:0.11.0"
  checksum: 10c0/5bd7576bb1b38a47a7fc7b51ac9f38748e772beebc56200450c4a817d712232b8f1d3ef70532c80840243c657d491cf6a6be1e3a214cff907645819fdc34aadd
  languageName: node
  linkType: hard

"@protobufjs/aspromise@npm:^1.1.1, @protobufjs/aspromise@npm:^1.1.2":
  version: 1.1.2
  resolution: "@protobufjs/aspromise@npm:1.1.2"
  checksum: 10c0/a83343a468ff5b5ec6bff36fd788a64c839e48a07ff9f4f813564f58caf44d011cd6504ed2147bf34835bd7a7dd2107052af755961c6b098fd8902b4f6500d0f
  languageName: node
  linkType: hard

"@protobufjs/base64@npm:^1.1.2":
  version: 1.1.2
  resolution: "@protobufjs/base64@npm:1.1.2"
  checksum: 10c0/eec925e681081af190b8ee231f9bad3101e189abbc182ff279da6b531e7dbd2a56f1f306f37a80b1be9e00aa2d271690d08dcc5f326f71c9eed8546675c8caf6
  languageName: node
  linkType: hard

"@protobufjs/codegen@npm:^2.0.4":
  version: 2.0.4
  resolution: "@protobufjs/codegen@npm:2.0.4"
  checksum: 10c0/26ae337c5659e41f091606d16465bbcc1df1f37cc1ed462438b1f67be0c1e28dfb2ca9f294f39100c52161aef82edf758c95d6d75650a1ddf31f7ddee1440b43
  languageName: node
  linkType: hard

"@protobufjs/eventemitter@npm:^1.1.0":
  version: 1.1.0
  resolution: "@protobufjs/eventemitter@npm:1.1.0"
  checksum: 10c0/1eb0a75180e5206d1033e4138212a8c7089a3d418c6dfa5a6ce42e593a4ae2e5892c4ef7421f38092badba4040ea6a45f0928869989411001d8c1018ea9a6e70
  languageName: node
  linkType: hard

"@protobufjs/fetch@npm:^1.1.0":
  version: 1.1.0
  resolution: "@protobufjs/fetch@npm:1.1.0"
  dependencies:
    "@protobufjs/aspromise": "npm:^1.1.1"
    "@protobufjs/inquire": "npm:^1.1.0"
  checksum: 10c0/cda6a3dc2d50a182c5865b160f72077aac197046600091dbb005dd0a66db9cce3c5eaed6d470ac8ed49d7bcbeef6ee5f0bc288db5ff9a70cbd003e5909065233
  languageName: node
  linkType: hard

"@protobufjs/float@npm:^1.0.2":
  version: 1.0.2
  resolution: "@protobufjs/float@npm:1.0.2"
  checksum: 10c0/18f2bdede76ffcf0170708af15c9c9db6259b771e6b84c51b06df34a9c339dbbeec267d14ce0bddd20acc142b1d980d983d31434398df7f98eb0c94a0eb79069
  languageName: node
  linkType: hard

"@protobufjs/inquire@npm:^1.1.0":
  version: 1.1.0
  resolution: "@protobufjs/inquire@npm:1.1.0"
  checksum: 10c0/64372482efcba1fb4d166a2664a6395fa978b557803857c9c03500e0ac1013eb4b1aacc9ed851dd5fc22f81583670b4f4431bae186f3373fedcfde863ef5921a
  languageName: node
  linkType: hard

"@protobufjs/path@npm:^1.1.2":
  version: 1.1.2
  resolution: "@protobufjs/path@npm:1.1.2"
  checksum: 10c0/cece0a938e7f5dfd2fa03f8c14f2f1cf8b0d6e13ac7326ff4c96ea311effd5fb7ae0bba754fbf505312af2e38500250c90e68506b97c02360a43793d88a0d8b4
  languageName: node
  linkType: hard

"@protobufjs/pool@npm:^1.1.0":
  version: 1.1.0
  resolution: "@protobufjs/pool@npm:1.1.0"
  checksum: 10c0/eda2718b7f222ac6e6ad36f758a92ef90d26526026a19f4f17f668f45e0306a5bd734def3f48f51f8134ae0978b6262a5c517c08b115a551756d1a3aadfcf038
  languageName: node
  linkType: hard

"@protobufjs/utf8@npm:^1.1.0":
  version: 1.1.0
  resolution: "@protobufjs/utf8@npm:1.1.0"
  checksum: 10c0/a3fe31fe3fa29aa3349e2e04ee13dc170cc6af7c23d92ad49e3eeaf79b9766264544d3da824dba93b7855bd6a2982fb40032ef40693da98a136d835752beb487
  languageName: node
  linkType: hard

"@redis/bloom@npm:1.2.0":
  version: 1.2.0
  resolution: "@redis/bloom@npm:1.2.0"
  peerDependencies:
    "@redis/client": ^1.0.0
  checksum: 10c0/7dde8e67188164e96226c8a5c78ebd2801f1662947371e78fb95fb180c1e9ddff8d237012eb5e9182775be61cb546f67f759927cdaee0d178d863ee290e1fb27
  languageName: node
  linkType: hard

"@redis/client@npm:1.6.1":
  version: 1.6.1
  resolution: "@redis/client@npm:1.6.1"
  dependencies:
    cluster-key-slot: "npm:1.1.2"
    generic-pool: "npm:3.9.0"
    yallist: "npm:4.0.0"
  checksum: 10c0/216c61f5aa2fef212386c2ef5b5f6d10f44244f6928682f370e190402d23338e11260377c08e87dd6d678408fa7c0a6b7bb5571ecadb830abfa3d7355b9eff1e
  languageName: node
  linkType: hard

"@redis/graph@npm:1.1.1":
  version: 1.1.1
  resolution: "@redis/graph@npm:1.1.1"
  peerDependencies:
    "@redis/client": ^1.0.0
  checksum: 10c0/64199db2cb3669c4911af8aba3b7116c4c2c1df37ca74b2a65555e62c863935a0cea74bc41bd92acf2e551074eb2a30c75f54a9f439b40e0f9bb67fc5fb66614
  languageName: node
  linkType: hard

"@redis/json@npm:1.0.7":
  version: 1.0.7
  resolution: "@redis/json@npm:1.0.7"
  peerDependencies:
    "@redis/client": ^1.0.0
  checksum: 10c0/cef473711d66f7568a16edbd728acca7d237cfeaa15e0326b5b628dfab4afc0c76c7354e7f8efad6ecc64a1cb774e4aa060ee46497b633e18ba0a2f0aace1cc4
  languageName: node
  linkType: hard

"@redis/search@npm:1.2.0":
  version: 1.2.0
  resolution: "@redis/search@npm:1.2.0"
  peerDependencies:
    "@redis/client": ^1.0.0
  checksum: 10c0/01d57ac10d2c5698e04e4a2f945440db3087e8834643ca950c099879dbcd77526604ca6f5c2ee883dfd4b337b0a24cb7d81ac56845aa83f89a4f161362a08dc6
  languageName: node
  linkType: hard

"@redis/time-series@npm:1.1.0":
  version: 1.1.0
  resolution: "@redis/time-series@npm:1.1.0"
  peerDependencies:
    "@redis/client": ^1.0.0
  checksum: 10c0/503d0d5cbc9113d26666bb7b4dea57619badbcdfeee0369abf647250f26c5482ed5827c83f88f9f0cf22e021e3e7cb562459669d733fac05652972e208d6ba0f
  languageName: node
  linkType: hard

"@rollup/plugin-alias@npm:^5.1.1":
  version: 5.1.1
  resolution: "@rollup/plugin-alias@npm:5.1.1"
  peerDependencies:
    rollup: ^1.20.0||^2.0.0||^3.0.0||^4.0.0
  peerDependenciesMeta:
    rollup:
      optional: true
  checksum: 10c0/00592400563b65689631e820bd72ff440f5cd21021bbd2f21b8558582ab58fd109067da77000091e40fcb8c20cabcd3a09b239a30e012bb47f6bc1a15b68ca59
  languageName: node
  linkType: hard

"@rollup/plugin-commonjs@npm:^28.0.5":
  version: 28.0.6
  resolution: "@rollup/plugin-commonjs@npm:28.0.6"
  dependencies:
    "@rollup/pluginutils": "npm:^5.0.1"
    commondir: "npm:^1.0.1"
    estree-walker: "npm:^2.0.2"
    fdir: "npm:^6.2.0"
    is-reference: "npm:1.2.1"
    magic-string: "npm:^0.30.3"
    picomatch: "npm:^4.0.2"
  peerDependencies:
    rollup: ^2.68.0||^3.0.0||^4.0.0
  peerDependenciesMeta:
    rollup:
      optional: true
  checksum: 10c0/67fa297384c2494c8f85df102c030e7f8ed8f600cfccdd1143266112ee4037d37faa1bda44a571dab35b48297342024551e995ad2f8a4d86da0aa1f33ec61868
  languageName: node
  linkType: hard

"@rollup/plugin-json@npm:^6.1.0":
  version: 6.1.0
  resolution: "@rollup/plugin-json@npm:6.1.0"
  dependencies:
    "@rollup/pluginutils": "npm:^5.1.0"
  peerDependencies:
    rollup: ^1.20.0||^2.0.0||^3.0.0||^4.0.0
  peerDependenciesMeta:
    rollup:
      optional: true
  checksum: 10c0/9400c431b5e0cf3088ba2eb2d038809a2b0fb2a84ed004997da85582f48cd64958ed3168893c4f2c8109e38652400ed68282d0c92bf8ec07a3b2ef2e1ceab0b7
  languageName: node
  linkType: hard

"@rollup/plugin-node-resolve@npm:^16.0.1":
  version: 16.0.1
  resolution: "@rollup/plugin-node-resolve@npm:16.0.1"
  dependencies:
    "@rollup/pluginutils": "npm:^5.0.1"
    "@types/resolve": "npm:1.20.2"
    deepmerge: "npm:^4.2.2"
    is-module: "npm:^1.0.0"
    resolve: "npm:^1.22.1"
  peerDependencies:
    rollup: ^2.78.0||^3.0.0||^4.0.0
  peerDependenciesMeta:
    rollup:
      optional: true
  checksum: 10c0/54d33282321492fafec29b49c66dd1efd90c72a24f9d1569dcb57a72ab8de8a782810f39fdb917b96ec6a598c18f3416588b419bf7af331793a010de1fe28c60
  languageName: node
  linkType: hard

"@rollup/plugin-virtual@npm:^3.0.2":
  version: 3.0.2
  resolution: "@rollup/plugin-virtual@npm:3.0.2"
  peerDependencies:
    rollup: ^1.20.0||^2.0.0||^3.0.0||^4.0.0
  peerDependenciesMeta:
    rollup:
      optional: true
  checksum: 10c0/7115edb7989096d1ce334939fcf6e1ba365586b487bf61b2dd4f915386197f350db70904030342c0720fe58f5a52828975c645c4d415c1d432d9b1b6760a22ef
  languageName: node
  linkType: hard

"@rollup/pluginutils@npm:^5.0.1, @rollup/pluginutils@npm:^5.1.0":
  version: 5.2.0
  resolution: "@rollup/pluginutils@npm:5.2.0"
  dependencies:
    "@types/estree": "npm:^1.0.0"
    estree-walker: "npm:^2.0.2"
    picomatch: "npm:^4.0.2"
  peerDependencies:
    rollup: ^1.20.0||^2.0.0||^3.0.0||^4.0.0
  peerDependenciesMeta:
    rollup:
      optional: true
  checksum: 10c0/794890d512751451bcc06aa112366ef47ea8f9125dac49b1abf72ff8b079518b09359de9c60a013b33266541634e765ae61839c749fae0edb59a463418665c55
  languageName: node
  linkType: hard

"@rollup/rollup-android-arm-eabi@npm:4.44.0":
  version: 4.44.0
  resolution: "@rollup/rollup-android-arm-eabi@npm:4.44.0"
  conditions: os=android & cpu=arm
  languageName: node
  linkType: hard

"@rollup/rollup-android-arm64@npm:4.44.0":
  version: 4.44.0
  resolution: "@rollup/rollup-android-arm64@npm:4.44.0"
  conditions: os=android & cpu=arm64
  languageName: node
  linkType: hard

"@rollup/rollup-darwin-arm64@npm:4.44.0, @rollup/rollup-darwin-arm64@npm:^4.18.1":
  version: 4.44.0
  resolution: "@rollup/rollup-darwin-arm64@npm:4.44.0"
  conditions: os=darwin & cpu=arm64
  languageName: node
  linkType: hard

"@rollup/rollup-darwin-x64@npm:4.44.0":
  version: 4.44.0
  resolution: "@rollup/rollup-darwin-x64@npm:4.44.0"
  conditions: os=darwin & cpu=x64
  languageName: node
  linkType: hard

"@rollup/rollup-freebsd-arm64@npm:4.44.0":
  version: 4.44.0
  resolution: "@rollup/rollup-freebsd-arm64@npm:4.44.0"
  conditions: os=freebsd & cpu=arm64
  languageName: node
  linkType: hard

"@rollup/rollup-freebsd-x64@npm:4.44.0":
  version: 4.44.0
  resolution: "@rollup/rollup-freebsd-x64@npm:4.44.0"
  conditions: os=freebsd & cpu=x64
  languageName: node
  linkType: hard

"@rollup/rollup-linux-arm-gnueabihf@npm:4.44.0":
  version: 4.44.0
  resolution: "@rollup/rollup-linux-arm-gnueabihf@npm:4.44.0"
  conditions: os=linux & cpu=arm & libc=glibc
  languageName: node
  linkType: hard

"@rollup/rollup-linux-arm-musleabihf@npm:4.44.0":
  version: 4.44.0
  resolution: "@rollup/rollup-linux-arm-musleabihf@npm:4.44.0"
  conditions: os=linux & cpu=arm & libc=musl
  languageName: node
  linkType: hard

"@rollup/rollup-linux-arm64-gnu@npm:4.44.0":
  version: 4.44.0
  resolution: "@rollup/rollup-linux-arm64-gnu@npm:4.44.0"
  conditions: os=linux & cpu=arm64 & libc=glibc
  languageName: node
  linkType: hard

"@rollup/rollup-linux-arm64-musl@npm:4.44.0":
  version: 4.44.0
  resolution: "@rollup/rollup-linux-arm64-musl@npm:4.44.0"
  conditions: os=linux & cpu=arm64 & libc=musl
  languageName: node
  linkType: hard

"@rollup/rollup-linux-loongarch64-gnu@npm:4.44.0":
  version: 4.44.0
  resolution: "@rollup/rollup-linux-loongarch64-gnu@npm:4.44.0"
  conditions: os=linux & cpu=loong64 & libc=glibc
  languageName: node
  linkType: hard

"@rollup/rollup-linux-powerpc64le-gnu@npm:4.44.0":
  version: 4.44.0
  resolution: "@rollup/rollup-linux-powerpc64le-gnu@npm:4.44.0"
  conditions: os=linux & cpu=ppc64 & libc=glibc
  languageName: node
  linkType: hard

"@rollup/rollup-linux-riscv64-gnu@npm:4.44.0":
  version: 4.44.0
  resolution: "@rollup/rollup-linux-riscv64-gnu@npm:4.44.0"
  conditions: os=linux & cpu=riscv64 & libc=glibc
  languageName: node
  linkType: hard

"@rollup/rollup-linux-riscv64-musl@npm:4.44.0":
  version: 4.44.0
  resolution: "@rollup/rollup-linux-riscv64-musl@npm:4.44.0"
  conditions: os=linux & cpu=riscv64 & libc=musl
  languageName: node
  linkType: hard

"@rollup/rollup-linux-s390x-gnu@npm:4.44.0":
  version: 4.44.0
  resolution: "@rollup/rollup-linux-s390x-gnu@npm:4.44.0"
  conditions: os=linux & cpu=s390x & libc=glibc
  languageName: node
  linkType: hard

"@rollup/rollup-linux-x64-gnu@npm:4.44.0":
  version: 4.44.0
  resolution: "@rollup/rollup-linux-x64-gnu@npm:4.44.0"
  conditions: os=linux & cpu=x64 & libc=glibc
  languageName: node
  linkType: hard

"@rollup/rollup-linux-x64-musl@npm:4.44.0":
  version: 4.44.0
  resolution: "@rollup/rollup-linux-x64-musl@npm:4.44.0"
  conditions: os=linux & cpu=x64 & libc=musl
  languageName: node
  linkType: hard

"@rollup/rollup-win32-arm64-msvc@npm:4.44.0":
  version: 4.44.0
  resolution: "@rollup/rollup-win32-arm64-msvc@npm:4.44.0"
  conditions: os=win32 & cpu=arm64
  languageName: node
  linkType: hard

"@rollup/rollup-win32-ia32-msvc@npm:4.44.0":
  version: 4.44.0
  resolution: "@rollup/rollup-win32-ia32-msvc@npm:4.44.0"
  conditions: os=win32 & cpu=ia32
  languageName: node
  linkType: hard

"@rollup/rollup-win32-x64-msvc@npm:4.44.0":
  version: 4.44.0
  resolution: "@rollup/rollup-win32-x64-msvc@npm:4.44.0"
  conditions: os=win32 & cpu=x64
  languageName: node
  linkType: hard

"@sec-ant/readable-stream@npm:^0.4.1":
  version: 0.4.1
  resolution: "@sec-ant/readable-stream@npm:0.4.1"
  checksum: 10c0/64e9e9cf161e848067a5bf60cdc04d18495dc28bb63a8d9f8993e4dd99b91ad34e4b563c85de17d91ffb177ec17a0664991d2e115f6543e73236a906068987af
  languageName: node
  linkType: hard

"@shikijs/core@npm:1.29.2":
  version: 1.29.2
  resolution: "@shikijs/core@npm:1.29.2"
  dependencies:
    "@shikijs/engine-javascript": "npm:1.29.2"
    "@shikijs/engine-oniguruma": "npm:1.29.2"
    "@shikijs/types": "npm:1.29.2"
    "@shikijs/vscode-textmate": "npm:^10.0.1"
    "@types/hast": "npm:^3.0.4"
    hast-util-to-html: "npm:^9.0.4"
  checksum: 10c0/b1bb0567babcee64608224d652ceb4076d387b409fb8ee767f7684c68f03cfaab0e17f42d0a3372fc7be1fe165af9a3a349efc188f6e7c720d4df1108c1ab78c
  languageName: node
  linkType: hard

"@shikijs/engine-javascript@npm:1.29.2":
  version: 1.29.2
  resolution: "@shikijs/engine-javascript@npm:1.29.2"
  dependencies:
    "@shikijs/types": "npm:1.29.2"
    "@shikijs/vscode-textmate": "npm:^10.0.1"
    oniguruma-to-es: "npm:^2.2.0"
  checksum: 10c0/b61f9e9079493c19419ff64af6454c4360a32785d47f49b41e87752e66ddbf7466dd9cce67f4d5d4a8447e31d96b4f0a39330e9f26e8bd2bc2f076644e78dff7
  languageName: node
  linkType: hard

"@shikijs/engine-oniguruma@npm:1.29.2":
  version: 1.29.2
  resolution: "@shikijs/engine-oniguruma@npm:1.29.2"
  dependencies:
    "@shikijs/types": "npm:1.29.2"
    "@shikijs/vscode-textmate": "npm:^10.0.1"
  checksum: 10c0/87d77e05af7fe862df40899a7034cbbd48d3635e27706873025e5035be578584d012f850208e97ca484d5e876bf802d4e23d0394d25026adb678eeb1d1f340ff
  languageName: node
  linkType: hard

"@shikijs/langs@npm:1.29.2":
  version: 1.29.2
  resolution: "@shikijs/langs@npm:1.29.2"
  dependencies:
    "@shikijs/types": "npm:1.29.2"
  checksum: 10c0/137af52ec19ab10bb167ec67e2dc6888d77dedddb3be37708569cb8e8d54c057d09df335261276012d11ac38366ba57b9eae121cc0b7045859638c25648b0563
  languageName: node
  linkType: hard

"@shikijs/themes@npm:1.29.2":
  version: 1.29.2
  resolution: "@shikijs/themes@npm:1.29.2"
  dependencies:
    "@shikijs/types": "npm:1.29.2"
  checksum: 10c0/1f7d3fc8615890d83b50c73c13e5182438dee579dd9a121d605bbdcc2dc877cafc9f7e23a3e1342345cd0b9161e3af6425b0fbfac949843f22b2a60527a8fb69
  languageName: node
  linkType: hard

"@shikijs/types@npm:1.29.2":
  version: 1.29.2
  resolution: "@shikijs/types@npm:1.29.2"
  dependencies:
    "@shikijs/vscode-textmate": "npm:^10.0.1"
    "@types/hast": "npm:^3.0.4"
  checksum: 10c0/37b4ac315effc03e7185aca1da0c2631ac55bdf613897476bd1d879105c41f86ccce6ebd0b78779513d88cc2ee371039f7efd95d604f77f21f180791978822b3
  languageName: node
  linkType: hard

"@shikijs/vscode-textmate@npm:^10.0.1":
  version: 10.0.2
  resolution: "@shikijs/vscode-textmate@npm:10.0.2"
  checksum: 10c0/36b682d691088ec244de292dc8f91b808f95c89466af421cf84cbab92230f03c8348649c14b3251991b10ce632b0c715e416e992dd5f28ff3221dc2693fd9462
  languageName: node
  linkType: hard

"@sinclair/typebox@npm:^0.27.8":
  version: 0.27.8
  resolution: "@sinclair/typebox@npm:0.27.8"
  checksum: 10c0/ef6351ae073c45c2ac89494dbb3e1f87cc60a93ce4cde797b782812b6f97da0d620ae81973f104b43c9b7eaa789ad20ba4f6a1359f1cc62f63729a55a7d22d4e
  languageName: node
  linkType: hard

"@sindresorhus/merge-streams@npm:^2.1.0":
  version: 2.3.0
  resolution: "@sindresorhus/merge-streams@npm:2.3.0"
  checksum: 10c0/69ee906f3125fb2c6bb6ec5cdd84e8827d93b49b3892bce8b62267116cc7e197b5cccf20c160a1d32c26014ecd14470a72a5e3ee37a58f1d6dadc0db1ccf3894
  languageName: node
  linkType: hard

"@sindresorhus/merge-streams@npm:^4.0.0":
  version: 4.0.0
  resolution: "@sindresorhus/merge-streams@npm:4.0.0"
  checksum: 10c0/482ee543629aa1933b332f811a1ae805a213681ecdd98c042b1c1b89387df63e7812248bb4df3910b02b3cc5589d3d73e4393f30e197c9dde18046ccd471fc6b
  languageName: node
  linkType: hard

"@sindresorhus/slugify@npm:^2.2.1":
  version: 2.2.1
  resolution: "@sindresorhus/slugify@npm:2.2.1"
  dependencies:
    "@sindresorhus/transliterate": "npm:^1.0.0"
    escape-string-regexp: "npm:^5.0.0"
  checksum: 10c0/c3fe41d917347f0e2a1e25a48225afffde8ef379a26217e749d5267e965f564c6a555fa17475b637d6fd84645f42e1e4b530477b57110fa80428024a0fadba25
  languageName: node
  linkType: hard

"@sindresorhus/transliterate@npm:^1.0.0":
  version: 1.6.0
  resolution: "@sindresorhus/transliterate@npm:1.6.0"
  dependencies:
    escape-string-regexp: "npm:^5.0.0"
  checksum: 10c0/c5552abd98eb4ab3a8653ccb7addf24e0b6f2aa2a4c420689033f8c9d292abb2222fc08e330adf4055580ac78fe810b7467ed012cdf38f4d64175c42571b8b15
  languageName: node
  linkType: hard

"@sinonjs/commons@npm:^3.0.0":
  version: 3.0.1
  resolution: "@sinonjs/commons@npm:3.0.1"
  dependencies:
    type-detect: "npm:4.0.8"
  checksum: 10c0/1227a7b5bd6c6f9584274db996d7f8cee2c8c350534b9d0141fc662eaf1f292ea0ae3ed19e5e5271c8fd390d27e492ca2803acd31a1978be2cdc6be0da711403
  languageName: node
  linkType: hard

"@sinonjs/fake-timers@npm:^10.0.2":
  version: 10.3.0
  resolution: "@sinonjs/fake-timers@npm:10.3.0"
  dependencies:
    "@sinonjs/commons": "npm:^3.0.0"
  checksum: 10c0/2e2fb6cc57f227912814085b7b01fede050cd4746ea8d49a1e44d5a0e56a804663b0340ae2f11af7559ea9bf4d087a11f2f646197a660ea3cb04e19efc04aa63
  languageName: node
  linkType: hard

"@smithy/abort-controller@npm:^4.0.4":
  version: 4.0.4
  resolution: "@smithy/abort-controller@npm:4.0.4"
  dependencies:
    "@smithy/types": "npm:^4.3.1"
    tslib: "npm:^2.6.2"
  checksum: 10c0/eb172b002fb92406c69b83460f949ace73247e6abd85d0d3714de2765c5db7b98070b9abfb630e2c591dd7b2ff770cc24f7737c1c207581f716c402b16bf46f9
  languageName: node
  linkType: hard

"@smithy/config-resolver@npm:^4.1.4":
  version: 4.1.4
  resolution: "@smithy/config-resolver@npm:4.1.4"
  dependencies:
    "@smithy/node-config-provider": "npm:^4.1.3"
    "@smithy/types": "npm:^4.3.1"
    "@smithy/util-config-provider": "npm:^4.0.0"
    "@smithy/util-middleware": "npm:^4.0.4"
    tslib: "npm:^2.6.2"
  checksum: 10c0/41832a42f8da7143732c71098b410f4ddcb096066126f7e8f45bae8d9aeb95681bd0d0d54886f46244c945c63829ca5d23373d4de31a038487aa07159722ef4e
  languageName: node
  linkType: hard

"@smithy/core@npm:^3.5.3":
  version: 3.5.3
  resolution: "@smithy/core@npm:3.5.3"
  dependencies:
    "@smithy/middleware-serde": "npm:^4.0.8"
    "@smithy/protocol-http": "npm:^5.1.2"
    "@smithy/types": "npm:^4.3.1"
    "@smithy/util-base64": "npm:^4.0.0"
    "@smithy/util-body-length-browser": "npm:^4.0.0"
    "@smithy/util-middleware": "npm:^4.0.4"
    "@smithy/util-stream": "npm:^4.2.2"
    "@smithy/util-utf8": "npm:^4.0.0"
    tslib: "npm:^2.6.2"
  checksum: 10c0/ba4bce5c58a93467e52cb9362dbdc8c8aa120dfbc5333e911c8aadcbbcd236054126277eff9f970bfc24a918f44e929a4116e4533644811ad83f44c7abc81766
  languageName: node
  linkType: hard

"@smithy/credential-provider-imds@npm:^4.0.6":
  version: 4.0.6
  resolution: "@smithy/credential-provider-imds@npm:4.0.6"
  dependencies:
    "@smithy/node-config-provider": "npm:^4.1.3"
    "@smithy/property-provider": "npm:^4.0.4"
    "@smithy/types": "npm:^4.3.1"
    "@smithy/url-parser": "npm:^4.0.4"
    tslib: "npm:^2.6.2"
  checksum: 10c0/b1f3157d0a7b9f9155ac80aeac70d7db896d23d0322a6b38f0e848f1e53864ba1bca6d3dc5dd9af86446c371ebc5bffe01f0712ad562e7635e7d13e532622aa4
  languageName: node
  linkType: hard

"@smithy/eventstream-codec@npm:^1.1.0":
  version: 1.1.0
  resolution: "@smithy/eventstream-codec@npm:1.1.0"
  dependencies:
    "@aws-crypto/crc32": "npm:3.0.0"
    "@smithy/types": "npm:^1.2.0"
    "@smithy/util-hex-encoding": "npm:^1.1.0"
    tslib: "npm:^2.5.0"
  checksum: 10c0/80af0aff986058ba7d8189c8a2361b9f18b615a1c0d29050ab423ac46c23e357be098d297f496122fb821d709ae0f3d88c27f1f5337da2369737101b6be7af58
  languageName: node
  linkType: hard

"@smithy/fetch-http-handler@npm:^5.0.4":
  version: 5.0.4
  resolution: "@smithy/fetch-http-handler@npm:5.0.4"
  dependencies:
    "@smithy/protocol-http": "npm:^5.1.2"
    "@smithy/querystring-builder": "npm:^4.0.4"
    "@smithy/types": "npm:^4.3.1"
    "@smithy/util-base64": "npm:^4.0.0"
    tslib: "npm:^2.6.2"
  checksum: 10c0/ce57acfcd40a6ff3965c5f14b432c5ab87f0b0766766960224d4af79af85e37d61da2db6dc5cfa16bf4b8f2d8966a2838d2ee6eef8d5cd5a837aacbc01517851
  languageName: node
  linkType: hard

"@smithy/hash-node@npm:^4.0.4":
  version: 4.0.4
  resolution: "@smithy/hash-node@npm:4.0.4"
  dependencies:
    "@smithy/types": "npm:^4.3.1"
    "@smithy/util-buffer-from": "npm:^4.0.0"
    "@smithy/util-utf8": "npm:^4.0.0"
    tslib: "npm:^2.6.2"
  checksum: 10c0/07beb38643990f6c055457765d65af2aedd5944d819025df90d1f2f59596d1a1394cd8c9035ac6d343bc55e3afeb186b51b0ac91938024da8687120fc0b436dc
  languageName: node
  linkType: hard

"@smithy/invalid-dependency@npm:^4.0.4":
  version: 4.0.4
  resolution: "@smithy/invalid-dependency@npm:4.0.4"
  dependencies:
    "@smithy/types": "npm:^4.3.1"
    tslib: "npm:^2.6.2"
  checksum: 10c0/5e5a6282c17a7310f8e866c7e34fa07479d42c650cf3c1875bdb0ec38d5280eeac82a269605a3521b8fa455b92673d8fd5e97eb997acf81a80da82d6f501d651
  languageName: node
  linkType: hard

"@smithy/is-array-buffer@npm:^1.1.0":
  version: 1.1.0
  resolution: "@smithy/is-array-buffer@npm:1.1.0"
  dependencies:
    tslib: "npm:^2.5.0"
  checksum: 10c0/245e525a291884224b3536440ee6fdc2651d301d17bea209ea0061e9fa61da05fbf53ccf15b49f1fdcdc28e54ef939b5c2a43a50e4f1b9a6df80bf3f806217bd
  languageName: node
  linkType: hard

"@smithy/is-array-buffer@npm:^2.2.0":
  version: 2.2.0
  resolution: "@smithy/is-array-buffer@npm:2.2.0"
  dependencies:
    tslib: "npm:^2.6.2"
  checksum: 10c0/2f2523cd8cc4538131e408eb31664983fecb0c8724956788b015aaf3ab85a0c976b50f4f09b176f1ed7bbe79f3edf80743be7a80a11f22cd9ce1285d77161aaf
  languageName: node
  linkType: hard

"@smithy/is-array-buffer@npm:^4.0.0":
  version: 4.0.0
  resolution: "@smithy/is-array-buffer@npm:4.0.0"
  dependencies:
    tslib: "npm:^2.6.2"
  checksum: 10c0/ae393fbd5944d710443cd5dd225d1178ef7fb5d6259c14f3e1316ec75e401bda6cf86f7eb98bfd38e5ed76e664b810426a5756b916702cbd418f0933e15e7a3b
  languageName: node
  linkType: hard

"@smithy/middleware-content-length@npm:^4.0.4":
  version: 4.0.4
  resolution: "@smithy/middleware-content-length@npm:4.0.4"
  dependencies:
    "@smithy/protocol-http": "npm:^5.1.2"
    "@smithy/types": "npm:^4.3.1"
    tslib: "npm:^2.6.2"
  checksum: 10c0/fde43ff13f0830c4608b83cf6e2bd3ae142aa6eb3df6f6c190c2564dd00c2c98f4f95da9146c69bc09115ad87ffc9dc24935d1a3d6d3b2383a9c8558d9177dd6
  languageName: node
  linkType: hard

"@smithy/middleware-endpoint@npm:^4.1.11":
  version: 4.1.11
  resolution: "@smithy/middleware-endpoint@npm:4.1.11"
  dependencies:
    "@smithy/core": "npm:^3.5.3"
    "@smithy/middleware-serde": "npm:^4.0.8"
    "@smithy/node-config-provider": "npm:^4.1.3"
    "@smithy/shared-ini-file-loader": "npm:^4.0.4"
    "@smithy/types": "npm:^4.3.1"
    "@smithy/url-parser": "npm:^4.0.4"
    "@smithy/util-middleware": "npm:^4.0.4"
    tslib: "npm:^2.6.2"
  checksum: 10c0/28420a3b8b42655e29a005d2de14348082fd472c008bee2d135aa0907772678961bf74a631dc583e136f4819936aa495c80fbcca5079cadfd1800bb6ab391110
  languageName: node
  linkType: hard

"@smithy/middleware-retry@npm:^4.1.12":
  version: 4.1.12
  resolution: "@smithy/middleware-retry@npm:4.1.12"
  dependencies:
    "@smithy/node-config-provider": "npm:^4.1.3"
    "@smithy/protocol-http": "npm:^5.1.2"
    "@smithy/service-error-classification": "npm:^4.0.5"
    "@smithy/smithy-client": "npm:^4.4.3"
    "@smithy/types": "npm:^4.3.1"
    "@smithy/util-middleware": "npm:^4.0.4"
    "@smithy/util-retry": "npm:^4.0.5"
    tslib: "npm:^2.6.2"
    uuid: "npm:^9.0.1"
  checksum: 10c0/7cd2ee73003423d0857a458db64ce0d9d484c8f4b669a8b33c866ee4fdbbc199e85a53f729a76d7f0874e771fb7f9b95ad151af443573588e15e9ecac1f38fbe
  languageName: node
  linkType: hard

"@smithy/middleware-serde@npm:^4.0.8":
  version: 4.0.8
  resolution: "@smithy/middleware-serde@npm:4.0.8"
  dependencies:
    "@smithy/protocol-http": "npm:^5.1.2"
    "@smithy/types": "npm:^4.3.1"
    tslib: "npm:^2.6.2"
  checksum: 10c0/11414e584780716b2b0487fe748da9927943d4d810b5b0161e73df6ab24a4d17f675773287f95868c57a71013385f7b027eb2afbab1eed3dbaafef754b482b27
  languageName: node
  linkType: hard

"@smithy/middleware-stack@npm:^4.0.4":
  version: 4.0.4
  resolution: "@smithy/middleware-stack@npm:4.0.4"
  dependencies:
    "@smithy/types": "npm:^4.3.1"
    tslib: "npm:^2.6.2"
  checksum: 10c0/b29b6430e31f11683f0ce0e06d21a4bfe6cb791ce1eb5686533559baa81698f617bfbfdac06f569e13f077ce177cb70e55f4db20701906b3e344d9294817f382
  languageName: node
  linkType: hard

"@smithy/node-config-provider@npm:^4.1.3":
  version: 4.1.3
  resolution: "@smithy/node-config-provider@npm:4.1.3"
  dependencies:
    "@smithy/property-provider": "npm:^4.0.4"
    "@smithy/shared-ini-file-loader": "npm:^4.0.4"
    "@smithy/types": "npm:^4.3.1"
    tslib: "npm:^2.6.2"
  checksum: 10c0/bea20b3f92290fbefa32d30c4ac7632f94d4e89b5432dfe5a2d0c6261bfd90e882d62dd02e0a4e65f3bc89f815b19e44d7bb103a78b6c77941cc186450ad79f1
  languageName: node
  linkType: hard

"@smithy/node-http-handler@npm:^4.0.6":
  version: 4.0.6
  resolution: "@smithy/node-http-handler@npm:4.0.6"
  dependencies:
    "@smithy/abort-controller": "npm:^4.0.4"
    "@smithy/protocol-http": "npm:^5.1.2"
    "@smithy/querystring-builder": "npm:^4.0.4"
    "@smithy/types": "npm:^4.3.1"
    tslib: "npm:^2.6.2"
  checksum: 10c0/bde23701b6166b76958cbc194d551a139e3dcc1d05a6c7de3d5b14f54934ca5a49a28d13d8ec4b012716aae816cd0c8c4735c959d5ef697a7a1932fbcfc5d7f2
  languageName: node
  linkType: hard

"@smithy/property-provider@npm:^4.0.4":
  version: 4.0.4
  resolution: "@smithy/property-provider@npm:4.0.4"
  dependencies:
    "@smithy/types": "npm:^4.3.1"
    tslib: "npm:^2.6.2"
  checksum: 10c0/c370efbb43ab01fb6050fbf4c231bbe2fb7d660256adeee40c0c4c14b7af1b9b75c36f6924aeacdd2885fad1aaf0655047cafe5f0d22f5e371cbd25ff2f04b27
  languageName: node
  linkType: hard

"@smithy/protocol-http@npm:^1.1.0":
  version: 1.2.0
  resolution: "@smithy/protocol-http@npm:1.2.0"
  dependencies:
    "@smithy/types": "npm:^1.2.0"
    tslib: "npm:^2.5.0"
  checksum: 10c0/7b2e290e21f17d6c944b7f48c4d364d049f551653464fabcf7bddbe2732f4c8a51a0056e1c57ac2eb4755d50918789a92205bc78ea7f1631453b946d7aa64009
  languageName: node
  linkType: hard

"@smithy/protocol-http@npm:^5.1.2":
  version: 5.1.2
  resolution: "@smithy/protocol-http@npm:5.1.2"
  dependencies:
    "@smithy/types": "npm:^4.3.1"
    tslib: "npm:^2.6.2"
  checksum: 10c0/50fb026efa321e65a77f9747312eeb428ff2196095c15ed5937efe807a4734c47746759ccf2dbc84a45719effcbc81221662289be6d4d5ec122afb0e3cd66fd9
  languageName: node
  linkType: hard

"@smithy/querystring-builder@npm:^4.0.4":
  version: 4.0.4
  resolution: "@smithy/querystring-builder@npm:4.0.4"
  dependencies:
    "@smithy/types": "npm:^4.3.1"
    "@smithy/util-uri-escape": "npm:^4.0.0"
    tslib: "npm:^2.6.2"
  checksum: 10c0/30ec0301fbc2212101391841000a3117ab6c3ae2b6b2a1db230cc1dfcf97738f527b23f859f0a5e843f2a983793b58cdcd21a0ce11ef93fcdf5d8a1ee0d70fbc
  languageName: node
  linkType: hard

"@smithy/querystring-parser@npm:^4.0.4":
  version: 4.0.4
  resolution: "@smithy/querystring-parser@npm:4.0.4"
  dependencies:
    "@smithy/types": "npm:^4.3.1"
    tslib: "npm:^2.6.2"
  checksum: 10c0/36bc93732a1628be5dd53748f6f36237bad26de2da810195213541dd35b20eee0b0264160a0de734b9333ca747e0229253d6729d1a8ddc26d176c0b1cce309e0
  languageName: node
  linkType: hard

"@smithy/service-error-classification@npm:^4.0.5":
  version: 4.0.5
  resolution: "@smithy/service-error-classification@npm:4.0.5"
  dependencies:
    "@smithy/types": "npm:^4.3.1"
  checksum: 10c0/9ca6a876a403fa15151d955ef43c7b4e8c3a93b334d493ab7086d095bcd8670b848779bb82be66b2a14423edf169f1be514ec381f71d2d78f0612731416911ac
  languageName: node
  linkType: hard

"@smithy/shared-ini-file-loader@npm:^4.0.4":
  version: 4.0.4
  resolution: "@smithy/shared-ini-file-loader@npm:4.0.4"
  dependencies:
    "@smithy/types": "npm:^4.3.1"
    tslib: "npm:^2.6.2"
  checksum: 10c0/a3ecabadda13ff6fca99585e7e0086a04c4d2350b8c783b3a23493c2ae0a599f397d3cb80a7e171b7123889340995cada866d320726fa6a03f3063d60d5d0207
  languageName: node
  linkType: hard

"@smithy/signature-v4@npm:^1.0.1":
  version: 1.1.0
  resolution: "@smithy/signature-v4@npm:1.1.0"
  dependencies:
    "@smithy/eventstream-codec": "npm:^1.1.0"
    "@smithy/is-array-buffer": "npm:^1.1.0"
    "@smithy/types": "npm:^1.2.0"
    "@smithy/util-hex-encoding": "npm:^1.1.0"
    "@smithy/util-middleware": "npm:^1.1.0"
    "@smithy/util-uri-escape": "npm:^1.1.0"
    "@smithy/util-utf8": "npm:^1.1.0"
    tslib: "npm:^2.5.0"
  checksum: 10c0/c4db101e800c4d2fee95441abbe56beb0e9e3aba2b09b21b87ad5b048356b9de2a57983c11d6a562ad860e4059f34e01ba2a9cd708eab66a989a49226c24b1be
  languageName: node
  linkType: hard

"@smithy/signature-v4@npm:^5.1.2":
  version: 5.1.2
  resolution: "@smithy/signature-v4@npm:5.1.2"
  dependencies:
    "@smithy/is-array-buffer": "npm:^4.0.0"
    "@smithy/protocol-http": "npm:^5.1.2"
    "@smithy/types": "npm:^4.3.1"
    "@smithy/util-hex-encoding": "npm:^4.0.0"
    "@smithy/util-middleware": "npm:^4.0.4"
    "@smithy/util-uri-escape": "npm:^4.0.0"
    "@smithy/util-utf8": "npm:^4.0.0"
    tslib: "npm:^2.6.2"
  checksum: 10c0/83d3870668a6c080c1d0cbecf2e7d1a86c0298cc3a3df9fba21bd942e2a9bcae81eb50960c66bba00c6f9820ef9e5ab3e5ddba67b2d7914a09a82c7887621c0c
  languageName: node
  linkType: hard

"@smithy/smithy-client@npm:^4.4.3":
  version: 4.4.3
  resolution: "@smithy/smithy-client@npm:4.4.3"
  dependencies:
    "@smithy/core": "npm:^3.5.3"
    "@smithy/middleware-endpoint": "npm:^4.1.11"
    "@smithy/middleware-stack": "npm:^4.0.4"
    "@smithy/protocol-http": "npm:^5.1.2"
    "@smithy/types": "npm:^4.3.1"
    "@smithy/util-stream": "npm:^4.2.2"
    tslib: "npm:^2.6.2"
  checksum: 10c0/37f69c4af3883525cebe4ea648b05cd93de01742d7206e7613e65ec15f3bd06874b0d923a6ccfbdc3b7e01a2cb6dc6c961f7590f984eea4e55c68871dfb3b11a
  languageName: node
  linkType: hard

"@smithy/types@npm:^1.2.0":
  version: 1.2.0
  resolution: "@smithy/types@npm:1.2.0"
  dependencies:
    tslib: "npm:^2.5.0"
  checksum: 10c0/fd82b07fe9e3d6fe0877a3bba7d4e93aa0d9d2b64762509ef8235a8b0d0e41631a2eb0c55678aad1d6ff1c59a443fe9647d1b79bf0ec52f78c46040bb1d8ffb9
  languageName: node
  linkType: hard

"@smithy/types@npm:^4.3.1":
  version: 4.3.1
  resolution: "@smithy/types@npm:4.3.1"
  dependencies:
    tslib: "npm:^2.6.2"
  checksum: 10c0/8b350562b9ed4ff97465025b4ae77a34bb07b9d47fb6f9781755aac9401b0355a63c2fef307393e2dae3fa0277149dd7d83f5bc2a63d4ad3519ea32fd56b5cda
  languageName: node
  linkType: hard

"@smithy/url-parser@npm:^4.0.4":
  version: 4.0.4
  resolution: "@smithy/url-parser@npm:4.0.4"
  dependencies:
    "@smithy/querystring-parser": "npm:^4.0.4"
    "@smithy/types": "npm:^4.3.1"
    tslib: "npm:^2.6.2"
  checksum: 10c0/5f4649d9ff618c683e339fa826b1d722419bf8e20d72726fc5fe3cd479ec8c161d4b09b6e24e49b0143a6fb4f9a950d35410db1834e143c28e377b9c529a3657
  languageName: node
  linkType: hard

"@smithy/util-base64@npm:^4.0.0":
  version: 4.0.0
  resolution: "@smithy/util-base64@npm:4.0.0"
  dependencies:
    "@smithy/util-buffer-from": "npm:^4.0.0"
    "@smithy/util-utf8": "npm:^4.0.0"
    tslib: "npm:^2.6.2"
  checksum: 10c0/ad18ec66cc357c189eef358d96876b114faf7086b13e47e009b265d0ff80cec046052500489c183957b3a036768409acdd1a373e01074cc002ca6983f780cffc
  languageName: node
  linkType: hard

"@smithy/util-body-length-browser@npm:^4.0.0":
  version: 4.0.0
  resolution: "@smithy/util-body-length-browser@npm:4.0.0"
  dependencies:
    tslib: "npm:^2.6.2"
  checksum: 10c0/574a10934024a86556e9dcde1a9776170284326c3dfcc034afa128cc5a33c1c8179fca9cfb622ef8be5f2004316cc3f427badccceb943e829105536ec26306d9
  languageName: node
  linkType: hard

"@smithy/util-body-length-node@npm:^4.0.0":
  version: 4.0.0
  resolution: "@smithy/util-body-length-node@npm:4.0.0"
  dependencies:
    tslib: "npm:^2.6.2"
  checksum: 10c0/e91fd3816767606c5f786166ada26440457fceb60f96653b3d624dcf762a8c650e513c275ff3f647cb081c63c283cc178853a7ed9aa224abc8ece4eeeef7a1dd
  languageName: node
  linkType: hard

"@smithy/util-buffer-from@npm:^1.1.0":
  version: 1.1.0
  resolution: "@smithy/util-buffer-from@npm:1.1.0"
  dependencies:
    "@smithy/is-array-buffer": "npm:^1.1.0"
    tslib: "npm:^2.5.0"
  checksum: 10c0/40f1c8092237b0e3a71998a08ce84906703a6d388ea60bd6142056d172b26651db568ad7d95ca37632083e48c4fc634d4247559306be77520867a1a1c894b155
  languageName: node
  linkType: hard

"@smithy/util-buffer-from@npm:^2.2.0":
  version: 2.2.0
  resolution: "@smithy/util-buffer-from@npm:2.2.0"
  dependencies:
    "@smithy/is-array-buffer": "npm:^2.2.0"
    tslib: "npm:^2.6.2"
  checksum: 10c0/223d6a508b52ff236eea01cddc062b7652d859dd01d457a4e50365af3de1e24a05f756e19433f6ccf1538544076b4215469e21a4ea83dc1d58d829725b0dbc5a
  languageName: node
  linkType: hard

"@smithy/util-buffer-from@npm:^4.0.0":
  version: 4.0.0
  resolution: "@smithy/util-buffer-from@npm:4.0.0"
  dependencies:
    "@smithy/is-array-buffer": "npm:^4.0.0"
    tslib: "npm:^2.6.2"
  checksum: 10c0/be7cd33b6cb91503982b297716251e67cdca02819a15797632091cadab2dc0b4a147fff0709a0aa9bbc0b82a2644a7ed7c8afdd2194d5093cee2e9605b3a9f6f
  languageName: node
  linkType: hard

"@smithy/util-config-provider@npm:^4.0.0":
  version: 4.0.0
  resolution: "@smithy/util-config-provider@npm:4.0.0"
  dependencies:
    tslib: "npm:^2.6.2"
  checksum: 10c0/cd9498d5f77a73aadd575084bcb22d2bb5945bac4605d605d36f2efe3f165f2b60f4dc88b7a62c2ed082ffa4b2c2f19621d0859f18399edbc2b5988d92e4649f
  languageName: node
  linkType: hard

"@smithy/util-defaults-mode-browser@npm:^4.0.19":
  version: 4.0.19
  resolution: "@smithy/util-defaults-mode-browser@npm:4.0.19"
  dependencies:
    "@smithy/property-provider": "npm:^4.0.4"
    "@smithy/smithy-client": "npm:^4.4.3"
    "@smithy/types": "npm:^4.3.1"
    bowser: "npm:^2.11.0"
    tslib: "npm:^2.6.2"
  checksum: 10c0/05998cf1481f1bc2467f2fba571faa9ebcaeb1cf58d5c411a1096320068a9467b100ee2491eb1d56458d56d723a0b28711a975fb186df60bf3165d2d8aa6a678
  languageName: node
  linkType: hard

"@smithy/util-defaults-mode-node@npm:^4.0.19":
  version: 4.0.19
  resolution: "@smithy/util-defaults-mode-node@npm:4.0.19"
  dependencies:
    "@smithy/config-resolver": "npm:^4.1.4"
    "@smithy/credential-provider-imds": "npm:^4.0.6"
    "@smithy/node-config-provider": "npm:^4.1.3"
    "@smithy/property-provider": "npm:^4.0.4"
    "@smithy/smithy-client": "npm:^4.4.3"
    "@smithy/types": "npm:^4.3.1"
    tslib: "npm:^2.6.2"
  checksum: 10c0/e12adbad9efa9f5604beb356d7b84de62df47cea6535e9835987a764c28602e341ea4909cd08daef6c0627bbcb921725bca524664ac00eb78ac27efbd0e924dd
  languageName: node
  linkType: hard

"@smithy/util-endpoints@npm:^3.0.6":
  version: 3.0.6
  resolution: "@smithy/util-endpoints@npm:3.0.6"
  dependencies:
    "@smithy/node-config-provider": "npm:^4.1.3"
    "@smithy/types": "npm:^4.3.1"
    tslib: "npm:^2.6.2"
  checksum: 10c0/d7d583c73a0c1ce38188569616cd4d7c95c36c0393516117043962b932f8c743e8cd672d2edd23ea8a9da0e30b84ee0f0ced0709cc8024b70ea8e5f17f505811
  languageName: node
  linkType: hard

"@smithy/util-hex-encoding@npm:^1.1.0":
  version: 1.1.0
  resolution: "@smithy/util-hex-encoding@npm:1.1.0"
  dependencies:
    tslib: "npm:^2.5.0"
  checksum: 10c0/1bc9ce34215e7c88a89068de1da4e094a70ee1c897e167470476af0b6a96c3104b59599a7c717252a1525fc1709508d6045e97e8698eeff40dfa5f095bb9cb92
  languageName: node
  linkType: hard

"@smithy/util-hex-encoding@npm:^4.0.0":
  version: 4.0.0
  resolution: "@smithy/util-hex-encoding@npm:4.0.0"
  dependencies:
    tslib: "npm:^2.6.2"
  checksum: 10c0/70dbb3aa1a79aff3329d07a66411ff26398df338bdd8a6d077b438231afe3dc86d9a7022204baddecd8bc633f059d5c841fa916d81dd7447ea79b64148f386d2
  languageName: node
  linkType: hard

"@smithy/util-middleware@npm:^1.1.0":
  version: 1.1.0
  resolution: "@smithy/util-middleware@npm:1.1.0"
  dependencies:
    tslib: "npm:^2.5.0"
  checksum: 10c0/547859b0e90ee0ff5abf3a72fdf36a6c3f5f0c50555ab384eaee215487914ae7bb28549bb0a172ccca5ccea906bc38347543c5883d2f015781b58cc7ddf3103b
  languageName: node
  linkType: hard

"@smithy/util-middleware@npm:^4.0.4":
  version: 4.0.4
  resolution: "@smithy/util-middleware@npm:4.0.4"
  dependencies:
    "@smithy/types": "npm:^4.3.1"
    tslib: "npm:^2.6.2"
  checksum: 10c0/39530add63ec13dac555846c30e98128316136f7f57bfd8fe876a8c15a7677cb64d0a33fd1f08b671096d769ab3f025d4d8c785a9d7a7cdf42fd0188236b0f32
  languageName: node
  linkType: hard

"@smithy/util-retry@npm:^4.0.5":
  version: 4.0.5
  resolution: "@smithy/util-retry@npm:4.0.5"
  dependencies:
    "@smithy/service-error-classification": "npm:^4.0.5"
    "@smithy/types": "npm:^4.3.1"
    tslib: "npm:^2.6.2"
  checksum: 10c0/8e6c136f79c503c02e28b31bc43fce7a37282143c20aee13d2a7421b0502d5c478f2eb3cf3c3455739ed9e441e572e2725bf39339aa08ed53825129123dcfff0
  languageName: node
  linkType: hard

"@smithy/util-stream@npm:^4.2.2":
  version: 4.2.2
  resolution: "@smithy/util-stream@npm:4.2.2"
  dependencies:
    "@smithy/fetch-http-handler": "npm:^5.0.4"
    "@smithy/node-http-handler": "npm:^4.0.6"
    "@smithy/types": "npm:^4.3.1"
    "@smithy/util-base64": "npm:^4.0.0"
    "@smithy/util-buffer-from": "npm:^4.0.0"
    "@smithy/util-hex-encoding": "npm:^4.0.0"
    "@smithy/util-utf8": "npm:^4.0.0"
    tslib: "npm:^2.6.2"
  checksum: 10c0/5e4ef783e41185d291a72e8503d02fd5a5f7bd23f3d30198f3d738c0f27dd6d7ea131fe6fbe36a6ac69b8bd4207f7dfc75a15329764e6aa52f62c45bc5442619
  languageName: node
  linkType: hard

"@smithy/util-uri-escape@npm:^1.1.0":
  version: 1.1.0
  resolution: "@smithy/util-uri-escape@npm:1.1.0"
  dependencies:
    tslib: "npm:^2.5.0"
  checksum: 10c0/54c2dd1a52df3c948c5182cda3385b670e9a1e9b287250b9becef7e8b675689808036b2a2ee27dad417aec5194f7fd90ee4e4d26d6826caf628f834e3b667d73
  languageName: node
  linkType: hard

"@smithy/util-uri-escape@npm:^4.0.0":
  version: 4.0.0
  resolution: "@smithy/util-uri-escape@npm:4.0.0"
  dependencies:
    tslib: "npm:^2.6.2"
  checksum: 10c0/23984624060756adba8aa4ab1693fe6b387ee5064d8ec4dfd39bb5908c4ee8b9c3f2dc755da9b07505d8e3ce1338c1867abfa74158931e4728bf3cfcf2c05c3d
  languageName: node
  linkType: hard

"@smithy/util-utf8@npm:^1.1.0":
  version: 1.1.0
  resolution: "@smithy/util-utf8@npm:1.1.0"
  dependencies:
    "@smithy/util-buffer-from": "npm:^1.1.0"
    tslib: "npm:^2.5.0"
  checksum: 10c0/44de2dcbb96766fdb31f4cfda2732f743c6ca2e088283d1efb6aa357eeaded75887eb68aa1cf3a7619625e90c77883c7047d4c875e1d15c92d83273f37aacabc
  languageName: node
  linkType: hard

"@smithy/util-utf8@npm:^2.0.0":
  version: 2.3.0
  resolution: "@smithy/util-utf8@npm:2.3.0"
  dependencies:
    "@smithy/util-buffer-from": "npm:^2.2.0"
    tslib: "npm:^2.6.2"
  checksum: 10c0/e18840c58cc507ca57fdd624302aefd13337ee982754c9aa688463ffcae598c08461e8620e9852a424d662ffa948fc64919e852508028d09e89ced459bd506ab
  languageName: node
  linkType: hard

"@smithy/util-utf8@npm:^4.0.0":
  version: 4.0.0
  resolution: "@smithy/util-utf8@npm:4.0.0"
  dependencies:
    "@smithy/util-buffer-from": "npm:^4.0.0"
    tslib: "npm:^2.6.2"
  checksum: 10c0/28a5a5372cbf0b3d2e32dd16f79b04c2aec6f704cf13789db922e9686fde38dde0171491cfa4c2c201595d54752a319faaeeed3c325329610887694431e28c98
  languageName: node
  linkType: hard

"@smithy/util-waiter@npm:^4.0.5":
  version: 4.0.5
  resolution: "@smithy/util-waiter@npm:4.0.5"
  dependencies:
    "@smithy/abort-controller": "npm:^4.0.4"
    "@smithy/types": "npm:^4.3.1"
    tslib: "npm:^2.6.2"
  checksum: 10c0/c53b4ae929d37d8d8b3629b0c91005d48c8f788257eccbfb62b3b7f7a670934d8a44556456289c4a0a5fde957d87162c36318184b5e2df154deeeabe97bfd4b4
  languageName: node
  linkType: hard

"@socket.io/component-emitter@npm:~3.1.0":
  version: 3.1.2
  resolution: "@socket.io/component-emitter@npm:3.1.2"
  checksum: 10c0/c4242bad66f67e6f7b712733d25b43cbb9e19a595c8701c3ad99cbeb5901555f78b095e24852f862fffb43e96f1d8552e62def885ca82ae1bb05da3668fd87d7
  languageName: node
  linkType: hard

"@supabase/auth-js@npm:2.70.0":
  version: 2.70.0
  resolution: "@supabase/auth-js@npm:2.70.0"
  dependencies:
    "@supabase/node-fetch": "npm:^2.6.14"
  checksum: 10c0/99696a5617dd55fa45815683fbb3d503146ab9c4d789ec746811af5df9fab3a41559c98175b8756247ccff727654f182c2038375a45d08da96cddcc740bfe717
  languageName: node
  linkType: hard

"@supabase/functions-js@npm:2.4.4":
  version: 2.4.4
  resolution: "@supabase/functions-js@npm:2.4.4"
  dependencies:
    "@supabase/node-fetch": "npm:^2.6.14"
  checksum: 10c0/35871341ca96c35a416d81a6f035bd0d594d278f5cbe4492173766b3d6b9acfc52374b0a2b50e31a900a8e3a9dcb131d1eadf3808a9a9e1c10bbab7a2045d2d3
  languageName: node
  linkType: hard

"@supabase/node-fetch@npm:2.6.15, @supabase/node-fetch@npm:^2.6.14":
  version: 2.6.15
  resolution: "@supabase/node-fetch@npm:2.6.15"
  dependencies:
    whatwg-url: "npm:^5.0.0"
  checksum: 10c0/98d25cab2eba53c93c59e730d52d50065b1a7fe216c65224471e83e2064ebd45ae51ad09cb39ec263c3cb59e3d41870fc2e789ea2e9587480d7ba212b85daf38
  languageName: node
  linkType: hard

"@supabase/node-fetch@npm:^2.6.13":
  version: 2.6.13
  resolution: "@supabase/node-fetch@npm:2.6.13"
  dependencies:
    whatwg-url: "npm:^5.0.0"
  checksum: 10c0/2f3b636e1152e314e1ba5e10d2849d159ad76e9492e828e171b6173763d01c5f04189d1b87b3ae342e826662e80be29c41cabed3e6e40c2d709fc22b965d85c4
  languageName: node
  linkType: hard

"@supabase/postgrest-js@npm:1.19.4":
  version: 1.19.4
  resolution: "@supabase/postgrest-js@npm:1.19.4"
  dependencies:
    "@supabase/node-fetch": "npm:^2.6.14"
  checksum: 10c0/1d14adc841f720e0035045f8b06cf2cb9f3b0a83ac903e268d5afb80b64d240ae64cb24372e0e9c857420b07010bfdb9a806f024fe60ac13468fd791ada2eb7f
  languageName: node
  linkType: hard

"@supabase/realtime-js@npm:2.11.10":
  version: 2.11.10
  resolution: "@supabase/realtime-js@npm:2.11.10"
  dependencies:
    "@supabase/node-fetch": "npm:^2.6.13"
    "@types/phoenix": "npm:^1.6.6"
    "@types/ws": "npm:^8.18.1"
    ws: "npm:^8.18.2"
  checksum: 10c0/948dfec5bf57049f0fe7fb2a91c71e6b863714c6a312e0db2b043735161d1a7996853b899c40a651b42000876e235d3960a32e5a96f40b8b5717e8cec3508670
  languageName: node
  linkType: hard

"@supabase/storage-js@npm:2.7.1":
  version: 2.7.1
  resolution: "@supabase/storage-js@npm:2.7.1"
  dependencies:
    "@supabase/node-fetch": "npm:^2.6.14"
  checksum: 10c0/bcaa8bd275c59b8c5f6f00b9590ef54f008b63aacdcd8bf1747cb73f61ea7bd321bb816314ae0cf1bb318cd4d398515f9a135bde84ef960c19ac3c11e38d00fd
  languageName: node
  linkType: hard

"@supabase/supabase-js@npm:^2.50.0":
  version: 2.50.0
  resolution: "@supabase/supabase-js@npm:2.50.0"
  dependencies:
    "@supabase/auth-js": "npm:2.70.0"
    "@supabase/functions-js": "npm:2.4.4"
    "@supabase/node-fetch": "npm:2.6.15"
    "@supabase/postgrest-js": "npm:1.19.4"
    "@supabase/realtime-js": "npm:2.11.10"
    "@supabase/storage-js": "npm:2.7.1"
  checksum: 10c0/42a1ba0bc4a902114cdb357fa194e20d67c52903b831543f67ee406ac51aba58bc9b33c0950d7c1fcaa561ebd9128fc02b04385add6a12816fd0668da5c50aa2
  languageName: node
  linkType: hard

"@trigger.dev/build@npm:^3.3.17":
  version: 3.3.17
  resolution: "@trigger.dev/build@npm:3.3.17"
  dependencies:
    "@trigger.dev/core": "npm:3.3.17"
    pkg-types: "npm:^1.1.3"
    tinyglobby: "npm:^0.2.2"
    tsconfck: "npm:3.1.3"
  checksum: 10c0/a9ded15114f4d03f2fda35ef057a6726b11c215d47aa34353a6bfcbc5bd7786722ae6e9edc5e9dc9af554a81b1d2a3d7012e1895c64484c08d09e231f0daaa4f
  languageName: node
  linkType: hard

"@trigger.dev/core@npm:3.3.17":
  version: 3.3.17
  resolution: "@trigger.dev/core@npm:3.3.17"
  dependencies:
    "@electric-sql/client": "npm:1.0.0-beta.1"
    "@google-cloud/precise-date": "npm:^4.0.0"
    "@jsonhero/path": "npm:^1.0.21"
    "@opentelemetry/api": "npm:1.9.0"
    "@opentelemetry/api-logs": "npm:0.52.1"
    "@opentelemetry/exporter-logs-otlp-http": "npm:0.52.1"
    "@opentelemetry/exporter-trace-otlp-http": "npm:0.52.1"
    "@opentelemetry/instrumentation": "npm:0.52.1"
    "@opentelemetry/resources": "npm:1.25.1"
    "@opentelemetry/sdk-logs": "npm:0.52.1"
    "@opentelemetry/sdk-node": "npm:0.52.1"
    "@opentelemetry/sdk-trace-base": "npm:1.25.1"
    "@opentelemetry/sdk-trace-node": "npm:1.25.1"
    "@opentelemetry/semantic-conventions": "npm:1.25.1"
    dequal: "npm:^2.0.3"
    eventsource: "npm:^3.0.5"
    eventsource-parser: "npm:^3.0.0"
    execa: "npm:^8.0.1"
    humanize-duration: "npm:^3.27.3"
    jose: "npm:^5.4.0"
    nanoid: "npm:^3.3.4"
    socket.io-client: "npm:4.7.5"
    superjson: "npm:^2.2.1"
    zod: "npm:3.23.8"
    zod-error: "npm:1.5.0"
    zod-validation-error: "npm:^1.5.0"
  checksum: 10c0/3e32a47c0d1043b9e3d395e5ac86c2cecdb0988176ada3840f2c10c01bce8cddf27a026fe8bdf64a3d411b31afb4ac160a6f7dc9efd0db8ac13bc286da72eeb6
  languageName: node
  linkType: hard

"@trigger.dev/sdk@npm:^3.3.17":
  version: 3.3.17
  resolution: "@trigger.dev/sdk@npm:3.3.17"
  dependencies:
    "@opentelemetry/api": "npm:1.9.0"
    "@opentelemetry/api-logs": "npm:0.52.1"
    "@opentelemetry/semantic-conventions": "npm:1.25.1"
    "@trigger.dev/core": "npm:3.3.17"
    chalk: "npm:^5.2.0"
    cronstrue: "npm:^2.21.0"
    debug: "npm:^4.3.4"
    evt: "npm:^2.4.13"
    slug: "npm:^6.0.0"
    terminal-link: "npm:^3.0.0"
    ulid: "npm:^2.3.0"
    uncrypto: "npm:^0.1.3"
    uuid: "npm:^9.0.0"
    ws: "npm:^8.11.0"
  peerDependencies:
    zod: ^3.0.0
  checksum: 10c0/16ca3da4209adbf1f5e176b2540f3184a66b3b658085d6948fb70083ea4e22f21e6921055224403439342a8b46f1def4cd5df041454f5006f960e85abd7e849d
  languageName: node
  linkType: hard

"@tsconfig/node10@npm:^1.0.7":
  version: 1.0.11
  resolution: "@tsconfig/node10@npm:1.0.11"
  checksum: 10c0/28a0710e5d039e0de484bdf85fee883bfd3f6a8980601f4d44066b0a6bcd821d31c4e231d1117731c4e24268bd4cf2a788a6787c12fc7f8d11014c07d582783c
  languageName: node
  linkType: hard

"@tsconfig/node12@npm:^1.0.7":
  version: 1.0.11
  resolution: "@tsconfig/node12@npm:1.0.11"
  checksum: 10c0/dddca2b553e2bee1308a056705103fc8304e42bb2d2cbd797b84403a223b25c78f2c683ec3e24a095e82cd435387c877239bffcb15a590ba817cd3f6b9a99fd9
  languageName: node
  linkType: hard

"@tsconfig/node14@npm:^1.0.0":
  version: 1.0.3
  resolution: "@tsconfig/node14@npm:1.0.3"
  checksum: 10c0/67c1316d065fdaa32525bc9449ff82c197c4c19092b9663b23213c8cbbf8d88b6ed6a17898e0cbc2711950fbfaf40388938c1c748a2ee89f7234fc9e7fe2bf44
  languageName: node
  linkType: hard

"@tsconfig/node16@npm:^1.0.2":
  version: 1.0.4
  resolution: "@tsconfig/node16@npm:1.0.4"
  checksum: 10c0/05f8f2734e266fb1839eb1d57290df1664fe2aa3b0fdd685a9035806daa635f7519bf6d5d9b33f6e69dd545b8c46bd6e2b5c79acb2b1f146e885f7f11a42a5bb
  languageName: node
  linkType: hard

"@types/aws-lambda@npm:8.10.147":
  version: 8.10.147
  resolution: "@types/aws-lambda@npm:8.10.147"
  checksum: 10c0/c77bcb18a935fb26f5b1164aaadf46b3d11d6c001a95c6e9f2ff72f7d9ed4e7f28075a3abf9f9585cc75510acbc29c7a6441e66727902eae1bd39ac8dc28351e
  languageName: node
  linkType: hard

"@types/babel__core@npm:^7.1.14":
  version: 7.20.5
  resolution: "@types/babel__core@npm:7.20.5"
  dependencies:
    "@babel/parser": "npm:^7.20.7"
    "@babel/types": "npm:^7.20.7"
    "@types/babel__generator": "npm:*"
    "@types/babel__template": "npm:*"
    "@types/babel__traverse": "npm:*"
  checksum: 10c0/bdee3bb69951e833a4b811b8ee9356b69a61ed5b7a23e1a081ec9249769117fa83aaaf023bb06562a038eb5845155ff663e2d5c75dd95c1d5ccc91db012868ff
  languageName: node
  linkType: hard

"@types/babel__generator@npm:*":
  version: 7.27.0
  resolution: "@types/babel__generator@npm:7.27.0"
  dependencies:
    "@babel/types": "npm:^7.0.0"
  checksum: 10c0/9f9e959a8792df208a9d048092fda7e1858bddc95c6314857a8211a99e20e6830bdeb572e3587ae8be5429e37f2a96fcf222a9f53ad232f5537764c9e13a2bbd
  languageName: node
  linkType: hard

"@types/babel__template@npm:*":
  version: 7.4.4
  resolution: "@types/babel__template@npm:7.4.4"
  dependencies:
    "@babel/parser": "npm:^7.1.0"
    "@babel/types": "npm:^7.0.0"
  checksum: 10c0/cc84f6c6ab1eab1427e90dd2b76ccee65ce940b778a9a67be2c8c39e1994e6f5bbc8efa309f6cea8dc6754994524cd4d2896558df76d92e7a1f46ecffee7112b
  languageName: node
  linkType: hard

"@types/babel__traverse@npm:*, @types/babel__traverse@npm:^7.0.6":
  version: 7.20.7
  resolution: "@types/babel__traverse@npm:7.20.7"
  dependencies:
    "@babel/types": "npm:^7.20.7"
  checksum: 10c0/5386f0af44f8746b063b87418f06129a814e16bb2686965a575e9d7376b360b088b89177778d8c426012abc43dd1a2d8ec3218bfc382280c898682746ce2ffbd
  languageName: node
  linkType: hard

"@types/body-parser@npm:*, @types/body-parser@npm:^1.19.5":
  version: 1.19.6
  resolution: "@types/body-parser@npm:1.19.6"
  dependencies:
    "@types/connect": "npm:*"
    "@types/node": "npm:*"
  checksum: 10c0/542da05c924dce58ee23f50a8b981fee36921850c82222e384931fda3e106f750f7880c47be665217d72dbe445129049db6eb1f44e7a06b09d62af8f3cca8ea7
  languageName: node
  linkType: hard

"@types/bunyan@npm:1.8.11":
  version: 1.8.11
  resolution: "@types/bunyan@npm:1.8.11"
  dependencies:
    "@types/node": "npm:*"
  checksum: 10c0/07d762499307a1c3f04f56f2c62417b909f86f6090cee29b73a00dde323a4463cfd2e78888598cb1cd3b1eb88e6c47ef2a58e17f119dae27ff04cd361c0a1d4c
  languageName: node
  linkType: hard

"@types/connect@npm:*, @types/connect@npm:3.4.38":
  version: 3.4.38
  resolution: "@types/connect@npm:3.4.38"
  dependencies:
    "@types/node": "npm:*"
  checksum: 10c0/2e1cdba2c410f25649e77856505cd60223250fa12dff7a503e492208dbfdd25f62859918f28aba95315251fd1f5e1ffbfca1e25e73037189ab85dd3f8d0a148c
  languageName: node
  linkType: hard

"@types/cookiejar@npm:^2.1.5":
  version: 2.1.5
  resolution: "@types/cookiejar@npm:2.1.5"
  checksum: 10c0/af38c3d84aebb3ccc6e46fb6afeeaac80fb26e63a487dd4db5a8b87e6ad3d4b845ba1116b2ae90d6f886290a36200fa433d8b1f6fe19c47da6b81872ce9a2764
  languageName: node
  linkType: hard

"@types/cors@npm:^2.8.17":
  version: 2.8.19
  resolution: "@types/cors@npm:2.8.19"
  dependencies:
    "@types/node": "npm:*"
  checksum: 10c0/b5dd407040db7d8aa1bd36e79e5f3f32292f6b075abc287529e9f48df1a25fda3e3799ba30b4656667ffb931d3b75690c1d6ca71e39f7337ea6dfda8581916d0
  languageName: node
  linkType: hard

"@types/diff-match-patch@npm:^1.0.36":
  version: 1.0.36
  resolution: "@types/diff-match-patch@npm:1.0.36"
  checksum: 10c0/0bad011ab138baa8bde94e7815064bb881f010452463272644ddbbb0590659cb93f7aa2776ff442c6721d70f202839e1053f8aa62d801cc4166f7a3ea9130055
  languageName: node
  linkType: hard

"@types/estree@npm:*, @types/estree@npm:1.0.8, @types/estree@npm:^1.0.0":
  version: 1.0.8
  resolution: "@types/estree@npm:1.0.8"
  checksum: 10c0/39d34d1afaa338ab9763f37ad6066e3f349444f9052b9676a7cc0252ef9485a41c6d81c9c4e0d26e9077993354edf25efc853f3224dd4b447175ef62bdcc86a5
  languageName: node
  linkType: hard

"@types/express-serve-static-core@npm:^4.17.33":
  version: 4.19.6
  resolution: "@types/express-serve-static-core@npm:4.19.6"
  dependencies:
    "@types/node": "npm:*"
    "@types/qs": "npm:*"
    "@types/range-parser": "npm:*"
    "@types/send": "npm:*"
  checksum: 10c0/4281f4ead71723f376b3ddf64868ae26244d434d9906c101cf8d436d4b5c779d01bd046e4ea0ed1a394d3e402216fabfa22b1fa4dba501061cd7c81c54045983
  languageName: node
  linkType: hard

"@types/express@npm:^4.17.21":
  version: 4.17.23
  resolution: "@types/express@npm:4.17.23"
  dependencies:
    "@types/body-parser": "npm:*"
    "@types/express-serve-static-core": "npm:^4.17.33"
    "@types/qs": "npm:*"
    "@types/serve-static": "npm:*"
  checksum: 10c0/60490cd4f73085007247e7d4fafad0a7abdafa34fa3caba2757512564ca5e094ece7459f0f324030a63d513f967bb86579a8682af76ae2fd718e889b0a2a4fe8
  languageName: node
  linkType: hard

"@types/graceful-fs@npm:^4.1.3":
  version: 4.1.9
  resolution: "@types/graceful-fs@npm:4.1.9"
  dependencies:
    "@types/node": "npm:*"
  checksum: 10c0/235d2fc69741448e853333b7c3d1180a966dd2b8972c8cbcd6b2a0c6cd7f8d582ab2b8e58219dbc62cce8f1b40aa317ff78ea2201cdd8249da5025adebed6f0b
  languageName: node
  linkType: hard

"@types/hast@npm:^3.0.0, @types/hast@npm:^3.0.4":
  version: 3.0.4
  resolution: "@types/hast@npm:3.0.4"
  dependencies:
    "@types/unist": "npm:*"
  checksum: 10c0/3249781a511b38f1d330fd1e3344eed3c4e7ea8eff82e835d35da78e637480d36fad37a78be5a7aed8465d237ad0446abc1150859d0fde395354ea634decf9f7
  languageName: node
  linkType: hard

"@types/http-errors@npm:*":
  version: 2.0.5
  resolution: "@types/http-errors@npm:2.0.5"
  checksum: 10c0/00f8140fbc504f47356512bd88e1910c2f07e04233d99c88c854b3600ce0523c8cd0ba7d1897667243282eb44c59abb9245959e2428b9de004f93937f52f7c15
  languageName: node
  linkType: hard

"@types/istanbul-lib-coverage@npm:*, @types/istanbul-lib-coverage@npm:^2.0.0, @types/istanbul-lib-coverage@npm:^2.0.1":
  version: 2.0.6
  resolution: "@types/istanbul-lib-coverage@npm:2.0.6"
  checksum: 10c0/3948088654f3eeb45363f1db158354fb013b362dba2a5c2c18c559484d5eb9f6fd85b23d66c0a7c2fcfab7308d0a585b14dadaca6cc8bf89ebfdc7f8f5102fb7
  languageName: node
  linkType: hard

"@types/istanbul-lib-report@npm:*":
  version: 3.0.3
  resolution: "@types/istanbul-lib-report@npm:3.0.3"
  dependencies:
    "@types/istanbul-lib-coverage": "npm:*"
  checksum: 10c0/247e477bbc1a77248f3c6de5dadaae85ff86ac2d76c5fc6ab1776f54512a745ff2a5f791d22b942e3990ddbd40f3ef5289317c4fca5741bedfaa4f01df89051c
  languageName: node
  linkType: hard

"@types/istanbul-reports@npm:^3.0.0":
  version: 3.0.4
  resolution: "@types/istanbul-reports@npm:3.0.4"
  dependencies:
    "@types/istanbul-lib-report": "npm:*"
  checksum: 10c0/1647fd402aced5b6edac87274af14ebd6b3a85447ef9ad11853a70fd92a98d35f81a5d3ea9fcb5dbb5834e800c6e35b64475e33fcae6bfa9acc70d61497c54ee
  languageName: node
  linkType: hard

"@types/jest@npm:^29.5.14":
  version: 29.5.14
  resolution: "@types/jest@npm:29.5.14"
  dependencies:
    expect: "npm:^29.0.0"
    pretty-format: "npm:^29.0.0"
  checksum: 10c0/18e0712d818890db8a8dab3d91e9ea9f7f19e3f83c2e50b312f557017dc81466207a71f3ed79cf4428e813ba939954fa26ffa0a9a7f153181ba174581b1c2aed
  languageName: node
  linkType: hard

"@types/json-schema@npm:^7.0.15":
  version: 7.0.15
  resolution: "@types/json-schema@npm:7.0.15"
  checksum: 10c0/a996a745e6c5d60292f36731dd41341339d4eeed8180bb09226e5c8d23759067692b1d88e5d91d72ee83dfc00d3aca8e7bd43ea120516c17922cbcb7c3e252db
  languageName: node
  linkType: hard

"@types/mdast@npm:^4.0.0":
  version: 4.0.4
  resolution: "@types/mdast@npm:4.0.4"
  dependencies:
    "@types/unist": "npm:*"
  checksum: 10c0/84f403dbe582ee508fd9c7643ac781ad8597fcbfc9ccb8d4715a2c92e4545e5772cbd0dbdf18eda65789386d81b009967fdef01b24faf6640f817287f54d9c82
  languageName: node
  linkType: hard

"@types/memcached@npm:^2.2.6":
  version: 2.2.10
  resolution: "@types/memcached@npm:2.2.10"
  dependencies:
    "@types/node": "npm:*"
  checksum: 10c0/0c5214a73c9abb3d1bbf91d2890d38476961ae8aa387f71235519be65a537c654ca0380a468cf3ab49d3b9409c441580d081f16f14ed6aea3339144aee0f16fb
  languageName: node
  linkType: hard

"@types/methods@npm:^1.1.4":
  version: 1.1.4
  resolution: "@types/methods@npm:1.1.4"
  checksum: 10c0/a78534d79c300718298bfff92facd07bf38429c66191f640c1db4c9cff1e36f819304298a96f7536b6512bfc398e5c3e6b831405e138cd774b88ad7be78d682a
  languageName: node
  linkType: hard

"@types/mime@npm:^1":
  version: 1.3.5
  resolution: "@types/mime@npm:1.3.5"
  checksum: 10c0/c2ee31cd9b993804df33a694d5aa3fa536511a49f2e06eeab0b484fef59b4483777dbb9e42a4198a0809ffbf698081fdbca1e5c2218b82b91603dfab10a10fbc
  languageName: node
  linkType: hard

"@types/mysql@npm:2.15.26":
  version: 2.15.26
  resolution: "@types/mysql@npm:2.15.26"
  dependencies:
    "@types/node": "npm:*"
  checksum: 10c0/3cf279e7db05d56c0544532a4380b9079f579092379a04c8138bd5cf88dda5b31208ac2d23ce7dbf4e3a3f43aaeed44e72f9f19f726518f308efe95a7435619a
  languageName: node
  linkType: hard

"@types/node@npm:*, @types/node@npm:>=13.7.0":
  version: 24.0.3
  resolution: "@types/node@npm:24.0.3"
  dependencies:
    undici-types: "npm:~7.8.0"
  checksum: 10c0/9c3c4e87600d1cf11e291c2fd4bfd806a615455463c30a0ef6dc9c801b3423344d9b82b8084e3ccabce485a7421ebb61a66e9676181bd7d9aea4759998a120d5
  languageName: node
  linkType: hard

"@types/node@npm:^22.15.3":
  version: 22.15.32
  resolution: "@types/node@npm:22.15.32"
  dependencies:
    undici-types: "npm:~6.21.0"
  checksum: 10c0/63a2fa52adf1134d1b3bee8b1862d4b8e4550fffc190551068d3d41a41d9e5c0c8f1cb81faa18767b260637360f662115c26c5e4e7718868ead40c4a57cbc0e3
  languageName: node
  linkType: hard

"@types/oracledb@npm:6.5.2":
  version: 6.5.2
  resolution: "@types/oracledb@npm:6.5.2"
  dependencies:
    "@types/node": "npm:*"
  checksum: 10c0/16e6d2e4247222dddf7be01273946b7f6a686327ce440be861671a2a0b98fe1a0d42df849d039a3f58aa1014f1c9d803f3c9793531a476077d762423ac911e65
  languageName: node
  linkType: hard

"@types/pg-pool@npm:2.0.6":
  version: 2.0.6
  resolution: "@types/pg-pool@npm:2.0.6"
  dependencies:
    "@types/pg": "npm:*"
  checksum: 10c0/41965d4d0b677c54ce45d36add760e496d356b78019cb062d124af40287cf6b0fd4d86e3b0085f443856c185983a60c8b0795ff76d15683e2a93c62f5ac0125f
  languageName: node
  linkType: hard

"@types/pg@npm:*":
  version: 8.15.4
  resolution: "@types/pg@npm:8.15.4"
  dependencies:
    "@types/node": "npm:*"
    pg-protocol: "npm:*"
    pg-types: "npm:^2.2.0"
  checksum: 10c0/7f9295cb2d934681bba84f7caad529c3b100d87e83ad0732c7fe496f4f79e42a795097321db54e010fcff22cb5e410cf683b4c9941907ee4564c822242816e91
  languageName: node
  linkType: hard

"@types/pg@npm:8.6.1":
  version: 8.6.1
  resolution: "@types/pg@npm:8.6.1"
  dependencies:
    "@types/node": "npm:*"
    pg-protocol: "npm:*"
    pg-types: "npm:^2.2.0"
  checksum: 10c0/8d16660c9a4f050d6d5e391c59f9a62e9d377a2a6a7eb5865f8828082dbdfeab700fd707e585f42d67b29e796b32863aea5bd6d5cbb8ceda2d598da5d0c61693
  languageName: node
  linkType: hard

"@types/phoenix@npm:^1.6.6":
  version: 1.6.6
  resolution: "@types/phoenix@npm:1.6.6"
  checksum: 10c0/4dfcb3fd36341ed5500de030291af14163c599857e00d2d4ff065d4c4600317d5d20aa170913fb9609747a09436e3add44db7d0c709bdf80f36cddcc67a42021
  languageName: node
  linkType: hard

"@types/qs@npm:*":
  version: 6.14.0
  resolution: "@types/qs@npm:6.14.0"
  checksum: 10c0/5b3036df6e507483869cdb3858201b2e0b64b4793dc4974f188caa5b5732f2333ab9db45c08157975054d3b070788b35088b4bc60257ae263885016ee2131310
  languageName: node
  linkType: hard

"@types/range-parser@npm:*":
  version: 1.2.7
  resolution: "@types/range-parser@npm:1.2.7"
  checksum: 10c0/361bb3e964ec5133fa40644a0b942279ed5df1949f21321d77de79f48b728d39253e5ce0408c9c17e4e0fd95ca7899da36841686393b9f7a1e209916e9381a3c
  languageName: node
  linkType: hard

"@types/resolve@npm:1.20.2":
  version: 1.20.2
  resolution: "@types/resolve@npm:1.20.2"
  checksum: 10c0/c5b7e1770feb5ccfb6802f6ad82a7b0d50874c99331e0c9b259e415e55a38d7a86ad0901c57665d93f75938be2a6a0bc9aa06c9749192cadb2e4512800bbc6e6
  languageName: node
  linkType: hard

"@types/send@npm:*":
  version: 0.17.5
  resolution: "@types/send@npm:0.17.5"
  dependencies:
    "@types/mime": "npm:^1"
    "@types/node": "npm:*"
  checksum: 10c0/a86c9b89bb0976ff58c1cdd56360ea98528f4dbb18a5c2287bb8af04815513a576a42b4e0e1e7c4d14f7d6ea54733f6ef935ebff8c65e86d9c222881a71e1f15
  languageName: node
  linkType: hard

"@types/serve-static@npm:*":
  version: 1.15.8
  resolution: "@types/serve-static@npm:1.15.8"
  dependencies:
    "@types/http-errors": "npm:*"
    "@types/node": "npm:*"
    "@types/send": "npm:*"
  checksum: 10c0/8ad86a25b87da5276cb1008c43c74667ff7583904d46d5fcaf0355887869d859d453d7dc4f890788ae04705c23720e9b6b6f3215e2d1d2a4278bbd090a9268dd
  languageName: node
  linkType: hard

"@types/shimmer@npm:^1.0.2, @types/shimmer@npm:^1.2.0":
  version: 1.2.0
  resolution: "@types/shimmer@npm:1.2.0"
  checksum: 10c0/6f7bfe1b55601cfc3ae713fc74a03341f3834253b8b91cb2add926d5949e4a63f7e666f59c2a6e40a883a5f9e2f3e3af10f9d3aed9b60fced0bda87659e58d8d
  languageName: node
  linkType: hard

"@types/stack-utils@npm:^2.0.0":
  version: 2.0.3
  resolution: "@types/stack-utils@npm:2.0.3"
  checksum: 10c0/1f4658385ae936330581bcb8aa3a066df03867d90281cdf89cc356d404bd6579be0f11902304e1f775d92df22c6dd761d4451c804b0a4fba973e06211e9bd77c
  languageName: node
  linkType: hard

"@types/superagent@npm:^8.1.0":
  version: 8.1.9
  resolution: "@types/superagent@npm:8.1.9"
  dependencies:
    "@types/cookiejar": "npm:^2.1.5"
    "@types/methods": "npm:^1.1.4"
    "@types/node": "npm:*"
    form-data: "npm:^4.0.0"
  checksum: 10c0/12631f1d8b3a62e1f435bc885f6d64d1a2d1ae82b80f0c6d63d4d6372c40b6f1fee6b3da59ac18bb86250b1eb73583bf2d4b1f7882048c32468791c560c69b7c
  languageName: node
  linkType: hard

"@types/supertest@npm:^6.0.3":
  version: 6.0.3
  resolution: "@types/supertest@npm:6.0.3"
  dependencies:
    "@types/methods": "npm:^1.1.4"
    "@types/superagent": "npm:^8.1.0"
  checksum: 10c0/a2080f870154b09db123864a484fb633bc9e2a0f7294a194388df4c7effe5af9de36d5a5ebf819f72b404fa47b5e813c47d5a3a51354251fd2fa8589bfb64f2c
  languageName: node
  linkType: hard

"@types/tedious@npm:^4.0.14":
  version: 4.0.14
  resolution: "@types/tedious@npm:4.0.14"
  dependencies:
    "@types/node": "npm:*"
  checksum: 10c0/d2914f8e9b5b998e4275ec5f0130cba1c2fb47e75616b5c125a65ef6c1db2f1dc3f978c7900693856a15d72bbb4f4e94f805537a4ecb6dc126c64415d31c0590
  languageName: node
  linkType: hard

"@types/unist@npm:*, @types/unist@npm:^3.0.0":
  version: 3.0.3
  resolution: "@types/unist@npm:3.0.3"
  checksum: 10c0/2b1e4adcab78388e088fcc3c0ae8700f76619dbcb4741d7d201f87e2cb346bfc29a89003cfea2d76c996e1061452e14fcd737e8b25aacf949c1f2d6b2bc3dd60
  languageName: node
  linkType: hard

"@types/uuid@npm:^10.0.0":
  version: 10.0.0
  resolution: "@types/uuid@npm:10.0.0"
  checksum: 10c0/9a1404bf287164481cb9b97f6bb638f78f955be57c40c6513b7655160beb29df6f84c915aaf4089a1559c216557dc4d2f79b48d978742d3ae10b937420ddac60
  languageName: node
  linkType: hard

"@types/uuid@npm:^9.0.1":
  version: 9.0.8
  resolution: "@types/uuid@npm:9.0.8"
  checksum: 10c0/b411b93054cb1d4361919579ef3508a1f12bf15b5fdd97337d3d351bece6c921b52b6daeef89b62340fd73fd60da407878432a1af777f40648cbe53a01723489
  languageName: node
  linkType: hard

"@types/ws@npm:^8.18.1":
  version: 8.18.1
  resolution: "@types/ws@npm:8.18.1"
  dependencies:
    "@types/node": "npm:*"
  checksum: 10c0/61aff1129143fcc4312f083bc9e9e168aa3026b7dd6e70796276dcfb2c8211c4292603f9c4864fae702f2ed86e4abd4d38aa421831c2fd7f856c931a481afbab
  languageName: node
  linkType: hard

"@types/yargs-parser@npm:*":
  version: 21.0.3
  resolution: "@types/yargs-parser@npm:21.0.3"
  checksum: 10c0/e71c3bd9d0b73ca82e10bee2064c384ab70f61034bbfb78e74f5206283fc16a6d85267b606b5c22cb2a3338373586786fed595b2009825d6a9115afba36560a0
  languageName: node
  linkType: hard

"@types/yargs@npm:^17.0.8":
  version: 17.0.33
  resolution: "@types/yargs@npm:17.0.33"
  dependencies:
    "@types/yargs-parser": "npm:*"
  checksum: 10c0/d16937d7ac30dff697801c3d6f235be2166df42e4a88bf730fa6dc09201de3727c0a9500c59a672122313341de5f24e45ee0ff579c08ce91928e519090b7906b
  languageName: node
  linkType: hard

"@ungap/structured-clone@npm:^1.0.0":
  version: 1.3.0
  resolution: "@ungap/structured-clone@npm:1.3.0"
  checksum: 10c0/0fc3097c2540ada1fc340ee56d58d96b5b536a2a0dab6e3ec17d4bfc8c4c86db345f61a375a8185f9da96f01c69678f836a2b57eeaa9e4b8eeafd26428e57b0a
  languageName: node
  linkType: hard

"@upstash/redis@npm:^1.35.0":
  version: 1.35.0
  resolution: "@upstash/redis@npm:1.35.0"
  dependencies:
    uncrypto: "npm:^0.1.3"
  checksum: 10c0/9276bd1b093c17c5acb235537f1cb1eccfce7be77d5a9fbb4caaf4dcfb85eda8c9e3dcea93a0bf14d4dc1c5e39d201620eb0ffc4249476a6c19dbcd83be207a2
  languageName: node
  linkType: hard

"@webcontainer/env@npm:^1.1.1":
  version: 1.1.1
  resolution: "@webcontainer/env@npm:1.1.1"
  checksum: 10c0/bc64114ffa7ee92f4985cc2bdd5e27f6f31d892b9aa5cde68eaf93df02d13ee6edf13faeebdd701464183b6f8f9c47c14975958cdd6fc20e7356ad32f6ee39e7
  languageName: node
  linkType: hard

"abbrev@npm:^3.0.0":
  version: 3.0.1
  resolution: "abbrev@npm:3.0.1"
  checksum: 10c0/21ba8f574ea57a3106d6d35623f2c4a9111d9ee3e9a5be47baed46ec2457d2eac46e07a5c4a60186f88cb98abbe3e24f2d4cca70bc2b12f1692523e2209a9ccf
  languageName: node
  linkType: hard

"abort-controller-x@npm:^0.4.0, abort-controller-x@npm:^0.4.3":
  version: 0.4.3
  resolution: "abort-controller-x@npm:0.4.3"
  checksum: 10c0/8091b5c9279c304890e4e9cc90601947790846b7b2c149bb322a25e873eb3db060ef3da74a93b6fe40ccea41c3962fc4b175468a0ecdf4c4bb6421023ad9d71e
  languageName: node
  linkType: hard

"abort-controller@npm:^3.0.0":
  version: 3.0.0
  resolution: "abort-controller@npm:3.0.0"
  dependencies:
    event-target-shim: "npm:^5.0.0"
  checksum: 10c0/90ccc50f010250152509a344eb2e71977fbf8db0ab8f1061197e3275ddf6c61a41a6edfd7b9409c664513131dd96e962065415325ef23efa5db931b382d24ca5
  languageName: node
  linkType: hard

"accepts@npm:^2.0.0":
  version: 2.0.0
  resolution: "accepts@npm:2.0.0"
  dependencies:
    mime-types: "npm:^3.0.0"
    negotiator: "npm:^1.0.0"
  checksum: 10c0/98374742097e140891546076215f90c32644feacf652db48412329de4c2a529178a81aa500fbb13dd3e6cbf6e68d829037b123ac037fc9a08bcec4b87b358eef
  languageName: node
  linkType: hard

"accepts@npm:~1.3.8":
  version: 1.3.8
  resolution: "accepts@npm:1.3.8"
  dependencies:
    mime-types: "npm:~2.1.34"
    negotiator: "npm:0.6.3"
  checksum: 10c0/3a35c5f5586cfb9a21163ca47a5f77ac34fa8ceb5d17d2fa2c0d81f41cbd7f8c6fa52c77e2c039acc0f4d09e71abdc51144246900f6bef5e3c4b333f77d89362
  languageName: node
  linkType: hard

"acorn-import-attributes@npm:^1.9.5":
  version: 1.9.5
  resolution: "acorn-import-attributes@npm:1.9.5"
  peerDependencies:
    acorn: ^8
  checksum: 10c0/5926eaaead2326d5a86f322ff1b617b0f698aa61dc719a5baa0e9d955c9885cc71febac3fb5bacff71bbf2c4f9c12db2056883c68c53eb962c048b952e1e013d
  languageName: node
  linkType: hard

"acorn-walk@npm:^8.1.1":
  version: 8.3.4
  resolution: "acorn-walk@npm:8.3.4"
  dependencies:
    acorn: "npm:^8.11.0"
  checksum: 10c0/76537ac5fb2c37a64560feaf3342023dadc086c46da57da363e64c6148dc21b57d49ace26f949e225063acb6fb441eabffd89f7a3066de5ad37ab3e328927c62
  languageName: node
  linkType: hard

"acorn@npm:^8.11.0, acorn@npm:^8.14.0, acorn@npm:^8.4.1":
  version: 8.15.0
  resolution: "acorn@npm:8.15.0"
  bin:
    acorn: bin/acorn
  checksum: 10c0/dec73ff59b7d6628a01eebaece7f2bdb8bb62b9b5926dcad0f8931f2b8b79c2be21f6c68ac095592adb5adb15831a3635d9343e6a91d028bbe85d564875ec3ec
  languageName: node
  linkType: hard

"agent-base@npm:^7.1.0, agent-base@npm:^7.1.2":
  version: 7.1.3
  resolution: "agent-base@npm:7.1.3"
  checksum: 10c0/6192b580c5b1d8fb399b9c62bf8343d76654c2dd62afcb9a52b2cf44a8b6ace1e3b704d3fe3547d91555c857d3df02603341ff2cb961b9cfe2b12f9f3c38ee11
  languageName: node
  linkType: hard

"ai@npm:^4.3.16":
  version: 4.3.16
  resolution: "ai@npm:4.3.16"
  dependencies:
    "@ai-sdk/provider": "npm:1.1.3"
    "@ai-sdk/provider-utils": "npm:2.2.8"
    "@ai-sdk/react": "npm:1.2.12"
    "@ai-sdk/ui-utils": "npm:1.2.11"
    "@opentelemetry/api": "npm:1.9.0"
    jsondiffpatch: "npm:0.6.0"
  peerDependencies:
    react: ^18 || ^19 || ^19.0.0-rc
    zod: ^3.23.8
  peerDependenciesMeta:
    react:
      optional: true
  checksum: 10c0/befe761c9386cda6de33370a2590900352b444d81959255c624e2bfd40765f126d29269f0ef3e00bde07daf237004aa0b66d0b253664aa478c148e923ce78c41
  languageName: node
  linkType: hard

"ajv@npm:^6.12.6":
  version: 6.12.6
  resolution: "ajv@npm:6.12.6"
  dependencies:
    fast-deep-equal: "npm:^3.1.1"
    fast-json-stable-stringify: "npm:^2.0.0"
    json-schema-traverse: "npm:^0.4.1"
    uri-js: "npm:^4.2.2"
  checksum: 10c0/41e23642cbe545889245b9d2a45854ebba51cda6c778ebced9649420d9205f2efb39cb43dbc41e358409223b1ea43303ae4839db682c848b891e4811da1a5a71
  languageName: node
  linkType: hard

"ansi-escapes@npm:^4.2.1":
  version: 4.3.2
  resolution: "ansi-escapes@npm:4.3.2"
  dependencies:
    type-fest: "npm:^0.21.3"
  checksum: 10c0/da917be01871525a3dfcf925ae2977bc59e8c513d4423368645634bf5d4ceba5401574eb705c1e92b79f7292af5a656f78c5725a4b0e1cec97c4b413705c1d50
  languageName: node
  linkType: hard

"ansi-escapes@npm:^5.0.0":
  version: 5.0.0
  resolution: "ansi-escapes@npm:5.0.0"
  dependencies:
    type-fest: "npm:^1.0.2"
  checksum: 10c0/f705cc7fbabb981ddf51562cd950792807bccd7260cc3d9478a619dda62bff6634c87ca100f2545ac7aade9b72652c4edad8c7f0d31a0b949b5fa58f33eaf0d0
  languageName: node
  linkType: hard

"ansi-regex@npm:^5.0.1":
  version: 5.0.1
  resolution: "ansi-regex@npm:5.0.1"
  checksum: 10c0/9a64bb8627b434ba9327b60c027742e5d17ac69277960d041898596271d992d4d52ba7267a63ca10232e29f6107fc8a835f6ce8d719b88c5f8493f8254813737
  languageName: node
  linkType: hard

"ansi-regex@npm:^6.0.1":
  version: 6.1.0
  resolution: "ansi-regex@npm:6.1.0"
  checksum: 10c0/a91daeddd54746338478eef88af3439a7edf30f8e23196e2d6ed182da9add559c601266dbef01c2efa46a958ad6f1f8b176799657616c702b5b02e799e7fd8dc
  languageName: node
  linkType: hard

"ansi-styles@npm:^4.0.0, ansi-styles@npm:^4.1.0":
  version: 4.3.0
  resolution: "ansi-styles@npm:4.3.0"
  dependencies:
    color-convert: "npm:^2.0.1"
  checksum: 10c0/895a23929da416f2bd3de7e9cb4eabd340949328ab85ddd6e484a637d8f6820d485f53933446f5291c3b760cbc488beb8e88573dd0f9c7daf83dccc8fe81b041
  languageName: node
  linkType: hard

"ansi-styles@npm:^5.0.0":
  version: 5.2.0
  resolution: "ansi-styles@npm:5.2.0"
  checksum: 10c0/9c4ca80eb3c2fb7b33841c210d2f20807f40865d27008d7c3f707b7f95cab7d67462a565e2388ac3285b71cb3d9bb2173de8da37c57692a362885ec34d6e27df
  languageName: node
  linkType: hard

"ansi-styles@npm:^6.1.0":
  version: 6.2.1
  resolution: "ansi-styles@npm:6.2.1"
  checksum: 10c0/5d1ec38c123984bcedd996eac680d548f31828bd679a66db2bdf11844634dde55fec3efa9c6bb1d89056a5e79c1ac540c4c784d592ea1d25028a92227d2f2d5c
  languageName: node
  linkType: hard

"anymatch@npm:^3.0.3, anymatch@npm:~3.1.2":
  version: 3.1.3
  resolution: "anymatch@npm:3.1.3"
  dependencies:
    normalize-path: "npm:^3.0.0"
    picomatch: "npm:^2.0.4"
  checksum: 10c0/57b06ae984bc32a0d22592c87384cd88fe4511b1dd7581497831c56d41939c8a001b28e7b853e1450f2bf61992dfcaa8ae2d0d161a0a90c4fb631ef07098fbac
  languageName: node
  linkType: hard

"append-field@npm:^1.0.0":
  version: 1.0.0
  resolution: "append-field@npm:1.0.0"
  checksum: 10c0/1b5abcc227e5179936a9e4f7e2af4769fa1f00eda85bbaed907f7964b0fd1f7d61f0f332b35337f391389ff13dd5310c2546ba670f8e5a743b23ec85185c73ef
  languageName: node
  linkType: hard

"arg@npm:^4.1.0":
  version: 4.1.3
  resolution: "arg@npm:4.1.3"
  checksum: 10c0/070ff801a9d236a6caa647507bdcc7034530604844d64408149a26b9e87c2f97650055c0f049abd1efc024b334635c01f29e0b632b371ac3f26130f4cf65997a
  languageName: node
  linkType: hard

"argparse@npm:^1.0.7":
  version: 1.0.10
  resolution: "argparse@npm:1.0.10"
  dependencies:
    sprintf-js: "npm:~1.0.2"
  checksum: 10c0/b2972c5c23c63df66bca144dbc65d180efa74f25f8fd9b7d9a0a6c88ae839db32df3d54770dcb6460cf840d232b60695d1a6b1053f599d84e73f7437087712de
  languageName: node
  linkType: hard

"argparse@npm:^2.0.1":
  version: 2.0.1
  resolution: "argparse@npm:2.0.1"
  checksum: 10c0/c5640c2d89045371c7cedd6a70212a04e360fd34d6edeae32f6952c63949e3525ea77dbec0289d8213a99bbaeab5abfa860b5c12cf88a2e6cf8106e90dd27a7e
  languageName: node
  linkType: hard

"array-flatten@npm:1.1.1":
  version: 1.1.1
  resolution: "array-flatten@npm:1.1.1"
  checksum: 10c0/********************************************************************************************************************************
  languageName: node
  linkType: hard

"asap@npm:^2.0.0":
  version: 2.0.6
  resolution: "asap@npm:2.0.6"
  checksum: 10c0/c6d5e39fe1f15e4b87677460bd66b66050cd14c772269cee6688824c1410a08ab20254bb6784f9afb75af9144a9f9a7692d49547f4d19d715aeb7c0318f3136d
  languageName: node
  linkType: hard

"assert-options@npm:0.8.3":
  version: 0.8.3
  resolution: "assert-options@npm:0.8.3"
  checksum: 10c0/694d64be3e574352c5a1f0f762c6e5d43189f0dbb1817a0abbffe531058c4780a0411be8ef51a2fdda50009eaa2277962cbded5638887ce5314d8b8e098d9c18
  languageName: node
  linkType: hard

"async-mutex@npm:^0.5.0":
  version: 0.5.0
  resolution: "async-mutex@npm:0.5.0"
  dependencies:
    tslib: "npm:^2.4.0"
  checksum: 10c0/9096e6ad6b674c894d8ddd5aa4c512b09bb05931b8746ebd634952b05685608b2b0820ed5c406e6569919ff5fe237ab3c491e6f2887d6da6b6ba906db3ee9c32
  languageName: node
  linkType: hard

"async@npm:3.2.3":
  version: 3.2.3
  resolution: "async@npm:3.2.3"
  checksum: 10c0/109780c846f05109dde14412d916ae4ed6daf6f9aad0c4aa1dcf0d4da775a3a9e35e0e06e4e06ad9fed66f99ca15549da16f2f243c56103b346e9d3bcd9c943f
  languageName: node
  linkType: hard

"async@npm:^2.6.4":
  version: 2.6.4
  resolution: "async@npm:2.6.4"
  dependencies:
    lodash: "npm:^4.17.14"
  checksum: 10c0/0ebb3273ef96513389520adc88e0d3c45e523d03653cc9b66f5c46f4239444294899bfd13d2b569e7dbfde7da2235c35cf5fd3ece9524f935d41bbe4efccdad0
  languageName: node
  linkType: hard

"async@npm:^3.2.3":
  version: 3.2.6
  resolution: "async@npm:3.2.6"
  checksum: 10c0/36484bb15ceddf07078688d95e27076379cc2f87b10c03b6dd8a83e89475a3c8df5848859dd06a4c95af1e4c16fc973de0171a77f18ea00be899aca2a4f85e70
  languageName: node
  linkType: hard

"asynckit@npm:^0.4.0":
  version: 0.4.0
  resolution: "asynckit@npm:0.4.0"
  checksum: 10c0/d73e2ddf20c4eb9337e1b3df1a0f6159481050a5de457c55b14ea2e5cb6d90bb69e004c9af54737a5ee0917fcf2c9e25de67777bbe58261847846066ba75bc9d
  languageName: node
  linkType: hard

"atomic-sleep@npm:^1.0.0":
  version: 1.0.0
  resolution: "atomic-sleep@npm:1.0.0"
  checksum: 10c0/e329a6665512736a9bbb073e1761b4ec102f7926cce35037753146a9db9c8104f5044c1662e4a863576ce544fb8be27cd2be6bc8c1a40147d03f31eb1cfb6e8a
  languageName: node
  linkType: hard

"axios@npm:^1.8.2, axios@npm:^1.9.0":
  version: 1.10.0
  resolution: "axios@npm:1.10.0"
  dependencies:
    follow-redirects: "npm:^1.15.6"
    form-data: "npm:^4.0.0"
    proxy-from-env: "npm:^1.1.0"
  checksum: 10c0/2239cb269cc789eac22f5d1aabd58e1a83f8f364c92c2caa97b6f5cbb4ab2903d2e557d9dc670b5813e9bcdebfb149e783fb8ab3e45098635cd2f559b06bd5d8
  languageName: node
  linkType: hard

"babel-jest@npm:^29.7.0":
  version: 29.7.0
  resolution: "babel-jest@npm:29.7.0"
  dependencies:
    "@jest/transform": "npm:^29.7.0"
    "@types/babel__core": "npm:^7.1.14"
    babel-plugin-istanbul: "npm:^6.1.1"
    babel-preset-jest: "npm:^29.6.3"
    chalk: "npm:^4.0.0"
    graceful-fs: "npm:^4.2.9"
    slash: "npm:^3.0.0"
  peerDependencies:
    "@babel/core": ^7.8.0
  checksum: 10c0/2eda9c1391e51936ca573dd1aedfee07b14c59b33dbe16ef347873ddd777bcf6e2fc739681e9e9661ab54ef84a3109a03725be2ac32cd2124c07ea4401cbe8c1
  languageName: node
  linkType: hard

"babel-plugin-istanbul@npm:^6.1.1":
  version: 6.1.1
  resolution: "babel-plugin-istanbul@npm:6.1.1"
  dependencies:
    "@babel/helper-plugin-utils": "npm:^7.0.0"
    "@istanbuljs/load-nyc-config": "npm:^1.0.0"
    "@istanbuljs/schema": "npm:^0.1.2"
    istanbul-lib-instrument: "npm:^5.0.4"
    test-exclude: "npm:^6.0.0"
  checksum: 10c0/1075657feb705e00fd9463b329921856d3775d9867c5054b449317d39153f8fbcebd3e02ebf00432824e647faff3683a9ca0a941325ef1afe9b3c4dd51b24beb
  languageName: node
  linkType: hard

"babel-plugin-jest-hoist@npm:^29.6.3":
  version: 29.6.3
  resolution: "babel-plugin-jest-hoist@npm:29.6.3"
  dependencies:
    "@babel/template": "npm:^7.3.3"
    "@babel/types": "npm:^7.3.3"
    "@types/babel__core": "npm:^7.1.14"
    "@types/babel__traverse": "npm:^7.0.6"
  checksum: 10c0/7e6451caaf7dce33d010b8aafb970e62f1b0c0b57f4978c37b0d457bbcf0874d75a395a102daf0bae0bd14eafb9f6e9a165ee5e899c0a4f1f3bb2e07b304ed2e
  languageName: node
  linkType: hard

"babel-preset-current-node-syntax@npm:^1.0.0":
  version: 1.1.0
  resolution: "babel-preset-current-node-syntax@npm:1.1.0"
  dependencies:
    "@babel/plugin-syntax-async-generators": "npm:^7.8.4"
    "@babel/plugin-syntax-bigint": "npm:^7.8.3"
    "@babel/plugin-syntax-class-properties": "npm:^7.12.13"
    "@babel/plugin-syntax-class-static-block": "npm:^7.14.5"
    "@babel/plugin-syntax-import-attributes": "npm:^7.24.7"
    "@babel/plugin-syntax-import-meta": "npm:^7.10.4"
    "@babel/plugin-syntax-json-strings": "npm:^7.8.3"
    "@babel/plugin-syntax-logical-assignment-operators": "npm:^7.10.4"
    "@babel/plugin-syntax-nullish-coalescing-operator": "npm:^7.8.3"
    "@babel/plugin-syntax-numeric-separator": "npm:^7.10.4"
    "@babel/plugin-syntax-object-rest-spread": "npm:^7.8.3"
    "@babel/plugin-syntax-optional-catch-binding": "npm:^7.8.3"
    "@babel/plugin-syntax-optional-chaining": "npm:^7.8.3"
    "@babel/plugin-syntax-private-property-in-object": "npm:^7.14.5"
    "@babel/plugin-syntax-top-level-await": "npm:^7.14.5"
  peerDependencies:
    "@babel/core": ^7.0.0
  checksum: 10c0/0b838d4412e3322cb4436f246e24e9c00bebcedfd8f00a2f51489db683bd35406bbd55a700759c28d26959c6e03f84dd6a1426f576f440267c1d7a73c5717281
  languageName: node
  linkType: hard

"babel-preset-jest@npm:^29.6.3":
  version: 29.6.3
  resolution: "babel-preset-jest@npm:29.6.3"
  dependencies:
    babel-plugin-jest-hoist: "npm:^29.6.3"
    babel-preset-current-node-syntax: "npm:^1.0.0"
  peerDependencies:
    "@babel/core": ^7.0.0
  checksum: 10c0/ec5fd0276b5630b05f0c14bb97cc3815c6b31600c683ebb51372e54dcb776cff790bdeeabd5b8d01ede375a040337ccbf6a3ccd68d3a34219125945e167ad943
  languageName: node
  linkType: hard

"balanced-match@npm:^1.0.0":
  version: 1.0.2
  resolution: "balanced-match@npm:1.0.2"
  checksum: 10c0/9308baf0a7e4838a82bbfd11e01b1cb0f0cf2893bc1676c27c2a8c0e70cbae1c59120c3268517a8ae7fb6376b4639ef81ca22582611dbee4ed28df945134aaee
  languageName: node
  linkType: hard

"base64-arraybuffer@npm:^1.0.2":
  version: 1.0.2
  resolution: "base64-arraybuffer@npm:1.0.2"
  checksum: 10c0/3acac95c70f9406e87a41073558ba85b6be9dbffb013a3d2a710e3f2d534d506c911847d5d9be4de458af6362c676de0a5c4c2d7bdf4def502d00b313368e72f
  languageName: node
  linkType: hard

"base64-js@npm:^1.3.1, base64-js@npm:^1.5.1":
  version: 1.5.1
  resolution: "base64-js@npm:1.5.1"
  checksum: 10c0/f23823513b63173a001030fae4f2dabe283b99a9d324ade3ad3d148e218134676f1ee8568c877cd79ec1c53158dcf2d2ba527a97c606618928ba99dd930102bf
  languageName: node
  linkType: hard

"bignumber.js@npm:^9.0.0":
  version: 9.3.0
  resolution: "bignumber.js@npm:9.3.0"
  checksum: 10c0/f54a79cd6fc98552ac0510c1cd9381650870ae443bdb20ba9b98e3548188d941506ac3c22a9f9c69b2cc60da9be5700e87d3f54d2825310a8b2ae999dfd6d99d
  languageName: node
  linkType: hard

"binary-extensions@npm:^2.0.0":
  version: 2.3.0
  resolution: "binary-extensions@npm:2.3.0"
  checksum: 10c0/75a59cafc10fb12a11d510e77110c6c7ae3f4ca22463d52487709ca7f18f69d886aa387557cc9864fbdb10153d0bdb4caacabf11541f55e89ed6e18d12ece2b5
  languageName: node
  linkType: hard

"body-parser@npm:1.20.3, body-parser@npm:^1.20.2":
  version: 1.20.3
  resolution: "body-parser@npm:1.20.3"
  dependencies:
    bytes: "npm:3.1.2"
    content-type: "npm:~1.0.5"
    debug: "npm:2.6.9"
    depd: "npm:2.0.0"
    destroy: "npm:1.2.0"
    http-errors: "npm:2.0.0"
    iconv-lite: "npm:0.4.24"
    on-finished: "npm:2.4.1"
    qs: "npm:6.13.0"
    raw-body: "npm:2.5.2"
    type-is: "npm:~1.6.18"
    unpipe: "npm:1.0.0"
  checksum: 10c0/0a9a93b7518f222885498dcecaad528cf010dd109b071bf471c93def4bfe30958b83e03496eb9c1ad4896db543d999bb62be1a3087294162a88cfa1b42c16310
  languageName: node
  linkType: hard

"body-parser@npm:^2.2.0":
  version: 2.2.0
  resolution: "body-parser@npm:2.2.0"
  dependencies:
    bytes: "npm:^3.1.2"
    content-type: "npm:^1.0.5"
    debug: "npm:^4.4.0"
    http-errors: "npm:^2.0.0"
    iconv-lite: "npm:^0.6.3"
    on-finished: "npm:^2.4.1"
    qs: "npm:^6.14.0"
    raw-body: "npm:^3.0.0"
    type-is: "npm:^2.0.0"
  checksum: 10c0/a9ded39e71ac9668e2211afa72e82ff86cc5ef94de1250b7d1ba9cc299e4150408aaa5f1e8b03dd4578472a3ce6d1caa2a23b27a6c18e526e48b4595174c116c
  languageName: node
  linkType: hard

"bowser@npm:^2.11.0":
  version: 2.11.0
  resolution: "bowser@npm:2.11.0"
  checksum: 10c0/04efeecc7927a9ec33c667fa0965dea19f4ac60b3fea60793c2e6cf06c1dcd2f7ae1dbc656f450c5f50783b1c75cf9dc173ba6f3b7db2feee01f8c4b793e1bd3
  languageName: node
  linkType: hard

"brace-expansion@npm:^1.1.7":
  version: 1.1.12
  resolution: "brace-expansion@npm:1.1.12"
  dependencies:
    balanced-match: "npm:^1.0.0"
    concat-map: "npm:0.0.1"
  checksum: 10c0/975fecac2bb7758c062c20d0b3b6288c7cc895219ee25f0a64a9de662dbac981ff0b6e89909c3897c1f84fa353113a721923afdec5f8b2350255b097f12b1f73
  languageName: node
  linkType: hard

"brace-expansion@npm:^2.0.1":
  version: 2.0.2
  resolution: "brace-expansion@npm:2.0.2"
  dependencies:
    balanced-match: "npm:^1.0.0"
  checksum: 10c0/6d117a4c793488af86b83172deb6af143e94c17bc53b0b3cec259733923b4ca84679d506ac261f4ba3c7ed37c46018e2ff442f9ce453af8643ecd64f4a54e6cf
  languageName: node
  linkType: hard

"braces@npm:^3.0.3, braces@npm:~3.0.2":
  version: 3.0.3
  resolution: "braces@npm:3.0.3"
  dependencies:
    fill-range: "npm:^7.1.1"
  checksum: 10c0/7c6dfd30c338d2997ba77500539227b9d1f85e388a5f43220865201e407e076783d0881f2d297b9f80951b4c957fcf0b51c1d2d24227631643c3f7c284b0aa04
  languageName: node
  linkType: hard

"browserslist@npm:^4.24.0":
  version: 4.25.0
  resolution: "browserslist@npm:4.25.0"
  dependencies:
    caniuse-lite: "npm:^1.0.30001718"
    electron-to-chromium: "npm:^1.5.160"
    node-releases: "npm:^2.0.19"
    update-browserslist-db: "npm:^1.1.3"
  bin:
    browserslist: cli.js
  checksum: 10c0/cc16c55b4468b18684a0e1ca303592b38635b1155d6724f172407192737a2f405b8030d87a05813729592793445b3d15e737b0055f901cdecccb29b1e580a1c5
  languageName: node
  linkType: hard

"bs-logger@npm:^0.2.6":
  version: 0.2.6
  resolution: "bs-logger@npm:0.2.6"
  dependencies:
    fast-json-stable-stringify: "npm:2.x"
  checksum: 10c0/80e89aaaed4b68e3374ce936f2eb097456a0dddbf11f75238dbd53140b1e39259f0d248a5089ed456f1158984f22191c3658d54a713982f676709fbe1a6fa5a0
  languageName: node
  linkType: hard

"bser@npm:2.1.1":
  version: 2.1.1
  resolution: "bser@npm:2.1.1"
  dependencies:
    node-int64: "npm:^0.4.0"
  checksum: 10c0/24d8dfb7b6d457d73f32744e678a60cc553e4ec0e9e1a01cf614b44d85c3c87e188d3cc78ef0442ce5032ee6818de20a0162ba1074725c0d08908f62ea979227
  languageName: node
  linkType: hard

"buffer-from@npm:^1.0.0":
  version: 1.1.2
  resolution: "buffer-from@npm:1.1.2"
  checksum: 10c0/124fff9d66d691a86d3b062eff4663fe437a9d9ee4b47b1b9e97f5a5d14f6d5399345db80f796827be7c95e70a8e765dd404b7c3ff3b3324f98e9b0c8826cc34
  languageName: node
  linkType: hard

"buffer@npm:^6.0.3":
  version: 6.0.3
  resolution: "buffer@npm:6.0.3"
  dependencies:
    base64-js: "npm:^1.3.1"
    ieee754: "npm:^1.2.1"
  checksum: 10c0/2a905fbbcde73cc5d8bd18d1caa23715d5f83a5935867c2329f0ac06104204ba7947be098fe1317fbd8830e26090ff8e764f08cd14fefc977bb248c3487bcbd0
  languageName: node
  linkType: hard

"builtins@npm:^5.1.0":
  version: 5.1.0
  resolution: "builtins@npm:5.1.0"
  dependencies:
    semver: "npm:^7.0.0"
  checksum: 10c0/3c32fe5bd7ed4ff7dbd6fb14bcb9d7eaa7e967327f1899cd336f8625d3f46fceead0a53528f1e332aeaee757034ebb307cb2f1a37af2b86a3c5ad4845d01c0c8
  languageName: node
  linkType: hard

"bullmq@npm:^5.55.0":
  version: 5.55.0
  resolution: "bullmq@npm:5.55.0"
  dependencies:
    cron-parser: "npm:^4.9.0"
    ioredis: "npm:^5.4.1"
    msgpackr: "npm:^1.11.2"
    node-abort-controller: "npm:^3.1.1"
    semver: "npm:^7.5.4"
    tslib: "npm:^2.0.0"
    uuid: "npm:^9.0.0"
  checksum: 10c0/48d8dc889171c072d46971bdfa713d72f2a8385687b8191ab32998b4d1fca21a557c88bd8fa842048a4f7cea5c86459e88f838c747b4d9707595bada0a02254e
  languageName: node
  linkType: hard

"bundle-name@npm:^4.1.0":
  version: 4.1.0
  resolution: "bundle-name@npm:4.1.0"
  dependencies:
    run-applescript: "npm:^7.0.0"
  checksum: 10c0/8e575981e79c2bcf14d8b1c027a3775c095d362d1382312f444a7c861b0e21513c0bd8db5bd2b16e50ba0709fa622d4eab6b53192d222120305e68359daece29
  languageName: node
  linkType: hard

"busboy@npm:^1.6.0":
  version: 1.6.0
  resolution: "busboy@npm:1.6.0"
  dependencies:
    streamsearch: "npm:^1.1.0"
  checksum: 10c0/fa7e836a2b82699b6e074393428b91ae579d4f9e21f5ac468e1b459a244341d722d2d22d10920cdd849743dbece6dca11d72de939fb75a7448825cf2babfba1f
  languageName: node
  linkType: hard

"bytes@npm:3.1.2, bytes@npm:^3.1.2":
  version: 3.1.2
  resolution: "bytes@npm:3.1.2"
  checksum: 10c0/76d1c43cbd602794ad8ad2ae94095cddeb1de78c5dddaa7005c51af10b0176c69971a6d88e805a90c2b6550d76636e43c40d8427a808b8645ede885de4a0358e
  languageName: node
  linkType: hard

"cacache@npm:^19.0.1":
  version: 19.0.1
  resolution: "cacache@npm:19.0.1"
  dependencies:
    "@npmcli/fs": "npm:^4.0.0"
    fs-minipass: "npm:^3.0.0"
    glob: "npm:^10.2.2"
    lru-cache: "npm:^10.0.1"
    minipass: "npm:^7.0.3"
    minipass-collect: "npm:^2.0.1"
    minipass-flush: "npm:^1.0.5"
    minipass-pipeline: "npm:^1.2.4"
    p-map: "npm:^7.0.2"
    ssri: "npm:^12.0.0"
    tar: "npm:^7.4.3"
    unique-filename: "npm:^4.0.0"
  checksum: 10c0/01f2134e1bd7d3ab68be851df96c8d63b492b1853b67f2eecb2c37bb682d37cb70bb858a16f2f0554d3c0071be6dfe21456a1ff6fa4b7eed996570d6a25ffe9c
  languageName: node
  linkType: hard

"call-bind-apply-helpers@npm:^1.0.1, call-bind-apply-helpers@npm:^1.0.2":
  version: 1.0.2
  resolution: "call-bind-apply-helpers@npm:1.0.2"
  dependencies:
    es-errors: "npm:^1.3.0"
    function-bind: "npm:^1.1.2"
  checksum: 10c0/47bd9901d57b857590431243fea704ff18078b16890a6b3e021e12d279bbf211d039155e27d7566b374d49ee1f8189344bac9833dec7a20cdec370506361c938
  languageName: node
  linkType: hard

"call-bound@npm:^1.0.2":
  version: 1.0.4
  resolution: "call-bound@npm:1.0.4"
  dependencies:
    call-bind-apply-helpers: "npm:^1.0.2"
    get-intrinsic: "npm:^1.3.0"
  checksum: 10c0/f4796a6a0941e71c766aea672f63b72bc61234c4f4964dc6d7606e3664c307e7d77845328a8f3359ce39ddb377fed67318f9ee203dea1d47e46165dcf2917644
  languageName: node
  linkType: hard

"callsites@npm:^3.0.0":
  version: 3.1.0
  resolution: "callsites@npm:3.1.0"
  checksum: 10c0/fff92277400eb06c3079f9e74f3af120db9f8ea03bad0e84d9aede54bbe2d44a56cccb5f6cf12211f93f52306df87077ecec5b712794c5a9b5dac6d615a3f301
  languageName: node
  linkType: hard

"camelcase@npm:^5.3.1":
  version: 5.3.1
  resolution: "camelcase@npm:5.3.1"
  checksum: 10c0/92ff9b443bfe8abb15f2b1513ca182d16126359ad4f955ebc83dc4ddcc4ef3fdd2c078bc223f2673dc223488e75c99b16cc4d056624374b799e6a1555cf61b23
  languageName: node
  linkType: hard

"camelcase@npm:^6.2.0":
  version: 6.3.0
  resolution: "camelcase@npm:6.3.0"
  checksum: 10c0/0d701658219bd3116d12da3eab31acddb3f9440790c0792e0d398f0a520a6a4058018e546862b6fba89d7ae990efaeb97da71e1913e9ebf5a8b5621a3d55c710
  languageName: node
  linkType: hard

"caniuse-lite@npm:^1.0.30001718":
  version: 1.0.30001724
  resolution: "caniuse-lite@npm:1.0.30001724"
  checksum: 10c0/ed9ec0bcf619f0e7ef2d33aac74d2346d1faf52060dfded1fb9c32d87854de5c2988b3ba338c281034c88bf797d6b55468a804ce8396a7e16a48cb0d481d4bfe
  languageName: node
  linkType: hard

"ccount@npm:^2.0.0":
  version: 2.0.1
  resolution: "ccount@npm:2.0.1"
  checksum: 10c0/3939b1664390174484322bc3f45b798462e6c07ee6384cb3d645e0aa2f318502d174845198c1561930e1d431087f74cf1fe291ae9a4722821a9f4ba67e574350
  languageName: node
  linkType: hard

"chalk@npm:^4.0.0, chalk@npm:^4.0.2, chalk@npm:^4.1.2":
  version: 4.1.2
  resolution: "chalk@npm:4.1.2"
  dependencies:
    ansi-styles: "npm:^4.1.0"
    supports-color: "npm:^7.1.0"
  checksum: 10c0/4a3fef5cc34975c898ffe77141450f679721df9dde00f6c304353fa9c8b571929123b26a0e4617bde5018977eb655b31970c297b91b63ee83bb82aeb04666880
  languageName: node
  linkType: hard

"chalk@npm:^5.2.0, chalk@npm:^5.3.0":
  version: 5.4.1
  resolution: "chalk@npm:5.4.1"
  checksum: 10c0/b23e88132c702f4855ca6d25cb5538b1114343e41472d5263ee8a37cccfccd9c4216d111e1097c6a27830407a1dc81fecdf2a56f2c63033d4dbbd88c10b0dcef
  languageName: node
  linkType: hard

"char-regex@npm:^1.0.2":
  version: 1.0.2
  resolution: "char-regex@npm:1.0.2"
  checksum: 10c0/57a09a86371331e0be35d9083ba429e86c4f4648ecbe27455dbfb343037c16ee6fdc7f6b61f433a57cc5ded5561d71c56a150e018f40c2ffb7bc93a26dae341e
  languageName: node
  linkType: hard

"character-entities-html4@npm:^2.0.0":
  version: 2.1.0
  resolution: "character-entities-html4@npm:2.1.0"
  checksum: 10c0/fe61b553f083400c20c0b0fd65095df30a0b445d960f3bbf271536ae6c3ba676f39cb7af0b4bf2755812f08ab9b88f2feed68f9aebb73bb153f7a115fe5c6e40
  languageName: node
  linkType: hard

"character-entities-legacy@npm:^3.0.0":
  version: 3.0.0
  resolution: "character-entities-legacy@npm:3.0.0"
  checksum: 10c0/ec4b430af873661aa754a896a2b55af089b4e938d3d010fad5219299a6b6d32ab175142699ee250640678cd64bdecd6db3c9af0b8759ab7b155d970d84c4c7d1
  languageName: node
  linkType: hard

"chatbot-ai-be@workspace:.":
  version: 0.0.0-use.local
  resolution: "chatbot-ai-be@workspace:."
  dependencies:
    "@ai-sdk/google": "npm:^1.2.19"
    "@ai-sdk/openai": "npm:^1.3.20"
    "@bull-board/api": "npm:^6.10.1"
    "@bull-board/express": "npm:^6.10.1"
    "@bull-board/ui": "npm:^6.10.1"
    "@mastra/client-js": "npm:^0.10.5"
    "@mastra/core": "npm:^0.10.8"
    "@mastra/loggers": "npm:^0.10.2"
    "@mastra/mcp": "npm:^0.10.4"
    "@mastra/memory": "npm:^0.11.0"
    "@mastra/pg": "npm:^0.12.0"
    "@openrouter/ai-sdk-provider": "npm:^0.4.5"
    "@opentelemetry/api": "npm:^1.8.0"
    "@supabase/supabase-js": "npm:^2.50.0"
    "@trigger.dev/build": "npm:^3.3.17"
    "@trigger.dev/sdk": "npm:^3.3.17"
    "@types/body-parser": "npm:^1.19.5"
    "@types/cors": "npm:^2.8.17"
    "@types/express": "npm:^4.17.21"
    "@types/jest": "npm:^29.5.14"
    "@types/node": "npm:^22.15.3"
    "@types/supertest": "npm:^6.0.3"
    ai: "npm:^4.3.16"
    axios: "npm:^1.9.0"
    base64-arraybuffer: "npm:^1.0.2"
    body-parser: "npm:^1.20.2"
    bullmq: "npm:^5.55.0"
    concurrently: "npm:^8.2.2"
    cors: "npm:^2.8.5"
    dotenv: "npm:^16.5.0"
    express: "npm:^4.18.3"
    ioredis: "npm:^5.6.1"
    jest: "npm:^29.7.0"
    langfuse-vercel: "npm:^3.37.2"
    mastra: "npm:^0.10.6"
    multer: "npm:^2.0.0"
    nodemon: "npm:^3.1.10"
    postgres: "npm:^3.4.5"
    redis-server: "npm:^1.2.2"
    rimraf: "npm:^5.0.5"
    supertest: "npm:^7.1.0"
    ts-jest: "npm:^29.3.2"
    ts-node: "npm:^10.9.2"
    tsx: "npm:^4.19.3"
    typescript: "npm:^5.8.3"
    weaviate-client: "npm:^3.5.2"
    zod: "npm:^3.24.3"
  languageName: unknown
  linkType: soft

"chokidar@npm:^3.5.2":
  version: 3.6.0
  resolution: "chokidar@npm:3.6.0"
  dependencies:
    anymatch: "npm:~3.1.2"
    braces: "npm:~3.0.2"
    fsevents: "npm:~2.3.2"
    glob-parent: "npm:~5.1.2"
    is-binary-path: "npm:~2.1.0"
    is-glob: "npm:~4.0.1"
    normalize-path: "npm:~3.0.0"
    readdirp: "npm:~3.6.0"
  dependenciesMeta:
    fsevents:
      optional: true
  checksum: 10c0/8361dcd013f2ddbe260eacb1f3cb2f2c6f2b0ad118708a343a5ed8158941a39cb8fb1d272e0f389712e74ee90ce8ba864eece9e0e62b9705cb468a2f6d917462
  languageName: node
  linkType: hard

"chownr@npm:^3.0.0":
  version: 3.0.0
  resolution: "chownr@npm:3.0.0"
  checksum: 10c0/43925b87700f7e3893296c8e9c56cc58f926411cce3a6e5898136daaf08f08b9a8eb76d37d3267e707d0dcc17aed2e2ebdf5848c0c3ce95cf910a919935c1b10
  languageName: node
  linkType: hard

"ci-info@npm:^3.2.0":
  version: 3.9.0
  resolution: "ci-info@npm:3.9.0"
  checksum: 10c0/6f0109e36e111684291d46123d491bc4e7b7a1934c3a20dea28cba89f1d4a03acd892f5f6a81ed3855c38647e285a150e3c9ba062e38943bef57fee6c1554c3a
  languageName: node
  linkType: hard

"cjs-module-lexer@npm:^1.0.0, cjs-module-lexer@npm:^1.2.2":
  version: 1.4.3
  resolution: "cjs-module-lexer@npm:1.4.3"
  checksum: 10c0/076b3af85adc4d65dbdab1b5b240fe5b45d44fcf0ef9d429044dd94d19be5589376805c44fb2d4b3e684e5fe6a9b7cf3e426476a6507c45283c5fc6ff95240be
  languageName: node
  linkType: hard

"cliui@npm:^8.0.1":
  version: 8.0.1
  resolution: "cliui@npm:8.0.1"
  dependencies:
    string-width: "npm:^4.2.0"
    strip-ansi: "npm:^6.0.1"
    wrap-ansi: "npm:^7.0.0"
  checksum: 10c0/4bda0f09c340cbb6dfdc1ed508b3ca080f12992c18d68c6be4d9cf51756033d5266e61ec57529e610dacbf4da1c634423b0c1b11037709cc6b09045cbd815df5
  languageName: node
  linkType: hard

"clone@npm:^2.1.2":
  version: 2.1.2
  resolution: "clone@npm:2.1.2"
  checksum: 10c0/ed0601cd0b1606bc7d82ee7175b97e68d1dd9b91fd1250a3617b38d34a095f8ee0431d40a1a611122dcccb4f93295b4fdb94942aa763392b5fe44effa50c2d5e
  languageName: node
  linkType: hard

"cluster-key-slot@npm:1.1.2, cluster-key-slot@npm:^1.1.0":
  version: 1.1.2
  resolution: "cluster-key-slot@npm:1.1.2"
  checksum: 10c0/d7d39ca28a8786e9e801eeb8c770e3c3236a566625d7299a47bb71113fb2298ce1039596acb82590e598c52dbc9b1f088c8f587803e697cb58e1867a95ff94d3
  languageName: node
  linkType: hard

"co@npm:^4.6.0":
  version: 4.6.0
  resolution: "co@npm:4.6.0"
  checksum: 10c0/c0e85ea0ca8bf0a50cbdca82efc5af0301240ca88ebe3644a6ffb8ffe911f34d40f8fbcf8f1d52c5ddd66706abd4d3bfcd64259f1e8e2371d4f47573b0dc8c28
  languageName: node
  linkType: hard

"cohere-ai@npm:^7.17.1":
  version: 7.17.1
  resolution: "cohere-ai@npm:7.17.1"
  dependencies:
    "@aws-sdk/client-sagemaker": "npm:^3.583.0"
    "@aws-sdk/credential-providers": "npm:^3.583.0"
    "@aws-sdk/protocol-http": "npm:^3.374.0"
    "@aws-sdk/signature-v4": "npm:^3.374.0"
    convict: "npm:^6.2.4"
    form-data: "npm:^4.0.0"
    form-data-encoder: "npm:^4.0.2"
    formdata-node: "npm:^6.0.3"
    js-base64: "npm:3.7.2"
    node-fetch: "npm:2.7.0"
    qs: "npm:6.11.2"
    readable-stream: "npm:^4.5.2"
    url-join: "npm:4.0.1"
  checksum: 10c0/0a8320932b784f66ea6e56528c26d5fb177eceefeaeda00982ea0e62b6ab29abd49e34e0ef60823a106c58aa8859056604ab5247fdbf736816b3a083afc705de
  languageName: node
  linkType: hard

"collect-v8-coverage@npm:^1.0.0":
  version: 1.0.2
  resolution: "collect-v8-coverage@npm:1.0.2"
  checksum: 10c0/ed7008e2e8b6852c5483b444a3ae6e976e088d4335a85aa0a9db2861c5f1d31bd2d7ff97a60469b3388deeba661a619753afbe201279fb159b4b9548ab8269a1
  languageName: node
  linkType: hard

"color-convert@npm:^2.0.1":
  version: 2.0.1
  resolution: "color-convert@npm:2.0.1"
  dependencies:
    color-name: "npm:~1.1.4"
  checksum: 10c0/37e1150172f2e311fe1b2df62c6293a342ee7380da7b9cfdba67ea539909afbd74da27033208d01d6d5cfc65ee7868a22e18d7e7648e004425441c0f8a15a7d7
  languageName: node
  linkType: hard

"color-name@npm:~1.1.4":
  version: 1.1.4
  resolution: "color-name@npm:1.1.4"
  checksum: 10c0/a1a3f914156960902f46f7f56bc62effc6c94e84b2cae157a526b1c1f74b677a47ec602bf68a61abfa2b42d15b7c5651c6dbe72a43af720bc588dff885b10f95
  languageName: node
  linkType: hard

"colorette@npm:^2.0.7":
  version: 2.0.20
  resolution: "colorette@npm:2.0.20"
  checksum: 10c0/e94116ff33b0ff56f3b83b9ace895e5bf87c2a7a47b3401b8c3f3226e050d5ef76cf4072fb3325f9dc24d1698f9b730baf4e05eeaf861d74a1883073f4c98a40
  languageName: node
  linkType: hard

"colors@npm:1.0.x":
  version: 1.0.3
  resolution: "colors@npm:1.0.3"
  checksum: 10c0/f9e40dd8b3e1a65378a7ced3fced15ddfd60aaf38e99a7521a7fdb25056b15e092f651cd0f5aa1e9b04fa8ce3616d094e07fc6c2bb261e24098db1ddd3d09a1d
  languageName: node
  linkType: hard

"combined-stream@npm:^1.0.8":
  version: 1.0.8
  resolution: "combined-stream@npm:1.0.8"
  dependencies:
    delayed-stream: "npm:~1.0.0"
  checksum: 10c0/0dbb829577e1b1e839fa82b40c07ffaf7de8a09b935cadd355a73652ae70a88b4320db322f6634a4ad93424292fa80973ac6480986247f1734a1137debf271d5
  languageName: node
  linkType: hard

"comma-separated-tokens@npm:^2.0.0":
  version: 2.0.3
  resolution: "comma-separated-tokens@npm:2.0.3"
  checksum: 10c0/91f90f1aae320f1755d6957ef0b864fe4f54737f3313bd95e0802686ee2ca38bff1dd381964d00ae5db42912dd1f4ae5c2709644e82706ffc6f6842a813cdd67
  languageName: node
  linkType: hard

"commander@npm:^12.1.0":
  version: 12.1.0
  resolution: "commander@npm:12.1.0"
  checksum: 10c0/6e1996680c083b3b897bfc1cfe1c58dfbcd9842fd43e1aaf8a795fbc237f65efcc860a3ef457b318e73f29a4f4a28f6403c3d653d021d960e4632dd45bde54a9
  languageName: node
  linkType: hard

"commondir@npm:^1.0.1":
  version: 1.0.1
  resolution: "commondir@npm:1.0.1"
  checksum: 10c0/33a124960e471c25ee19280c9ce31ccc19574b566dc514fe4f4ca4c34fa8b0b57cf437671f5de380e11353ea9426213fca17687dd2ef03134fea2dbc53809fd6
  languageName: node
  linkType: hard

"component-emitter@npm:^1.3.0":
  version: 1.3.1
  resolution: "component-emitter@npm:1.3.1"
  checksum: 10c0/e4900b1b790b5e76b8d71b328da41482118c0f3523a516a41be598dc2785a07fd721098d9bf6e22d89b19f4fa4e1025160dc00317ea111633a3e4f75c2b86032
  languageName: node
  linkType: hard

"concat-map@npm:0.0.1":
  version: 0.0.1
  resolution: "concat-map@npm:0.0.1"
  checksum: 10c0/c996b1cfdf95b6c90fee4dae37e332c8b6eb7d106430c17d538034c0ad9a1630cb194d2ab37293b1bdd4d779494beee7786d586a50bd9376fd6f7bcc2bd4c98f
  languageName: node
  linkType: hard

"concat-stream@npm:^2.0.0":
  version: 2.0.0
  resolution: "concat-stream@npm:2.0.0"
  dependencies:
    buffer-from: "npm:^1.0.0"
    inherits: "npm:^2.0.3"
    readable-stream: "npm:^3.0.2"
    typedarray: "npm:^0.0.6"
  checksum: 10c0/29565dd9198fe1d8cf57f6cc71527dbc6ad67e12e4ac9401feb389c53042b2dceedf47034cbe702dfc4fd8df3ae7e6bfeeebe732cc4fa2674e484c13f04c219a
  languageName: node
  linkType: hard

"concurrently@npm:^8.2.2":
  version: 8.2.2
  resolution: "concurrently@npm:8.2.2"
  dependencies:
    chalk: "npm:^4.1.2"
    date-fns: "npm:^2.30.0"
    lodash: "npm:^4.17.21"
    rxjs: "npm:^7.8.1"
    shell-quote: "npm:^1.8.1"
    spawn-command: "npm:0.0.2"
    supports-color: "npm:^8.1.1"
    tree-kill: "npm:^1.2.2"
    yargs: "npm:^17.7.2"
  bin:
    conc: dist/bin/concurrently.js
    concurrently: dist/bin/concurrently.js
  checksum: 10c0/0e9683196fe9c071d944345d21d8f34aa6c0cc50c0dd897e95619f2f1c9eb4871dca851b2569da17888235b7335b4c821ca19deed35bebcd9a131ee5d247f34c
  languageName: node
  linkType: hard

"confbox@npm:^0.1.8":
  version: 0.1.8
  resolution: "confbox@npm:0.1.8"
  checksum: 10c0/fc2c68d97cb54d885b10b63e45bd8da83a8a71459d3ecf1825143dd4c7f9f1b696b3283e07d9d12a144c1301c2ebc7842380bdf0014e55acc4ae1c9550102418
  languageName: node
  linkType: hard

"content-disposition@npm:0.5.4":
  version: 0.5.4
  resolution: "content-disposition@npm:0.5.4"
  dependencies:
    safe-buffer: "npm:5.2.1"
  checksum: 10c0/bac0316ebfeacb8f381b38285dc691c9939bf0a78b0b7c2d5758acadad242d04783cee5337ba7d12a565a19075af1b3c11c728e1e4946de73c6ff7ce45f3f1bb
  languageName: node
  linkType: hard

"content-disposition@npm:^1.0.0":
  version: 1.0.0
  resolution: "content-disposition@npm:1.0.0"
  dependencies:
    safe-buffer: "npm:5.2.1"
  checksum: 10c0/c7b1ba0cea2829da0352ebc1b7f14787c73884bc707c8bc2271d9e3bf447b372270d09f5d3980dc5037c749ceef56b9a13fccd0b0001c87c3f12579967e4dd27
  languageName: node
  linkType: hard

"content-type@npm:^1.0.5, content-type@npm:~1.0.4, content-type@npm:~1.0.5":
  version: 1.0.5
  resolution: "content-type@npm:1.0.5"
  checksum: 10c0/b76ebed15c000aee4678c3707e0860cb6abd4e680a598c0a26e17f0bfae723ec9cc2802f0ff1bc6e4d80603719010431d2231018373d4dde10f9ccff9dadf5af
  languageName: node
  linkType: hard

"convert-source-map@npm:^2.0.0":
  version: 2.0.0
  resolution: "convert-source-map@npm:2.0.0"
  checksum: 10c0/8f2f7a27a1a011cc6cc88cc4da2d7d0cfa5ee0369508baae3d98c260bb3ac520691464e5bbe4ae7cdf09860c1d69ecc6f70c63c6e7c7f7e3f18ec08484dc7d9b
  languageName: node
  linkType: hard

"convict@npm:^6.2.4":
  version: 6.2.4
  resolution: "convict@npm:6.2.4"
  dependencies:
    lodash.clonedeep: "npm:^4.5.0"
    yargs-parser: "npm:^20.2.7"
  checksum: 10c0/82d4cd93bfe028948f4b145562d4a1650881952d0e4b615b014bbcdf3e5caaad6f5dc12b647eeb82c135eba1ce1a0922f7823c34d17162646f7030dc002985ad
  languageName: node
  linkType: hard

"cookie-signature@npm:1.0.6":
  version: 1.0.6
  resolution: "cookie-signature@npm:1.0.6"
  checksum: 10c0/b36fd0d4e3fef8456915fcf7742e58fbfcc12a17a018e0eb9501c9d5ef6893b596466f03b0564b81af29ff2538fd0aa4b9d54fe5ccbfb4c90ea50ad29fe2d221
  languageName: node
  linkType: hard

"cookie-signature@npm:^1.2.1":
  version: 1.2.2
  resolution: "cookie-signature@npm:1.2.2"
  checksum: 10c0/54e05df1a293b3ce81589b27dddc445f462f6fa6812147c033350cd3561a42bc14481674e05ed14c7bd0ce1e8bb3dc0e40851bad75415733711294ddce0b7bc6
  languageName: node
  linkType: hard

"cookie@npm:0.7.1":
  version: 0.7.1
  resolution: "cookie@npm:0.7.1"
  checksum: 10c0/5de60c67a410e7c8dc8a46a4b72eb0fe925871d057c9a5d2c0e8145c4270a4f81076de83410c4d397179744b478e33cd80ccbcc457abf40a9409ad27dcd21dde
  languageName: node
  linkType: hard

"cookie@npm:^0.7.1":
  version: 0.7.2
  resolution: "cookie@npm:0.7.2"
  checksum: 10c0/9596e8ccdbf1a3a88ae02cf5ee80c1c50959423e1022e4e60b91dd87c622af1da309253d8abdb258fb5e3eacb4f08e579dc58b4897b8087574eee0fd35dfa5d2
  languageName: node
  linkType: hard

"cookiejar@npm:^2.1.4":
  version: 2.1.4
  resolution: "cookiejar@npm:2.1.4"
  checksum: 10c0/2dae55611c6e1678f34d93984cbd4bda58f4fe3e5247cc4993f4a305cd19c913bbaf325086ed952e892108115073a747596453d3dc1c34947f47f731818b8ad1
  languageName: node
  linkType: hard

"copy-anything@npm:^3.0.2":
  version: 3.0.5
  resolution: "copy-anything@npm:3.0.5"
  dependencies:
    is-what: "npm:^4.1.8"
  checksum: 10c0/01eadd500c7e1db71d32d95a3bfaaedcb839ef891c741f6305ab0461398056133de08f2d1bf4c392b364e7bdb7ce498513896e137a7a183ac2516b065c28a4fe
  languageName: node
  linkType: hard

"cors@npm:^2.8.5":
  version: 2.8.5
  resolution: "cors@npm:2.8.5"
  dependencies:
    object-assign: "npm:^4"
    vary: "npm:^1"
  checksum: 10c0/373702b7999409922da80de4a61938aabba6929aea5b6fd9096fefb9e8342f626c0ebd7507b0e8b0b311380744cc985f27edebc0a26e0ddb784b54e1085de761
  languageName: node
  linkType: hard

"create-jest@npm:^29.7.0":
  version: 29.7.0
  resolution: "create-jest@npm:29.7.0"
  dependencies:
    "@jest/types": "npm:^29.6.3"
    chalk: "npm:^4.0.0"
    exit: "npm:^0.1.2"
    graceful-fs: "npm:^4.2.9"
    jest-config: "npm:^29.7.0"
    jest-util: "npm:^29.7.0"
    prompts: "npm:^2.0.1"
  bin:
    create-jest: bin/create-jest.js
  checksum: 10c0/e7e54c280692470d3398f62a6238fd396327e01c6a0757002833f06d00afc62dd7bfe04ff2b9cd145264460e6b4d1eb8386f2925b7e567f97939843b7b0e812f
  languageName: node
  linkType: hard

"create-require@npm:^1.1.0":
  version: 1.1.1
  resolution: "create-require@npm:1.1.1"
  checksum: 10c0/157cbc59b2430ae9a90034a5f3a1b398b6738bf510f713edc4d4e45e169bc514d3d99dd34d8d01ca7ae7830b5b8b537e46ae8f3c8f932371b0875c0151d7ec91
  languageName: node
  linkType: hard

"cron-parser@npm:^4.9.0":
  version: 4.9.0
  resolution: "cron-parser@npm:4.9.0"
  dependencies:
    luxon: "npm:^3.2.1"
  checksum: 10c0/348622bdcd1a15695b61fc33af8a60133e5913a85cf99f6344367579e7002896514ba3b0a9d6bb569b02667d6b06836722bf2295fcd101b3de378f71d37bed0b
  languageName: node
  linkType: hard

"cronstrue@npm:^2.21.0":
  version: 2.61.0
  resolution: "cronstrue@npm:2.61.0"
  bin:
    cronstrue: bin/cli.js
  checksum: 10c0/4c850b4440da9495359ad14d17e9fec06855f5b9e6871e8527a3ea7206887df944f868784f65bdf3d8ea09b99e69974429d81f88027a6774c1d08c2f5874069b
  languageName: node
  linkType: hard

"cross-fetch@npm:^3.1.5":
  version: 3.2.0
  resolution: "cross-fetch@npm:3.2.0"
  dependencies:
    node-fetch: "npm:^2.7.0"
  checksum: 10c0/d8596adf0269130098a676f6739a0922f3cc7b71cc89729925411ebe851a87026171c82ea89154c4811c9867c01c44793205a52e618ce2684650218c7fbeeb9f
  languageName: node
  linkType: hard

"cross-spawn@npm:^7.0.3, cross-spawn@npm:^7.0.5, cross-spawn@npm:^7.0.6":
  version: 7.0.6
  resolution: "cross-spawn@npm:7.0.6"
  dependencies:
    path-key: "npm:^3.1.0"
    shebang-command: "npm:^2.0.0"
    which: "npm:^2.0.1"
  checksum: 10c0/053ea8b2135caff68a9e81470e845613e374e7309a47731e81639de3eaeb90c3d01af0e0b44d2ab9d50b43467223b88567dfeb3262db942dc063b9976718ffc1
  languageName: node
  linkType: hard

"cycle@npm:1.0.x":
  version: 1.0.3
  resolution: "cycle@npm:1.0.3"
  checksum: 10c0/f38aae412cea9e895e963e0ff8d4d19852e53b630e7fc1dd078da551f3a4c0a98c5f026d4626dfc0b42648b804dabf13a56faace60b09cf6f3cc706c0819f119
  languageName: node
  linkType: hard

"date-fns@npm:^2.30.0":
  version: 2.30.0
  resolution: "date-fns@npm:2.30.0"
  dependencies:
    "@babel/runtime": "npm:^7.21.0"
  checksum: 10c0/e4b521fbf22bc8c3db332bbfb7b094fd3e7627de0259a9d17c7551e2d2702608a7307a449206065916538e384f37b181565447ce2637ae09828427aed9cb5581
  languageName: node
  linkType: hard

"date-fns@npm:^3.6.0":
  version: 3.6.0
  resolution: "date-fns@npm:3.6.0"
  checksum: 10c0/0b5fb981590ef2f8e5a3ba6cd6d77faece0ea7f7158948f2eaae7bbb7c80a8f63ae30b01236c2923cf89bb3719c33aeb150c715ea4fe4e86e37dcf06bed42fb6
  languageName: node
  linkType: hard

"date-fns@npm:^4.1.0":
  version: 4.1.0
  resolution: "date-fns@npm:4.1.0"
  checksum: 10c0/b79ff32830e6b7faa009590af6ae0fb8c3fd9ffad46d930548fbb5acf473773b4712ae887e156ba91a7b3dc30591ce0f517d69fd83bd9c38650fdc03b4e0bac8
  languageName: node
  linkType: hard

"dateformat@npm:^4.6.3":
  version: 4.6.3
  resolution: "dateformat@npm:4.6.3"
  checksum: 10c0/e2023b905e8cfe2eb8444fb558562b524807a51cdfe712570f360f873271600b5c94aebffaf11efb285e2c072264a7cf243eadb68f3eba0f8cc85fb86cd25df6
  languageName: node
  linkType: hard

"debug@npm:2.6.9":
  version: 2.6.9
  resolution: "debug@npm:2.6.9"
  dependencies:
    ms: "npm:2.0.0"
  checksum: 10c0/121908fb839f7801180b69a7e218a40b5a0b718813b886b7d6bdb82001b931c938e2941d1e4450f33a1b1df1da653f5f7a0440c197f29fbf8a6e9d45ff6ef589
  languageName: node
  linkType: hard

"debug@npm:4, debug@npm:^4, debug@npm:^4.1.0, debug@npm:^4.1.1, debug@npm:^4.3.1, debug@npm:^4.3.4, debug@npm:^4.3.5, debug@npm:^4.4.0":
  version: 4.4.1
  resolution: "debug@npm:4.4.1"
  dependencies:
    ms: "npm:^2.1.3"
  peerDependenciesMeta:
    supports-color:
      optional: true
  checksum: 10c0/d2b44bc1afd912b49bb7ebb0d50a860dc93a4dd7d946e8de94abc957bb63726b7dd5aa48c18c2386c379ec024c46692e15ed3ed97d481729f929201e671fcd55
  languageName: node
  linkType: hard

"debug@npm:4.3.1":
  version: 4.3.1
  resolution: "debug@npm:4.3.1"
  dependencies:
    ms: "npm:2.1.2"
  peerDependenciesMeta:
    supports-color:
      optional: true
  checksum: 10c0/610bcc2eb07c533d6a9964478422f7d741095d67301888ee0b77b8f2ad0a15d115c93fb2adb13d10a9eda3d81f2d4d335405540b09596fb23aca070e77497d95
  languageName: node
  linkType: hard

"debug@npm:~4.3.1, debug@npm:~4.3.2":
  version: 4.3.7
  resolution: "debug@npm:4.3.7"
  dependencies:
    ms: "npm:^2.1.3"
  peerDependenciesMeta:
    supports-color:
      optional: true
  checksum: 10c0/1471db19c3b06d485a622d62f65947a19a23fbd0dd73f7fd3eafb697eec5360cde447fb075919987899b1a2096e85d35d4eb5a4de09a57600ac9cf7e6c8e768b
  languageName: node
  linkType: hard

"dedent@npm:^1.0.0":
  version: 1.6.0
  resolution: "dedent@npm:1.6.0"
  peerDependencies:
    babel-plugin-macros: ^3.1.0
  peerDependenciesMeta:
    babel-plugin-macros:
      optional: true
  checksum: 10c0/671b8f5e390dd2a560862c4511dd6d2638e71911486f78cb32116551f8f2aa6fcaf50579ffffb2f866d46b5b80fd72470659ca5760ede8f967619ef7df79e8a5
  languageName: node
  linkType: hard

"deep-is@npm:^0.1.3":
  version: 0.1.4
  resolution: "deep-is@npm:0.1.4"
  checksum: 10c0/7f0ee496e0dff14a573dc6127f14c95061b448b87b995fc96c017ce0a1e66af1675e73f1d6064407975bc4ea6ab679497a29fff7b5b9c4e99cb10797c1ad0b4c
  languageName: node
  linkType: hard

"deepmerge@npm:^4.2.2":
  version: 4.3.1
  resolution: "deepmerge@npm:4.3.1"
  checksum: 10c0/e53481aaf1aa2c4082b5342be6b6d8ad9dfe387bc92ce197a66dea08bd4265904a087e75e464f14d1347cf2ac8afe1e4c16b266e0561cc5df29382d3c5f80044
  languageName: node
  linkType: hard

"default-browser-id@npm:^5.0.0":
  version: 5.0.0
  resolution: "default-browser-id@npm:5.0.0"
  checksum: 10c0/957fb886502594c8e645e812dfe93dba30ed82e8460d20ce39c53c5b0f3e2afb6ceaec2249083b90bdfbb4cb0f34e1f73fde3d68cac00becdbcfd894156b5ead
  languageName: node
  linkType: hard

"default-browser@npm:^5.2.1":
  version: 5.2.1
  resolution: "default-browser@npm:5.2.1"
  dependencies:
    bundle-name: "npm:^4.1.0"
    default-browser-id: "npm:^5.0.0"
  checksum: 10c0/73f17dc3c58026c55bb5538749597db31f9561c0193cd98604144b704a981c95a466f8ecc3c2db63d8bfd04fb0d426904834cfc91ae510c6aeb97e13c5167c4d
  languageName: node
  linkType: hard

"define-lazy-prop@npm:^3.0.0":
  version: 3.0.0
  resolution: "define-lazy-prop@npm:3.0.0"
  checksum: 10c0/5ab0b2bf3fa58b3a443140bbd4cd3db1f91b985cc8a246d330b9ac3fc0b6a325a6d82bddc0b055123d745b3f9931afeea74a5ec545439a1630b9c8512b0eeb49
  languageName: node
  linkType: hard

"delayed-stream@npm:~1.0.0":
  version: 1.0.0
  resolution: "delayed-stream@npm:1.0.0"
  checksum: 10c0/d758899da03392e6712f042bec80aa293bbe9e9ff1b2634baae6a360113e708b91326594c8a486d475c69d6259afb7efacdc3537bfcda1c6c648e390ce601b19
  languageName: node
  linkType: hard

"denque@npm:^2.1.0":
  version: 2.1.0
  resolution: "denque@npm:2.1.0"
  checksum: 10c0/f9ef81aa0af9c6c614a727cb3bd13c5d7db2af1abf9e6352045b86e85873e629690f6222f4edd49d10e4ccf8f078bbeec0794fafaf61b659c0589d0c511ec363
  languageName: node
  linkType: hard

"depd@npm:2.0.0, depd@npm:^2.0.0":
  version: 2.0.0
  resolution: "depd@npm:2.0.0"
  checksum: 10c0/58bd06ec20e19529b06f7ad07ddab60e504d9e0faca4bd23079fac2d279c3594334d736508dc350e06e510aba5e22e4594483b3a6562ce7c17dd797f4cc4ad2c
  languageName: node
  linkType: hard

"dequal@npm:^2.0.0, dequal@npm:^2.0.3":
  version: 2.0.3
  resolution: "dequal@npm:2.0.3"
  checksum: 10c0/f98860cdf58b64991ae10205137c0e97d384c3a4edc7f807603887b7c4b850af1224a33d88012009f150861cbee4fa2d322c4cc04b9313bee312e47f6ecaa888
  languageName: node
  linkType: hard

"destroy@npm:1.2.0":
  version: 1.2.0
  resolution: "destroy@npm:1.2.0"
  checksum: 10c0/bd7633942f57418f5a3b80d5cb53898127bcf53e24cdf5d5f4396be471417671f0fee48a4ebe9a1e9defbde2a31280011af58a57e090ff822f589b443ed4e643
  languageName: node
  linkType: hard

"detect-libc@npm:^2.0.1, detect-libc@npm:^2.0.4":
  version: 2.0.4
  resolution: "detect-libc@npm:2.0.4"
  checksum: 10c0/c15541f836eba4b1f521e4eecc28eefefdbc10a94d3b8cb4c507689f332cc111babb95deda66f2de050b22122113189986d5190be97d51b5a2b23b938415e67c
  languageName: node
  linkType: hard

"detect-newline@npm:^3.0.0":
  version: 3.1.0
  resolution: "detect-newline@npm:3.1.0"
  checksum: 10c0/c38cfc8eeb9fda09febb44bcd85e467c970d4e3bf526095394e5a4f18bc26dd0cf6b22c69c1fa9969261521c593836db335c2795218f6d781a512aea2fb8209d
  languageName: node
  linkType: hard

"devlop@npm:^1.0.0":
  version: 1.1.0
  resolution: "devlop@npm:1.1.0"
  dependencies:
    dequal: "npm:^2.0.0"
  checksum: 10c0/e0928ab8f94c59417a2b8389c45c55ce0a02d9ac7fd74ef62d01ba48060129e1d594501b77de01f3eeafc7cb00773819b0df74d96251cf20b31c5b3071f45c0e
  languageName: node
  linkType: hard

"dezalgo@npm:^1.0.4":
  version: 1.0.4
  resolution: "dezalgo@npm:1.0.4"
  dependencies:
    asap: "npm:^2.0.0"
    wrappy: "npm:1"
  checksum: 10c0/8a870ed42eade9a397e6141fe5c025148a59ed52f1f28b1db5de216b4d57f0af7a257070c3af7ce3d5508c1ce9dd5009028a76f4b2cc9370dc56551d2355fad8
  languageName: node
  linkType: hard

"diff-match-patch@npm:^1.0.5":
  version: 1.0.5
  resolution: "diff-match-patch@npm:1.0.5"
  checksum: 10c0/142b6fad627b9ef309d11bd935e82b84c814165a02500f046e2773f4ea894d10ed3017ac20454900d79d4a0322079f5b713cf0986aaf15fce0ec4a2479980c86
  languageName: node
  linkType: hard

"diff-sequences@npm:^29.6.3":
  version: 29.6.3
  resolution: "diff-sequences@npm:29.6.3"
  checksum: 10c0/32e27ac7dbffdf2fb0eb5a84efd98a9ad084fbabd5ac9abb8757c6770d5320d2acd172830b28c4add29bb873d59420601dfc805ac4064330ce59b1adfd0593b2
  languageName: node
  linkType: hard

"diff@npm:^4.0.1":
  version: 4.0.2
  resolution: "diff@npm:4.0.2"
  checksum: 10c0/81b91f9d39c4eaca068eb0c1eb0e4afbdc5bb2941d197f513dd596b820b956fef43485876226d65d497bebc15666aa2aa82c679e84f65d5f2bfbf14ee46e32c1
  languageName: node
  linkType: hard

"dotenv@npm:^16.5.0":
  version: 16.5.0
  resolution: "dotenv@npm:16.5.0"
  checksum: 10c0/5bc94c919fbd955bf0ba44d33922a1e93d1078e64a1db5c30faeded1d996e7a83c55332cb8ea4fae5a9ca4d0be44cbceb95c5811e70f9f095298df09d1997dd9
  languageName: node
  linkType: hard

"dunder-proto@npm:^1.0.1":
  version: 1.0.1
  resolution: "dunder-proto@npm:1.0.1"
  dependencies:
    call-bind-apply-helpers: "npm:^1.0.1"
    es-errors: "npm:^1.3.0"
    gopd: "npm:^1.2.0"
  checksum: 10c0/199f2a0c1c16593ca0a145dbf76a962f8033ce3129f01284d48c45ed4e14fea9bbacd7b3610b6cdc33486cef20385ac054948fefc6272fcce645c09468f93031
  languageName: node
  linkType: hard

"eastasianwidth@npm:^0.2.0":
  version: 0.2.0
  resolution: "eastasianwidth@npm:0.2.0"
  checksum: 10c0/26f364ebcdb6395f95124fda411f63137a4bfb5d3a06453f7f23dfe52502905bd84e0488172e0f9ec295fdc45f05c23d5d91baf16bd26f0fe9acd777a188dc39
  languageName: node
  linkType: hard

"ee-first@npm:1.1.1":
  version: 1.1.1
  resolution: "ee-first@npm:1.1.1"
  checksum: 10c0/b5bb125ee93161bc16bfe6e56c6b04de5ad2aa44234d8f644813cc95d861a6910903132b05093706de2b706599367c4130eb6d170f6b46895686b95f87d017b7
  languageName: node
  linkType: hard

"ejs@npm:^3.1.10":
  version: 3.1.10
  resolution: "ejs@npm:3.1.10"
  dependencies:
    jake: "npm:^10.8.5"
  bin:
    ejs: bin/cli.js
  checksum: 10c0/52eade9e68416ed04f7f92c492183340582a36482836b11eab97b159fcdcfdedc62233a1bf0bf5e5e1851c501f2dca0e2e9afd111db2599e4e7f53ee29429ae1
  languageName: node
  linkType: hard

"electron-to-chromium@npm:^1.5.160":
  version: 1.5.171
  resolution: "electron-to-chromium@npm:1.5.171"
  checksum: 10c0/e9d7e70d5fe829951c955287877155889a752336e48c715e373c6919f8e438bb686b7278511013aa8456c329c55895059a1d9e4b799217483f28dbae60c198d8
  languageName: node
  linkType: hard

"emittery@npm:^0.13.1":
  version: 0.13.1
  resolution: "emittery@npm:0.13.1"
  checksum: 10c0/1573d0ae29ab34661b6c63251ff8f5facd24ccf6a823f19417ae8ba8c88ea450325788c67f16c99edec8de4b52ce93a10fe441ece389fd156e88ee7dab9bfa35
  languageName: node
  linkType: hard

"emoji-regex-xs@npm:^1.0.0":
  version: 1.0.0
  resolution: "emoji-regex-xs@npm:1.0.0"
  checksum: 10c0/1082de006991eb05a3324ef0efe1950c7cdf66efc01d4578de82b0d0d62add4e55e97695a8a7eeda826c305081562dc79b477ddf18d886da77f3ba08c4b940a0
  languageName: node
  linkType: hard

"emoji-regex@npm:^8.0.0":
  version: 8.0.0
  resolution: "emoji-regex@npm:8.0.0"
  checksum: 10c0/b6053ad39951c4cf338f9092d7bfba448cdfd46fe6a2a034700b149ac9ffbc137e361cbd3c442297f86bed2e5f7576c1b54cc0a6bf8ef5106cc62f496af35010
  languageName: node
  linkType: hard

"emoji-regex@npm:^9.2.2":
  version: 9.2.2
  resolution: "emoji-regex@npm:9.2.2"
  checksum: 10c0/af014e759a72064cf66e6e694a7fc6b0ed3d8db680427b021a89727689671cefe9d04151b2cad51dbaf85d5ba790d061cd167f1cf32eb7b281f6368b3c181639
  languageName: node
  linkType: hard

"encodeurl@npm:^2.0.0, encodeurl@npm:~2.0.0":
  version: 2.0.0
  resolution: "encodeurl@npm:2.0.0"
  checksum: 10c0/5d317306acb13e6590e28e27924c754163946a2480de11865c991a3a7eed4315cd3fba378b543ca145829569eefe9b899f3d84bb09870f675ae60bc924b01ceb
  languageName: node
  linkType: hard

"encodeurl@npm:~1.0.2":
  version: 1.0.2
  resolution: "encodeurl@npm:1.0.2"
  checksum: 10c0/f6c2387379a9e7c1156c1c3d4f9cb7bb11cf16dd4c1682e1f6746512564b053df5781029b6061296832b59fb22f459dbe250386d217c2f6e203601abb2ee0bec
  languageName: node
  linkType: hard

"encoding@npm:^0.1.13":
  version: 0.1.13
  resolution: "encoding@npm:0.1.13"
  dependencies:
    iconv-lite: "npm:^0.6.2"
  checksum: 10c0/36d938712ff00fe1f4bac88b43bcffb5930c1efa57bbcdca9d67e1d9d6c57cfb1200fb01efe0f3109b2ce99b231f90779532814a81370a1bd3274a0f58585039
  languageName: node
  linkType: hard

"end-of-stream@npm:^1.1.0":
  version: 1.4.5
  resolution: "end-of-stream@npm:1.4.5"
  dependencies:
    once: "npm:^1.4.0"
  checksum: 10c0/b0701c92a10b89afb1cb45bf54a5292c6f008d744eb4382fa559d54775ff31617d1d7bc3ef617575f552e24fad2c7c1a1835948c66b3f3a4be0a6c1f35c883d8
  languageName: node
  linkType: hard

"engine.io-client@npm:~6.5.2":
  version: 6.5.4
  resolution: "engine.io-client@npm:6.5.4"
  dependencies:
    "@socket.io/component-emitter": "npm:~3.1.0"
    debug: "npm:~4.3.1"
    engine.io-parser: "npm:~5.2.1"
    ws: "npm:~8.17.1"
    xmlhttprequest-ssl: "npm:~2.0.0"
  checksum: 10c0/ef220f9875d6a43bade906bd9b61118e812474bbe46e80f38c92dca238484170daf92d51e58bbade6433c29ffb5ba329f4864c5609f2e33c5e31041b1f8ad672
  languageName: node
  linkType: hard

"engine.io-parser@npm:~5.2.1":
  version: 5.2.3
  resolution: "engine.io-parser@npm:5.2.3"
  checksum: 10c0/ed4900d8dbef470ab3839ccf3bfa79ee518ea8277c7f1f2759e8c22a48f64e687ea5e474291394d0c94f84054749fd93f3ef0acb51fa2f5f234cc9d9d8e7c536
  languageName: node
  linkType: hard

"env-paths@npm:^2.2.0":
  version: 2.2.1
  resolution: "env-paths@npm:2.2.1"
  checksum: 10c0/285325677bf00e30845e330eec32894f5105529db97496ee3f598478e50f008c5352a41a30e5e72ec9de8a542b5a570b85699cd63bd2bc646dbcb9f311d83bc4
  languageName: node
  linkType: hard

"err-code@npm:^2.0.2":
  version: 2.0.3
  resolution: "err-code@npm:2.0.3"
  checksum: 10c0/b642f7b4dd4a376e954947550a3065a9ece6733ab8e51ad80db727aaae0817c2e99b02a97a3d6cecc648a97848305e728289cf312d09af395403a90c9d4d8a66
  languageName: node
  linkType: hard

"error-ex@npm:^1.3.1":
  version: 1.3.2
  resolution: "error-ex@npm:1.3.2"
  dependencies:
    is-arrayish: "npm:^0.2.1"
  checksum: 10c0/ba827f89369b4c93382cfca5a264d059dfefdaa56ecc5e338ffa58a6471f5ed93b71a20add1d52290a4873d92381174382658c885ac1a2305f7baca363ce9cce
  languageName: node
  linkType: hard

"es-define-property@npm:^1.0.1":
  version: 1.0.1
  resolution: "es-define-property@npm:1.0.1"
  checksum: 10c0/3f54eb49c16c18707949ff25a1456728c883e81259f045003499efba399c08bad00deebf65cccde8c0e07908c1a225c9d472b7107e558f2a48e28d530e34527c
  languageName: node
  linkType: hard

"es-errors@npm:^1.3.0":
  version: 1.3.0
  resolution: "es-errors@npm:1.3.0"
  checksum: 10c0/0a61325670072f98d8ae3b914edab3559b6caa980f08054a3b872052640d91da01d38df55df797fcc916389d77fc92b8d5906cf028f4db46d7e3003abecbca85
  languageName: node
  linkType: hard

"es-module-lexer@npm:^1.6.0":
  version: 1.7.0
  resolution: "es-module-lexer@npm:1.7.0"
  checksum: 10c0/4c935affcbfeba7fb4533e1da10fa8568043df1e3574b869385980de9e2d475ddc36769891936dbb07036edb3c3786a8b78ccf44964cd130dedc1f2c984b6c7b
  languageName: node
  linkType: hard

"es-object-atoms@npm:^1.0.0, es-object-atoms@npm:^1.1.1":
  version: 1.1.1
  resolution: "es-object-atoms@npm:1.1.1"
  dependencies:
    es-errors: "npm:^1.3.0"
  checksum: 10c0/65364812ca4daf48eb76e2a3b7a89b3f6a2e62a1c420766ce9f692665a29d94fe41fe88b65f24106f449859549711e4b40d9fb8002d862dfd7eb1c512d10be0c
  languageName: node
  linkType: hard

"es-set-tostringtag@npm:^2.1.0":
  version: 2.1.0
  resolution: "es-set-tostringtag@npm:2.1.0"
  dependencies:
    es-errors: "npm:^1.3.0"
    get-intrinsic: "npm:^1.2.6"
    has-tostringtag: "npm:^1.0.2"
    hasown: "npm:^2.0.2"
  checksum: 10c0/ef2ca9ce49afe3931cb32e35da4dcb6d86ab02592cfc2ce3e49ced199d9d0bb5085fc7e73e06312213765f5efa47cc1df553a6a5154584b21448e9fb8355b1af
  languageName: node
  linkType: hard

"esbuild@npm:^0.25.5, esbuild@npm:~0.25.0":
  version: 0.25.5
  resolution: "esbuild@npm:0.25.5"
  dependencies:
    "@esbuild/aix-ppc64": "npm:0.25.5"
    "@esbuild/android-arm": "npm:0.25.5"
    "@esbuild/android-arm64": "npm:0.25.5"
    "@esbuild/android-x64": "npm:0.25.5"
    "@esbuild/darwin-arm64": "npm:0.25.5"
    "@esbuild/darwin-x64": "npm:0.25.5"
    "@esbuild/freebsd-arm64": "npm:0.25.5"
    "@esbuild/freebsd-x64": "npm:0.25.5"
    "@esbuild/linux-arm": "npm:0.25.5"
    "@esbuild/linux-arm64": "npm:0.25.5"
    "@esbuild/linux-ia32": "npm:0.25.5"
    "@esbuild/linux-loong64": "npm:0.25.5"
    "@esbuild/linux-mips64el": "npm:0.25.5"
    "@esbuild/linux-ppc64": "npm:0.25.5"
    "@esbuild/linux-riscv64": "npm:0.25.5"
    "@esbuild/linux-s390x": "npm:0.25.5"
    "@esbuild/linux-x64": "npm:0.25.5"
    "@esbuild/netbsd-arm64": "npm:0.25.5"
    "@esbuild/netbsd-x64": "npm:0.25.5"
    "@esbuild/openbsd-arm64": "npm:0.25.5"
    "@esbuild/openbsd-x64": "npm:0.25.5"
    "@esbuild/sunos-x64": "npm:0.25.5"
    "@esbuild/win32-arm64": "npm:0.25.5"
    "@esbuild/win32-ia32": "npm:0.25.5"
    "@esbuild/win32-x64": "npm:0.25.5"
  dependenciesMeta:
    "@esbuild/aix-ppc64":
      optional: true
    "@esbuild/android-arm":
      optional: true
    "@esbuild/android-arm64":
      optional: true
    "@esbuild/android-x64":
      optional: true
    "@esbuild/darwin-arm64":
      optional: true
    "@esbuild/darwin-x64":
      optional: true
    "@esbuild/freebsd-arm64":
      optional: true
    "@esbuild/freebsd-x64":
      optional: true
    "@esbuild/linux-arm":
      optional: true
    "@esbuild/linux-arm64":
      optional: true
    "@esbuild/linux-ia32":
      optional: true
    "@esbuild/linux-loong64":
      optional: true
    "@esbuild/linux-mips64el":
      optional: true
    "@esbuild/linux-ppc64":
      optional: true
    "@esbuild/linux-riscv64":
      optional: true
    "@esbuild/linux-s390x":
      optional: true
    "@esbuild/linux-x64":
      optional: true
    "@esbuild/netbsd-arm64":
      optional: true
    "@esbuild/netbsd-x64":
      optional: true
    "@esbuild/openbsd-arm64":
      optional: true
    "@esbuild/openbsd-x64":
      optional: true
    "@esbuild/sunos-x64":
      optional: true
    "@esbuild/win32-arm64":
      optional: true
    "@esbuild/win32-ia32":
      optional: true
    "@esbuild/win32-x64":
      optional: true
  bin:
    esbuild: bin/esbuild
  checksum: 10c0/aba8cbc11927fa77562722ed5e95541ce2853f67ad7bdc40382b558abc2e0ec57d92ffb820f082ba2047b4ef9f3bc3da068cdebe30dfd3850cfa3827a78d604e
  languageName: node
  linkType: hard

"escalade@npm:^3.1.1, escalade@npm:^3.2.0":
  version: 3.2.0
  resolution: "escalade@npm:3.2.0"
  checksum: 10c0/ced4dd3a78e15897ed3be74e635110bbf3b08877b0a41be50dcb325ee0e0b5f65fc2d50e9845194d7c4633f327e2e1c6cce00a71b617c5673df0374201d67f65
  languageName: node
  linkType: hard

"escape-html@npm:^1.0.3, escape-html@npm:~1.0.3":
  version: 1.0.3
  resolution: "escape-html@npm:1.0.3"
  checksum: 10c0/524c739d776b36c3d29fa08a22e03e8824e3b2fd57500e5e44ecf3cc4707c34c60f9ca0781c0e33d191f2991161504c295e98f68c78fe7baa6e57081ec6ac0a3
  languageName: node
  linkType: hard

"escape-string-regexp@npm:^2.0.0":
  version: 2.0.0
  resolution: "escape-string-regexp@npm:2.0.0"
  checksum: 10c0/2530479fe8db57eace5e8646c9c2a9c80fa279614986d16dcc6bcaceb63ae77f05a851ba6c43756d816c61d7f4534baf56e3c705e3e0d884818a46808811c507
  languageName: node
  linkType: hard

"escape-string-regexp@npm:^5.0.0":
  version: 5.0.0
  resolution: "escape-string-regexp@npm:5.0.0"
  checksum: 10c0/6366f474c6f37a802800a435232395e04e9885919873e382b157ab7e8f0feb8fed71497f84a6f6a81a49aab41815522f5839112bd38026d203aea0c91622df95
  languageName: node
  linkType: hard

"esprima@npm:^4.0.0":
  version: 4.0.1
  resolution: "esprima@npm:4.0.1"
  bin:
    esparse: ./bin/esparse.js
    esvalidate: ./bin/esvalidate.js
  checksum: 10c0/ad4bab9ead0808cf56501750fd9d3fb276f6b105f987707d059005d57e182d18a7c9ec7f3a01794ebddcca676773e42ca48a32d67a250c9d35e009ca613caba3
  languageName: node
  linkType: hard

"estree-walker@npm:^2.0.2":
  version: 2.0.2
  resolution: "estree-walker@npm:2.0.2"
  checksum: 10c0/53a6c54e2019b8c914dc395890153ffdc2322781acf4bd7d1a32d7aedc1710807bdcd866ac133903d5629ec601fbb50abe8c2e5553c7f5a0afdd9b6af6c945af
  languageName: node
  linkType: hard

"etag@npm:^1.8.1, etag@npm:~1.8.1":
  version: 1.8.1
  resolution: "etag@npm:1.8.1"
  checksum: 10c0/12be11ef62fb9817314d790089a0a49fae4e1b50594135dcb8076312b7d7e470884b5100d249b28c18581b7fd52f8b485689ffae22a11ed9ec17377a33a08f84
  languageName: node
  linkType: hard

"event-target-shim@npm:^5.0.0":
  version: 5.0.1
  resolution: "event-target-shim@npm:5.0.1"
  checksum: 10c0/0255d9f936215fd206156fd4caa9e8d35e62075d720dc7d847e89b417e5e62cf1ce6c9b4e0a1633a9256de0efefaf9f8d26924b1f3c8620cffb9db78e7d3076b
  languageName: node
  linkType: hard

"events@npm:^3.3.0":
  version: 3.3.0
  resolution: "events@npm:3.3.0"
  checksum: 10c0/d6b6f2adbccbcda74ddbab52ed07db727ef52e31a61ed26db9feb7dc62af7fc8e060defa65e5f8af9449b86b52cc1a1f6a79f2eafcf4e62add2b7a1fa4a432f6
  languageName: node
  linkType: hard

"eventsource-parser@npm:^3.0.0, eventsource-parser@npm:^3.0.1":
  version: 3.0.2
  resolution: "eventsource-parser@npm:3.0.2"
  checksum: 10c0/067c6e60b7c68a4577630cc7e11d2aaeef52005e377a213308c7c2350596a175d5a179671d85f570726dce3f451c15d174ece4479ce68a1805686c88950d08dd
  languageName: node
  linkType: hard

"eventsource@npm:^3.0.2, eventsource@npm:^3.0.5":
  version: 3.0.7
  resolution: "eventsource@npm:3.0.7"
  dependencies:
    eventsource-parser: "npm:^3.0.1"
  checksum: 10c0/c48a73c38f300e33e9f11375d4ee969f25cbb0519608a12378a38068055ae8b55b6e0e8a49c3f91c784068434efe1d9f01eb49b6315b04b0da9157879ce2f67d
  languageName: node
  linkType: hard

"evt@npm:^2.4.13":
  version: 2.5.9
  resolution: "evt@npm:2.5.9"
  dependencies:
    minimal-polyfills: "npm:^2.2.3"
    run-exclusive: "npm:^2.2.19"
    tsafe: "npm:^1.8.5"
  checksum: 10c0/d23532129c2a4b930bfd49e931cf1f9eb4023ad6e18caee52229998ae44c954574b97be745e3bf8af00e5854426f5b1a3d6c01410a14c19cdebf62d5c51ef510
  languageName: node
  linkType: hard

"execa@npm:^5.0.0":
  version: 5.1.1
  resolution: "execa@npm:5.1.1"
  dependencies:
    cross-spawn: "npm:^7.0.3"
    get-stream: "npm:^6.0.0"
    human-signals: "npm:^2.1.0"
    is-stream: "npm:^2.0.0"
    merge-stream: "npm:^2.0.0"
    npm-run-path: "npm:^4.0.1"
    onetime: "npm:^5.1.2"
    signal-exit: "npm:^3.0.3"
    strip-final-newline: "npm:^2.0.0"
  checksum: 10c0/c8e615235e8de4c5addf2fa4c3da3e3aa59ce975a3e83533b4f6a71750fb816a2e79610dc5f1799b6e28976c9ae86747a36a606655bf8cb414a74d8d507b304f
  languageName: node
  linkType: hard

"execa@npm:^8.0.1":
  version: 8.0.1
  resolution: "execa@npm:8.0.1"
  dependencies:
    cross-spawn: "npm:^7.0.3"
    get-stream: "npm:^8.0.1"
    human-signals: "npm:^5.0.0"
    is-stream: "npm:^3.0.0"
    merge-stream: "npm:^2.0.0"
    npm-run-path: "npm:^5.1.0"
    onetime: "npm:^6.0.0"
    signal-exit: "npm:^4.1.0"
    strip-final-newline: "npm:^3.0.0"
  checksum: 10c0/2c52d8775f5bf103ce8eec9c7ab3059909ba350a5164744e9947ed14a53f51687c040a250bda833f906d1283aa8803975b84e6c8f7a7c42f99dc8ef80250d1af
  languageName: node
  linkType: hard

"execa@npm:^9.6.0":
  version: 9.6.0
  resolution: "execa@npm:9.6.0"
  dependencies:
    "@sindresorhus/merge-streams": "npm:^4.0.0"
    cross-spawn: "npm:^7.0.6"
    figures: "npm:^6.1.0"
    get-stream: "npm:^9.0.0"
    human-signals: "npm:^8.0.1"
    is-plain-obj: "npm:^4.1.0"
    is-stream: "npm:^4.0.1"
    npm-run-path: "npm:^6.0.0"
    pretty-ms: "npm:^9.2.0"
    signal-exit: "npm:^4.1.0"
    strip-final-newline: "npm:^4.0.0"
    yoctocolors: "npm:^2.1.1"
  checksum: 10c0/2c44a33142f77d3a6a590a3b769b49b27029a76768593bac1f26fed4dd1330e9c189ee61eba6a8c990fb77e37286c68c7445472ebf24c22b31e9ff320e73d7ac
  languageName: node
  linkType: hard

"exit-hook@npm:^4.0.0":
  version: 4.0.0
  resolution: "exit-hook@npm:4.0.0"
  checksum: 10c0/7fb33eaeb9050aee9479da9c93d42b796fb409c40e1d2b6ea2f40786ae7d7db6dc6a0f6ecc7bc24e479f957b7844bcb880044ded73320334743c64e3ecef48d7
  languageName: node
  linkType: hard

"exit@npm:^0.1.2":
  version: 0.1.2
  resolution: "exit@npm:0.1.2"
  checksum: 10c0/71d2ad9b36bc25bb8b104b17e830b40a08989be7f7d100b13269aaae7c3784c3e6e1e88a797e9e87523993a25ba27c8958959a554535370672cfb4d824af8989
  languageName: node
  linkType: hard

"expect@npm:^29.0.0, expect@npm:^29.7.0":
  version: 29.7.0
  resolution: "expect@npm:29.7.0"
  dependencies:
    "@jest/expect-utils": "npm:^29.7.0"
    jest-get-type: "npm:^29.6.3"
    jest-matcher-utils: "npm:^29.7.0"
    jest-message-util: "npm:^29.7.0"
    jest-util: "npm:^29.7.0"
  checksum: 10c0/2eddeace66e68b8d8ee5f7be57f3014b19770caaf6815c7a08d131821da527fb8c8cb7b3dcd7c883d2d3d8d184206a4268984618032d1e4b16dc8d6596475d41
  languageName: node
  linkType: hard

"exponential-backoff@npm:^3.1.1":
  version: 3.1.2
  resolution: "exponential-backoff@npm:3.1.2"
  checksum: 10c0/d9d3e1eafa21b78464297df91f1776f7fbaa3d5e3f7f0995648ca5b89c069d17055033817348d9f4a43d1c20b0eab84f75af6991751e839df53e4dfd6f22e844
  languageName: node
  linkType: hard

"express-rate-limit@npm:^7.5.0":
  version: 7.5.1
  resolution: "express-rate-limit@npm:7.5.1"
  peerDependencies:
    express: ">= 4.11"
  checksum: 10c0/b07de84d700a2c07c4bf2f040e7558ed5a1f660f03ed5f30bf8ff7b51e98ba7a85215640e70fc48cbbb9151066ea51239d9a1b41febc9b84d98c7915b0186161
  languageName: node
  linkType: hard

"express@npm:^4.18.3":
  version: 4.21.2
  resolution: "express@npm:4.21.2"
  dependencies:
    accepts: "npm:~1.3.8"
    array-flatten: "npm:1.1.1"
    body-parser: "npm:1.20.3"
    content-disposition: "npm:0.5.4"
    content-type: "npm:~1.0.4"
    cookie: "npm:0.7.1"
    cookie-signature: "npm:1.0.6"
    debug: "npm:2.6.9"
    depd: "npm:2.0.0"
    encodeurl: "npm:~2.0.0"
    escape-html: "npm:~1.0.3"
    etag: "npm:~1.8.1"
    finalhandler: "npm:1.3.1"
    fresh: "npm:0.5.2"
    http-errors: "npm:2.0.0"
    merge-descriptors: "npm:1.0.3"
    methods: "npm:~1.1.2"
    on-finished: "npm:2.4.1"
    parseurl: "npm:~1.3.3"
    path-to-regexp: "npm:0.1.12"
    proxy-addr: "npm:~2.0.7"
    qs: "npm:6.13.0"
    range-parser: "npm:~1.2.1"
    safe-buffer: "npm:5.2.1"
    send: "npm:0.19.0"
    serve-static: "npm:1.16.2"
    setprototypeof: "npm:1.2.0"
    statuses: "npm:2.0.1"
    type-is: "npm:~1.6.18"
    utils-merge: "npm:1.0.1"
    vary: "npm:~1.1.2"
  checksum: 10c0/38168fd0a32756600b56e6214afecf4fc79ec28eca7f7a91c2ab8d50df4f47562ca3f9dee412da7f5cea6b1a1544b33b40f9f8586dbacfbdada0fe90dbb10a1f
  languageName: node
  linkType: hard

"express@npm:^4.21.1 || ^5.0.0, express@npm:^5.0.1":
  version: 5.1.0
  resolution: "express@npm:5.1.0"
  dependencies:
    accepts: "npm:^2.0.0"
    body-parser: "npm:^2.2.0"
    content-disposition: "npm:^1.0.0"
    content-type: "npm:^1.0.5"
    cookie: "npm:^0.7.1"
    cookie-signature: "npm:^1.2.1"
    debug: "npm:^4.4.0"
    encodeurl: "npm:^2.0.0"
    escape-html: "npm:^1.0.3"
    etag: "npm:^1.8.1"
    finalhandler: "npm:^2.1.0"
    fresh: "npm:^2.0.0"
    http-errors: "npm:^2.0.0"
    merge-descriptors: "npm:^2.0.0"
    mime-types: "npm:^3.0.0"
    on-finished: "npm:^2.4.1"
    once: "npm:^1.4.0"
    parseurl: "npm:^1.3.3"
    proxy-addr: "npm:^2.0.7"
    qs: "npm:^6.14.0"
    range-parser: "npm:^1.2.1"
    router: "npm:^2.2.0"
    send: "npm:^1.1.0"
    serve-static: "npm:^2.2.0"
    statuses: "npm:^2.0.1"
    type-is: "npm:^2.0.1"
    vary: "npm:^1.1.2"
  checksum: 10c0/80ce7c53c5f56887d759b94c3f2283e2e51066c98d4b72a4cc1338e832b77f1e54f30d0239cc10815a0f849bdb753e6a284d2fa48d4ab56faf9c501f55d751d6
  languageName: node
  linkType: hard

"extend@npm:^3.0.2":
  version: 3.0.2
  resolution: "extend@npm:3.0.2"
  checksum: 10c0/73bf6e27406e80aa3e85b0d1c4fd987261e628064e170ca781125c0b635a3dabad5e05adbf07595ea0cf1e6c5396cacb214af933da7cbaf24fe75ff14818e8f9
  languageName: node
  linkType: hard

"eyes@npm:0.1.x":
  version: 0.1.8
  resolution: "eyes@npm:0.1.8"
  checksum: 10c0/4c79a9cbf45746d8c9f48cc957e35ad8ea336add1c7b8d5a0e002efc791a7a62b27b2188184ef1a1eea7bc3cd06b161791421e0e6c5fe78309705a162c53eea8
  languageName: node
  linkType: hard

"fast-copy@npm:^3.0.2":
  version: 3.0.2
  resolution: "fast-copy@npm:3.0.2"
  checksum: 10c0/02e8b9fd03c8c024d2987760ce126456a0e17470850b51e11a1c3254eed6832e4733ded2d93316c82bc0b36aeb991ad1ff48d1ba95effe7add7c3ab8d8eb554a
  languageName: node
  linkType: hard

"fast-deep-equal@npm:^3.1.1, fast-deep-equal@npm:^3.1.3":
  version: 3.1.3
  resolution: "fast-deep-equal@npm:3.1.3"
  checksum: 10c0/40dedc862eb8992c54579c66d914635afbec43350afbbe991235fdcb4e3a8d5af1b23ae7e79bef7d4882d0ecee06c3197488026998fb19f72dc95acff1d1b1d0
  languageName: node
  linkType: hard

"fast-glob@npm:^3.3.2, fast-glob@npm:^3.3.3":
  version: 3.3.3
  resolution: "fast-glob@npm:3.3.3"
  dependencies:
    "@nodelib/fs.stat": "npm:^2.0.2"
    "@nodelib/fs.walk": "npm:^1.2.3"
    glob-parent: "npm:^5.1.2"
    merge2: "npm:^1.3.0"
    micromatch: "npm:^4.0.8"
  checksum: 10c0/f6aaa141d0d3384cf73cbcdfc52f475ed293f6d5b65bfc5def368b09163a9f7e5ec2b3014d80f733c405f58e470ee0cc451c2937685045cddcdeaa24199c43fe
  languageName: node
  linkType: hard

"fast-json-patch@npm:^3.1.1":
  version: 3.1.1
  resolution: "fast-json-patch@npm:3.1.1"
  checksum: 10c0/8a0438b4818bb53153275fe5b38033610e8c9d9eb11869e6a7dc05eb92fa70f3caa57015e344eb3ae1e71c7a75ad4cc6bc2dc9e0ff281d6ed8ecd44505210ca8
  languageName: node
  linkType: hard

"fast-json-stable-stringify@npm:2.x, fast-json-stable-stringify@npm:^2.0.0, fast-json-stable-stringify@npm:^2.1.0":
  version: 2.1.0
  resolution: "fast-json-stable-stringify@npm:2.1.0"
  checksum: 10c0/7f081eb0b8a64e0057b3bb03f974b3ef00135fbf36c1c710895cd9300f13c94ba809bb3a81cf4e1b03f6e5285610a61abbd7602d0652de423144dfee5a389c9b
  languageName: node
  linkType: hard

"fast-redact@npm:^3.1.1":
  version: 3.5.0
  resolution: "fast-redact@npm:3.5.0"
  checksum: 10c0/7e2ce4aad6e7535e0775bf12bd3e4f2e53d8051d8b630e0fa9e67f68cb0b0e6070d2f7a94b1d0522ef07e32f7c7cda5755e2b677a6538f1e9070ca053c42343a
  languageName: node
  linkType: hard

"fast-safe-stringify@npm:^2.1.1":
  version: 2.1.1
  resolution: "fast-safe-stringify@npm:2.1.1"
  checksum: 10c0/d90ec1c963394919828872f21edaa3ad6f1dddd288d2bd4e977027afff09f5db40f94e39536d4646f7e01761d704d72d51dce5af1b93717f3489ef808f5f4e4d
  languageName: node
  linkType: hard

"fast-xml-parser@npm:4.4.1":
  version: 4.4.1
  resolution: "fast-xml-parser@npm:4.4.1"
  dependencies:
    strnum: "npm:^1.0.5"
  bin:
    fxparser: src/cli/cli.js
  checksum: 10c0/7f334841fe41bfb0bf5d920904ccad09cefc4b5e61eaf4c225bf1e1bb69ee77ef2147d8942f783ee8249e154d1ca8a858e10bda78a5d78b8bed3f48dcee9bf33
  languageName: node
  linkType: hard

"fastq@npm:^1.6.0":
  version: 1.19.1
  resolution: "fastq@npm:1.19.1"
  dependencies:
    reusify: "npm:^1.0.4"
  checksum: 10c0/ebc6e50ac7048daaeb8e64522a1ea7a26e92b3cee5cd1c7f2316cdca81ba543aa40a136b53891446ea5c3a67ec215fbaca87ad405f102dd97012f62916905630
  languageName: node
  linkType: hard

"fb-watchman@npm:^2.0.0":
  version: 2.0.2
  resolution: "fb-watchman@npm:2.0.2"
  dependencies:
    bser: "npm:2.1.1"
  checksum: 10c0/feae89ac148adb8f6ae8ccd87632e62b13563e6fb114cacb5265c51f585b17e2e268084519fb2edd133872f1d47a18e6bfd7e5e08625c0d41b93149694187581
  languageName: node
  linkType: hard

"fdir@npm:^6.2.0, fdir@npm:^6.4.4":
  version: 6.4.6
  resolution: "fdir@npm:6.4.6"
  peerDependencies:
    picomatch: ^3 || ^4
  peerDependenciesMeta:
    picomatch:
      optional: true
  checksum: 10c0/45b559cff889934ebb8bc498351e5acba40750ada7e7d6bde197768d2fa67c149be8ae7f8ff34d03f4e1eb20f2764116e56440aaa2f6689e9a4aa7ef06acafe9
  languageName: node
  linkType: hard

"figures@npm:^6.1.0":
  version: 6.1.0
  resolution: "figures@npm:6.1.0"
  dependencies:
    is-unicode-supported: "npm:^2.0.0"
  checksum: 10c0/9159df4264d62ef447a3931537de92f5012210cf5135c35c010df50a2169377581378149abfe1eb238bd6acbba1c0d547b1f18e0af6eee49e30363cedaffcfe4
  languageName: node
  linkType: hard

"filelist@npm:^1.0.4":
  version: 1.0.4
  resolution: "filelist@npm:1.0.4"
  dependencies:
    minimatch: "npm:^5.0.1"
  checksum: 10c0/426b1de3944a3d153b053f1c0ebfd02dccd0308a4f9e832ad220707a6d1f1b3c9784d6cadf6b2f68f09a57565f63ebc7bcdc913ccf8012d834f472c46e596f41
  languageName: node
  linkType: hard

"fill-range@npm:^7.1.1":
  version: 7.1.1
  resolution: "fill-range@npm:7.1.1"
  dependencies:
    to-regex-range: "npm:^5.0.1"
  checksum: 10c0/b75b691bbe065472f38824f694c2f7449d7f5004aa950426a2c28f0306c60db9b880c0b0e4ed819997ffb882d1da02cfcfc819bddc94d71627f5269682edf018
  languageName: node
  linkType: hard

"finalhandler@npm:1.3.1":
  version: 1.3.1
  resolution: "finalhandler@npm:1.3.1"
  dependencies:
    debug: "npm:2.6.9"
    encodeurl: "npm:~2.0.0"
    escape-html: "npm:~1.0.3"
    on-finished: "npm:2.4.1"
    parseurl: "npm:~1.3.3"
    statuses: "npm:2.0.1"
    unpipe: "npm:~1.0.0"
  checksum: 10c0/d38035831865a49b5610206a3a9a9aae4e8523cbbcd01175d0480ffbf1278c47f11d89be3ca7f617ae6d94f29cf797546a4619cd84dd109009ef33f12f69019f
  languageName: node
  linkType: hard

"finalhandler@npm:^2.1.0":
  version: 2.1.0
  resolution: "finalhandler@npm:2.1.0"
  dependencies:
    debug: "npm:^4.4.0"
    encodeurl: "npm:^2.0.0"
    escape-html: "npm:^1.0.3"
    on-finished: "npm:^2.4.1"
    parseurl: "npm:^1.3.3"
    statuses: "npm:^2.0.1"
  checksum: 10c0/da0bbca6d03873472ee890564eb2183f4ed377f25f3628a0fc9d16dac40bed7b150a0d82ebb77356e4c6d97d2796ad2dba22948b951dddee2c8768b0d1b9fb1f
  languageName: node
  linkType: hard

"find-up@npm:^4.0.0, find-up@npm:^4.1.0":
  version: 4.1.0
  resolution: "find-up@npm:4.1.0"
  dependencies:
    locate-path: "npm:^5.0.0"
    path-exists: "npm:^4.0.0"
  checksum: 10c0/0406ee89ebeefa2d507feb07ec366bebd8a6167ae74aa4e34fb4c4abd06cf782a3ce26ae4194d70706f72182841733f00551c209fe575cb00bd92104056e78c1
  languageName: node
  linkType: hard

"find-workspaces@npm:^0.3.1":
  version: 0.3.1
  resolution: "find-workspaces@npm:0.3.1"
  dependencies:
    fast-glob: "npm:^3.3.2"
    pkg-types: "npm:^1.0.3"
    yaml: "npm:^2.3.4"
  checksum: 10c0/e28dd30a753d9cabc1c9e94746ef75be181ce6403d9987c9efac7379ab94c8f776f541acd342907f3077cf288b6464dc807b30b57b2ded96a3e1caca0da3b10a
  languageName: node
  linkType: hard

"follow-redirects@npm:^1.15.6":
  version: 1.15.9
  resolution: "follow-redirects@npm:1.15.9"
  peerDependenciesMeta:
    debug:
      optional: true
  checksum: 10c0/5829165bd112c3c0e82be6c15b1a58fa9dcfaede3b3c54697a82fe4a62dd5ae5e8222956b448d2f98e331525f05d00404aba7d696de9e761ef6e42fdc780244f
  languageName: node
  linkType: hard

"foreground-child@npm:^3.1.0":
  version: 3.3.1
  resolution: "foreground-child@npm:3.3.1"
  dependencies:
    cross-spawn: "npm:^7.0.6"
    signal-exit: "npm:^4.0.1"
  checksum: 10c0/8986e4af2430896e65bc2788d6679067294d6aee9545daefc84923a0a4b399ad9c7a3ea7bd8c0b2b80fdf4a92de4c69df3f628233ff3224260e9c1541a9e9ed3
  languageName: node
  linkType: hard

"form-data-encoder@npm:^4.0.2":
  version: 4.1.0
  resolution: "form-data-encoder@npm:4.1.0"
  checksum: 10c0/cbd655aa8ffff6f7c2733b1d8e95fa9a2fe8a88a90bde29fb54b8e02c9406e51f32a014bfe8297d67fbac9f77614d14a8b4bbc4fd0352838e67e97a881d06332
  languageName: node
  linkType: hard

"form-data@npm:^4.0.0":
  version: 4.0.3
  resolution: "form-data@npm:4.0.3"
  dependencies:
    asynckit: "npm:^0.4.0"
    combined-stream: "npm:^1.0.8"
    es-set-tostringtag: "npm:^2.1.0"
    hasown: "npm:^2.0.2"
    mime-types: "npm:^2.1.12"
  checksum: 10c0/f0cf45873d600110b5fadf5804478377694f73a1ed97aaa370a74c90cebd7fe6e845a081171668a5476477d0d55a73a4e03d6682968fa8661eac2a81d651fcdb
  languageName: node
  linkType: hard

"formdata-node@npm:^6.0.3":
  version: 6.0.3
  resolution: "formdata-node@npm:6.0.3"
  checksum: 10c0/9b8ada280c7b0c7314bed57fd50b3562f8825bd3ede6f6231b1bc7683b649e7f3ffb7b0f13d8e9e6cae8042ea21eaf497a7c676d2fe6dc63daefefaea4838240
  languageName: node
  linkType: hard

"formidable@npm:^3.5.4":
  version: 3.5.4
  resolution: "formidable@npm:3.5.4"
  dependencies:
    "@paralleldrive/cuid2": "npm:^2.2.2"
    dezalgo: "npm:^1.0.4"
    once: "npm:^1.4.0"
  checksum: 10c0/3a311ce57617eb8f532368e91c0f2bbfb299a0f1a35090e085bd6ca772298f196fbb0b66f0d4b5549d7bf3c5e1844439338d4402b7b6d1fedbe206ad44a931f8
  languageName: node
  linkType: hard

"forwarded-parse@npm:2.1.2":
  version: 2.1.2
  resolution: "forwarded-parse@npm:2.1.2"
  checksum: 10c0/0c6b4c631775f272b4475e935108635495e8a5b261d1b4a5caef31c47c5a0b04134adc564e655aadfef366a02647fa3ae90a1d3ac19929f3ade47f9bed53036a
  languageName: node
  linkType: hard

"forwarded@npm:0.2.0":
  version: 0.2.0
  resolution: "forwarded@npm:0.2.0"
  checksum: 10c0/9b67c3fac86acdbc9ae47ba1ddd5f2f81526fa4c8226863ede5600a3f7c7416ef451f6f1e240a3cc32d0fd79fcfe6beb08fd0da454f360032bde70bf80afbb33
  languageName: node
  linkType: hard

"fresh@npm:0.5.2":
  version: 0.5.2
  resolution: "fresh@npm:0.5.2"
  checksum: 10c0/c6d27f3ed86cc5b601404822f31c900dd165ba63fff8152a3ef714e2012e7535027063bc67ded4cb5b3a49fa596495d46cacd9f47d6328459cf570f08b7d9e5a
  languageName: node
  linkType: hard

"fresh@npm:^2.0.0":
  version: 2.0.0
  resolution: "fresh@npm:2.0.0"
  checksum: 10c0/0557548194cb9a809a435bf92bcfbc20c89e8b5eb38861b73ced36750437251e39a111fc3a18b98531be9dd91fe1411e4969f229dc579ec0251ce6c5d4900bbc
  languageName: node
  linkType: hard

"fs-extra@npm:^11.3.0":
  version: 11.3.0
  resolution: "fs-extra@npm:11.3.0"
  dependencies:
    graceful-fs: "npm:^4.2.0"
    jsonfile: "npm:^6.0.1"
    universalify: "npm:^2.0.0"
  checksum: 10c0/5f95e996186ff45463059feb115a22fb048bdaf7e487ecee8a8646c78ed8fdca63630e3077d4c16ce677051f5e60d3355a06f3cd61f3ca43f48cc58822a44d0a
  languageName: node
  linkType: hard

"fs-minipass@npm:^3.0.0":
  version: 3.0.3
  resolution: "fs-minipass@npm:3.0.3"
  dependencies:
    minipass: "npm:^7.0.3"
  checksum: 10c0/63e80da2ff9b621e2cb1596abcb9207f1cf82b968b116ccd7b959e3323144cce7fb141462200971c38bbf2ecca51695069db45265705bed09a7cd93ae5b89f94
  languageName: node
  linkType: hard

"fs.realpath@npm:^1.0.0":
  version: 1.0.0
  resolution: "fs.realpath@npm:1.0.0"
  checksum: 10c0/444cf1291d997165dfd4c0d58b69f0e4782bfd9149fd72faa4fe299e68e0e93d6db941660b37dd29153bf7186672ececa3b50b7e7249477b03fdf850f287c948
  languageName: node
  linkType: hard

"fsevents@npm:^2.3.2, fsevents@npm:~2.3.2, fsevents@npm:~2.3.3":
  version: 2.3.3
  resolution: "fsevents@npm:2.3.3"
  dependencies:
    node-gyp: "npm:latest"
  checksum: 10c0/a1f0c44595123ed717febbc478aa952e47adfc28e2092be66b8ab1635147254ca6cfe1df792a8997f22716d4cbafc73309899ff7bfac2ac3ad8cf2e4ecc3ec60
  conditions: os=darwin
  languageName: node
  linkType: hard

"fsevents@patch:fsevents@npm%3A^2.3.2#optional!builtin<compat/fsevents>, fsevents@patch:fsevents@npm%3A~2.3.2#optional!builtin<compat/fsevents>, fsevents@patch:fsevents@npm%3A~2.3.3#optional!builtin<compat/fsevents>":
  version: 2.3.3
  resolution: "fsevents@patch:fsevents@npm%3A2.3.3#optional!builtin<compat/fsevents>::version=2.3.3&hash=df0bf1"
  dependencies:
    node-gyp: "npm:latest"
  conditions: os=darwin
  languageName: node
  linkType: hard

"function-bind@npm:^1.1.2":
  version: 1.1.2
  resolution: "function-bind@npm:1.1.2"
  checksum: 10c0/d8680ee1e5fcd4c197e4ac33b2b4dce03c71f4d91717292785703db200f5c21f977c568d28061226f9b5900cbcd2c84463646134fd5337e7925e0942bc3f46d5
  languageName: node
  linkType: hard

"gaxios@npm:^6.1.1":
  version: 6.7.1
  resolution: "gaxios@npm:6.7.1"
  dependencies:
    extend: "npm:^3.0.2"
    https-proxy-agent: "npm:^7.0.1"
    is-stream: "npm:^2.0.0"
    node-fetch: "npm:^2.6.9"
    uuid: "npm:^9.0.1"
  checksum: 10c0/53e92088470661c5bc493a1de29d05aff58b1f0009ec5e7903f730f892c3642a93e264e61904383741ccbab1ce6e519f12a985bba91e13527678b32ee6d7d3fd
  languageName: node
  linkType: hard

"gcp-metadata@npm:^6.0.0":
  version: 6.1.1
  resolution: "gcp-metadata@npm:6.1.1"
  dependencies:
    gaxios: "npm:^6.1.1"
    google-logging-utils: "npm:^0.0.2"
    json-bigint: "npm:^1.0.0"
  checksum: 10c0/71f6ad4800aa622c246ceec3955014c0c78cdcfe025971f9558b9379f4019f5e65772763428ee8c3244fa81b8631977316eaa71a823493f82e5c44d7259ffac8
  languageName: node
  linkType: hard

"generic-pool@npm:3.9.0":
  version: 3.9.0
  resolution: "generic-pool@npm:3.9.0"
  checksum: 10c0/6b314d0d71170d5cbaf7162c423f53f8d6556b2135626a65bcdc03c089840b0a2f59eeb2d907939b8200e945eaf71ceb6630426f22d2128a1d242aec4b232aa7
  languageName: node
  linkType: hard

"gensync@npm:^1.0.0-beta.2":
  version: 1.0.0-beta.2
  resolution: "gensync@npm:1.0.0-beta.2"
  checksum: 10c0/782aba6cba65b1bb5af3b095d96249d20edbe8df32dbf4696fd49be2583faf676173bf4809386588828e4dd76a3354fcbeb577bab1c833ccd9fc4577f26103f8
  languageName: node
  linkType: hard

"get-caller-file@npm:^2.0.5":
  version: 2.0.5
  resolution: "get-caller-file@npm:2.0.5"
  checksum: 10c0/c6c7b60271931fa752aeb92f2b47e355eac1af3a2673f47c9589e8f8a41adc74d45551c1bc57b5e66a80609f10ffb72b6f575e4370d61cc3f7f3aaff01757cde
  languageName: node
  linkType: hard

"get-intrinsic@npm:^1.2.5, get-intrinsic@npm:^1.2.6, get-intrinsic@npm:^1.3.0":
  version: 1.3.0
  resolution: "get-intrinsic@npm:1.3.0"
  dependencies:
    call-bind-apply-helpers: "npm:^1.0.2"
    es-define-property: "npm:^1.0.1"
    es-errors: "npm:^1.3.0"
    es-object-atoms: "npm:^1.1.1"
    function-bind: "npm:^1.1.2"
    get-proto: "npm:^1.0.1"
    gopd: "npm:^1.2.0"
    has-symbols: "npm:^1.1.0"
    hasown: "npm:^2.0.2"
    math-intrinsics: "npm:^1.1.0"
  checksum: 10c0/52c81808af9a8130f581e6a6a83e1ba4a9f703359e7a438d1369a5267a25412322f03dcbd7c549edaef0b6214a0630a28511d7df0130c93cfd380f4fa0b5b66a
  languageName: node
  linkType: hard

"get-package-type@npm:^0.1.0":
  version: 0.1.0
  resolution: "get-package-type@npm:0.1.0"
  checksum: 10c0/e34cdf447fdf1902a1f6d5af737eaadf606d2ee3518287abde8910e04159368c268568174b2e71102b87b26c2020486f126bfca9c4fb1ceb986ff99b52ecd1be
  languageName: node
  linkType: hard

"get-port@npm:^7.1.0":
  version: 7.1.0
  resolution: "get-port@npm:7.1.0"
  checksum: 10c0/896051fea0fd3df58c050566754ab91f46406e898ce0c708414739d908a5ac03ffef3eca7a494ea9cc1914439e8caccd2218010d1eeabdde914b9ff920fa28fc
  languageName: node
  linkType: hard

"get-proto@npm:^1.0.1":
  version: 1.0.1
  resolution: "get-proto@npm:1.0.1"
  dependencies:
    dunder-proto: "npm:^1.0.1"
    es-object-atoms: "npm:^1.0.0"
  checksum: 10c0/9224acb44603c5526955e83510b9da41baf6ae73f7398875fba50edc5e944223a89c4a72b070fcd78beb5f7bdda58ecb6294adc28f7acfc0da05f76a2399643c
  languageName: node
  linkType: hard

"get-stream@npm:^6.0.0":
  version: 6.0.1
  resolution: "get-stream@npm:6.0.1"
  checksum: 10c0/49825d57d3fd6964228e6200a58169464b8e8970489b3acdc24906c782fb7f01f9f56f8e6653c4a50713771d6658f7cfe051e5eb8c12e334138c9c918b296341
  languageName: node
  linkType: hard

"get-stream@npm:^8.0.1":
  version: 8.0.1
  resolution: "get-stream@npm:8.0.1"
  checksum: 10c0/5c2181e98202b9dae0bb4a849979291043e5892eb40312b47f0c22b9414fc9b28a3b6063d2375705eb24abc41ecf97894d9a51f64ff021511b504477b27b4290
  languageName: node
  linkType: hard

"get-stream@npm:^9.0.0":
  version: 9.0.1
  resolution: "get-stream@npm:9.0.1"
  dependencies:
    "@sec-ant/readable-stream": "npm:^0.4.1"
    is-stream: "npm:^4.0.1"
  checksum: 10c0/d70e73857f2eea1826ac570c3a912757dcfbe8a718a033fa0c23e12ac8e7d633195b01710e0559af574cbb5af101009b42df7b6f6b29ceec8dbdf7291931b948
  languageName: node
  linkType: hard

"get-tsconfig@npm:^4.10.0, get-tsconfig@npm:^4.7.5":
  version: 4.10.1
  resolution: "get-tsconfig@npm:4.10.1"
  dependencies:
    resolve-pkg-maps: "npm:^1.0.0"
  checksum: 10c0/7f8e3dabc6a49b747920a800fb88e1952fef871cdf51b79e98db48275a5de6cdaf499c55ee67df5fa6fe7ce65f0063e26de0f2e53049b408c585aa74d39ffa21
  languageName: node
  linkType: hard

"glob-parent@npm:^5.1.2, glob-parent@npm:~5.1.2":
  version: 5.1.2
  resolution: "glob-parent@npm:5.1.2"
  dependencies:
    is-glob: "npm:^4.0.1"
  checksum: 10c0/cab87638e2112bee3f839ef5f6e0765057163d39c66be8ec1602f3823da4692297ad4e972de876ea17c44d652978638d2fd583c6713d0eb6591706825020c9ee
  languageName: node
  linkType: hard

"glob@npm:^10.2.2, glob@npm:^10.3.7":
  version: 10.4.5
  resolution: "glob@npm:10.4.5"
  dependencies:
    foreground-child: "npm:^3.1.0"
    jackspeak: "npm:^3.1.2"
    minimatch: "npm:^9.0.4"
    minipass: "npm:^7.1.2"
    package-json-from-dist: "npm:^1.0.0"
    path-scurry: "npm:^1.11.1"
  bin:
    glob: dist/esm/bin.mjs
  checksum: 10c0/19a9759ea77b8e3ca0a43c2f07ecddc2ad46216b786bb8f993c445aee80d345925a21e5280c7b7c6c59e860a0154b84e4b2b60321fea92cd3c56b4a7489f160e
  languageName: node
  linkType: hard

"glob@npm:^7.1.3, glob@npm:^7.1.4":
  version: 7.2.3
  resolution: "glob@npm:7.2.3"
  dependencies:
    fs.realpath: "npm:^1.0.0"
    inflight: "npm:^1.0.4"
    inherits: "npm:2"
    minimatch: "npm:^3.1.1"
    once: "npm:^1.3.0"
    path-is-absolute: "npm:^1.0.0"
  checksum: 10c0/65676153e2b0c9095100fe7f25a778bf45608eeb32c6048cf307f579649bcc30353277b3b898a3792602c65764e5baa4f643714dfbdfd64ea271d210c7a425fe
  languageName: node
  linkType: hard

"globals@npm:^11.1.0":
  version: 11.12.0
  resolution: "globals@npm:11.12.0"
  checksum: 10c0/758f9f258e7b19226bd8d4af5d3b0dcf7038780fb23d82e6f98932c44e239f884847f1766e8fa9cc5635ccb3204f7fa7314d4408dd4002a5e8ea827b4018f0a1
  languageName: node
  linkType: hard

"globby@npm:^14.1.0":
  version: 14.1.0
  resolution: "globby@npm:14.1.0"
  dependencies:
    "@sindresorhus/merge-streams": "npm:^2.1.0"
    fast-glob: "npm:^3.3.3"
    ignore: "npm:^7.0.3"
    path-type: "npm:^6.0.0"
    slash: "npm:^5.1.0"
    unicorn-magic: "npm:^0.3.0"
  checksum: 10c0/527a1063c5958255969620c6fa4444a2b2e9278caddd571d46dfbfa307cb15977afb746e84d682ba5b6c94fc081e8997f80ff05dd235441ba1cb16f86153e58e
  languageName: node
  linkType: hard

"google-logging-utils@npm:^0.0.2":
  version: 0.0.2
  resolution: "google-logging-utils@npm:0.0.2"
  checksum: 10c0/9a4bbd470dd101c77405e450fffca8592d1d7114f245a121288d04a957aca08c9dea2dd1a871effe71e41540d1bb0494731a0b0f6fea4358e77f06645e4268c1
  languageName: node
  linkType: hard

"gopd@npm:^1.2.0":
  version: 1.2.0
  resolution: "gopd@npm:1.2.0"
  checksum: 10c0/50fff1e04ba2b7737c097358534eacadad1e68d24cccee3272e04e007bed008e68d2614f3987788428fd192a5ae3889d08fb2331417e4fc4a9ab366b2043cead
  languageName: node
  linkType: hard

"graceful-fs@npm:^4.1.6, graceful-fs@npm:^4.2.0, graceful-fs@npm:^4.2.6, graceful-fs@npm:^4.2.9":
  version: 4.2.11
  resolution: "graceful-fs@npm:4.2.11"
  checksum: 10c0/386d011a553e02bc594ac2ca0bd6d9e4c22d7fa8cfbfc448a6d148c59ea881b092db9dbe3547ae4b88e55f1b01f7c4a2ecc53b310c042793e63aa44cf6c257f2
  languageName: node
  linkType: hard

"graphql-request@npm:^6.1.0":
  version: 6.1.0
  resolution: "graphql-request@npm:6.1.0"
  dependencies:
    "@graphql-typed-document-node/core": "npm:^3.2.0"
    cross-fetch: "npm:^3.1.5"
  peerDependencies:
    graphql: 14 - 16
  checksum: 10c0/f8167925a110e8e1de93d56c14245e7e64391dc8dce5002dd01bf24a3059f345d4ca1bb6ce2040e2ec78264211b0704e75da3e63984f0f74d2042f697a4e8cc6
  languageName: node
  linkType: hard

"graphql@npm:^16.10.0":
  version: 16.11.0
  resolution: "graphql@npm:16.11.0"
  checksum: 10c0/124da7860a2292e9acf2fed0c71fc0f6a9b9ca865d390d112bdd563c1f474357141501c12891f4164fe984315764736ad67f705219c62f7580681d431a85db88
  languageName: node
  linkType: hard

"has-flag@npm:^3.0.0":
  version: 3.0.0
  resolution: "has-flag@npm:3.0.0"
  checksum: 10c0/1c6c83b14b8b1b3c25b0727b8ba3e3b647f99e9e6e13eb7322107261de07a4c1be56fc0d45678fc376e09772a3a1642ccdaf8fc69bdf123b6c086598397ce473
  languageName: node
  linkType: hard

"has-flag@npm:^4.0.0":
  version: 4.0.0
  resolution: "has-flag@npm:4.0.0"
  checksum: 10c0/2e789c61b7888d66993e14e8331449e525ef42aac53c627cc53d1c3334e768bcb6abdc4f5f0de1478a25beec6f0bd62c7549058b7ac53e924040d4f301f02fd1
  languageName: node
  linkType: hard

"has-symbols@npm:^1.0.3, has-symbols@npm:^1.1.0":
  version: 1.1.0
  resolution: "has-symbols@npm:1.1.0"
  checksum: 10c0/dde0a734b17ae51e84b10986e651c664379018d10b91b6b0e9b293eddb32f0f069688c841fb40f19e9611546130153e0a2a48fd7f512891fb000ddfa36f5a20e
  languageName: node
  linkType: hard

"has-tostringtag@npm:^1.0.2":
  version: 1.0.2
  resolution: "has-tostringtag@npm:1.0.2"
  dependencies:
    has-symbols: "npm:^1.0.3"
  checksum: 10c0/a8b166462192bafe3d9b6e420a1d581d93dd867adb61be223a17a8d6dad147aa77a8be32c961bb2f27b3ef893cae8d36f564ab651f5e9b7938ae86f74027c48c
  languageName: node
  linkType: hard

"hasown@npm:^2.0.2":
  version: 2.0.2
  resolution: "hasown@npm:2.0.2"
  dependencies:
    function-bind: "npm:^1.1.2"
  checksum: 10c0/3769d434703b8ac66b209a4cca0737519925bbdb61dd887f93a16372b14694c63ff4e797686d87c90f08168e81082248b9b028bad60d4da9e0d1148766f56eb9
  languageName: node
  linkType: hard

"hast-util-to-html@npm:^9.0.4":
  version: 9.0.5
  resolution: "hast-util-to-html@npm:9.0.5"
  dependencies:
    "@types/hast": "npm:^3.0.0"
    "@types/unist": "npm:^3.0.0"
    ccount: "npm:^2.0.0"
    comma-separated-tokens: "npm:^2.0.0"
    hast-util-whitespace: "npm:^3.0.0"
    html-void-elements: "npm:^3.0.0"
    mdast-util-to-hast: "npm:^13.0.0"
    property-information: "npm:^7.0.0"
    space-separated-tokens: "npm:^2.0.0"
    stringify-entities: "npm:^4.0.0"
    zwitch: "npm:^2.0.4"
  checksum: 10c0/b7a08c30bab4371fc9b4a620965c40b270e5ae7a8e94cf885f43b21705179e28c8e43b39c72885d1647965fb3738654e6962eb8b58b0c2a84271655b4d748836
  languageName: node
  linkType: hard

"hast-util-whitespace@npm:^3.0.0":
  version: 3.0.0
  resolution: "hast-util-whitespace@npm:3.0.0"
  dependencies:
    "@types/hast": "npm:^3.0.0"
  checksum: 10c0/b898bc9fe27884b272580d15260b6bbdabe239973a147e97fa98c45fa0ffec967a481aaa42291ec34fb56530dc2d484d473d7e2bae79f39c83f3762307edfea8
  languageName: node
  linkType: hard

"help-me@npm:^5.0.0":
  version: 5.0.0
  resolution: "help-me@npm:5.0.0"
  checksum: 10c0/054c0e2e9ae2231c85ab5e04f75109b9d068ffcc54e58fb22079822a5ace8ff3d02c66fd45379c902ad5ab825e5d2e1451fcc2f7eab1eb49e7d488133ba4cacb
  languageName: node
  linkType: hard

"hono-openapi@npm:^0.4.8":
  version: 0.4.8
  resolution: "hono-openapi@npm:0.4.8"
  dependencies:
    json-schema-walker: "npm:^2.0.0"
  peerDependencies:
    "@hono/arktype-validator": ^2.0.0
    "@hono/effect-validator": ^1.2.0
    "@hono/typebox-validator": ^0.2.0 || ^0.3.0
    "@hono/valibot-validator": ^0.5.1
    "@hono/zod-validator": ^0.4.1
    "@sinclair/typebox": ^0.34.9
    "@valibot/to-json-schema": ^1.0.0-beta.3
    arktype: ^2.0.0
    effect: ^3.11.3
    hono: ^4.6.13
    openapi-types: ^12.1.3
    valibot: ^1.0.0-beta.9
    zod: ^3.23.8
    zod-openapi: ^4.0.0
  peerDependenciesMeta:
    "@hono/arktype-validator":
      optional: true
    "@hono/effect-validator":
      optional: true
    "@hono/typebox-validator":
      optional: true
    "@hono/valibot-validator":
      optional: true
    "@hono/zod-validator":
      optional: true
    "@sinclair/typebox":
      optional: true
    "@valibot/to-json-schema":
      optional: true
    arktype:
      optional: true
    effect:
      optional: true
    hono:
      optional: true
    valibot:
      optional: true
    zod:
      optional: true
    zod-openapi:
      optional: true
  checksum: 10c0/e1304dc2b6e016a59ae163d7c8f372c2fa0179f4a5614d5b6499f284deb4325ab0f7d562163b28fb8174de03ef0b1c02afc8b1e26c6a86b2c41bc1842ae92baf
  languageName: node
  linkType: hard

"hono@npm:^4.7.11":
  version: 4.8.2
  resolution: "hono@npm:4.8.2"
  checksum: 10c0/371092393238fa9628efc9aaf41e3f6da33db242aabec858fd1bf9509b0b3b74883f7ca724cc41143ccdbc03617f2606911a97a0a0632f97c14731769412ef31
  languageName: node
  linkType: hard

"html-escaper@npm:^2.0.0":
  version: 2.0.2
  resolution: "html-escaper@npm:2.0.2"
  checksum: 10c0/208e8a12de1a6569edbb14544f4567e6ce8ecc30b9394fcaa4e7bb1e60c12a7c9a1ed27e31290817157e8626f3a4f29e76c8747030822eb84a6abb15c255f0a0
  languageName: node
  linkType: hard

"html-void-elements@npm:^3.0.0":
  version: 3.0.0
  resolution: "html-void-elements@npm:3.0.0"
  checksum: 10c0/a8b9ec5db23b7c8053876dad73a0336183e6162bf6d2677376d8b38d654fdc59ba74fdd12f8812688f7db6fad451210c91b300e472afc0909224e0a44c8610d2
  languageName: node
  linkType: hard

"http-cache-semantics@npm:^4.1.1":
  version: 4.2.0
  resolution: "http-cache-semantics@npm:4.2.0"
  checksum: 10c0/45b66a945cf13ec2d1f29432277201313babf4a01d9e52f44b31ca923434083afeca03f18417f599c9ab3d0e7b618ceb21257542338b57c54b710463b4a53e37
  languageName: node
  linkType: hard

"http-errors@npm:2.0.0, http-errors@npm:^2.0.0":
  version: 2.0.0
  resolution: "http-errors@npm:2.0.0"
  dependencies:
    depd: "npm:2.0.0"
    inherits: "npm:2.0.4"
    setprototypeof: "npm:1.2.0"
    statuses: "npm:2.0.1"
    toidentifier: "npm:1.0.1"
  checksum: 10c0/fc6f2715fe188d091274b5ffc8b3657bd85c63e969daa68ccb77afb05b071a4b62841acb7a21e417b5539014dff2ebf9550f0b14a9ff126f2734a7c1387f8e19
  languageName: node
  linkType: hard

"http-proxy-agent@npm:^7.0.0":
  version: 7.0.2
  resolution: "http-proxy-agent@npm:7.0.2"
  dependencies:
    agent-base: "npm:^7.1.0"
    debug: "npm:^4.3.4"
  checksum: 10c0/4207b06a4580fb85dd6dff521f0abf6db517489e70863dca1a0291daa7f2d3d2d6015a57bd702af068ea5cf9f1f6ff72314f5f5b4228d299c0904135d2aef921
  languageName: node
  linkType: hard

"https-proxy-agent@npm:^7.0.1":
  version: 7.0.6
  resolution: "https-proxy-agent@npm:7.0.6"
  dependencies:
    agent-base: "npm:^7.1.2"
    debug: "npm:4"
  checksum: 10c0/f729219bc735edb621fa30e6e84e60ee5d00802b8247aac0d7b79b0bd6d4b3294737a337b93b86a0bd9e68099d031858a39260c976dc14cdbba238ba1f8779ac
  languageName: node
  linkType: hard

"human-signals@npm:^2.1.0":
  version: 2.1.0
  resolution: "human-signals@npm:2.1.0"
  checksum: 10c0/695edb3edfcfe9c8b52a76926cd31b36978782062c0ed9b1192b36bebc75c4c87c82e178dfcb0ed0fc27ca59d434198aac0bd0be18f5781ded775604db22304a
  languageName: node
  linkType: hard

"human-signals@npm:^5.0.0":
  version: 5.0.0
  resolution: "human-signals@npm:5.0.0"
  checksum: 10c0/5a9359073fe17a8b58e5a085e9a39a950366d9f00217c4ff5878bd312e09d80f460536ea6a3f260b5943a01fe55c158d1cea3fc7bee3d0520aeef04f6d915c82
  languageName: node
  linkType: hard

"human-signals@npm:^8.0.1":
  version: 8.0.1
  resolution: "human-signals@npm:8.0.1"
  checksum: 10c0/195ac607108c56253757717242e17cd2e21b29f06c5d2dad362e86c672bf2d096e8a3bbb2601841c376c2301c4ae7cff129e87f740aa4ebff1390c163114c7c4
  languageName: node
  linkType: hard

"humanize-duration@npm:^3.27.3":
  version: 3.33.0
  resolution: "humanize-duration@npm:3.33.0"
  checksum: 10c0/516c966e848177df526444f83f27d92fdfb7b96f8cd3d42e5b1bdbda950c8b56db0ac8ea25bc130445b577d19c382c35fe3d458643c10f0fe071ab1b8fe8ad80
  languageName: node
  linkType: hard

"iconv-lite@npm:0.4.24":
  version: 0.4.24
  resolution: "iconv-lite@npm:0.4.24"
  dependencies:
    safer-buffer: "npm:>= 2.1.2 < 3"
  checksum: 10c0/c6886a24cc00f2a059767440ec1bc00d334a89f250db8e0f7feb4961c8727118457e27c495ba94d082e51d3baca378726cd110aaf7ded8b9bbfd6a44760cf1d4
  languageName: node
  linkType: hard

"iconv-lite@npm:0.6.3, iconv-lite@npm:^0.6.2, iconv-lite@npm:^0.6.3":
  version: 0.6.3
  resolution: "iconv-lite@npm:0.6.3"
  dependencies:
    safer-buffer: "npm:>= 2.1.2 < 3.0.0"
  checksum: 10c0/98102bc66b33fcf5ac044099d1257ba0b7ad5e3ccd3221f34dd508ab4070edff183276221684e1e0555b145fce0850c9f7d2b60a9fcac50fbb4ea0d6e845a3b1
  languageName: node
  linkType: hard

"ieee754@npm:^1.2.1":
  version: 1.2.1
  resolution: "ieee754@npm:1.2.1"
  checksum: 10c0/b0782ef5e0935b9f12883a2e2aa37baa75da6e66ce6515c168697b42160807d9330de9a32ec1ed73149aea02e0d822e572bca6f1e22bdcbd2149e13b050b17bb
  languageName: node
  linkType: hard

"ignore-by-default@npm:^1.0.1":
  version: 1.0.1
  resolution: "ignore-by-default@npm:1.0.1"
  checksum: 10c0/9ab6e70e80f7cc12735def7ecb5527cfa56ab4e1152cd64d294522827f2dcf1f6d85531241537dc3713544e88dd888f65cb3c49c7b2cddb9009087c75274e533
  languageName: node
  linkType: hard

"ignore@npm:^7.0.3":
  version: 7.0.5
  resolution: "ignore@npm:7.0.5"
  checksum: 10c0/ae00db89fe873064a093b8999fe4cc284b13ef2a178636211842cceb650b9c3e390d3339191acb145d81ed5379d2074840cf0c33a20bdbd6f32821f79eb4ad5d
  languageName: node
  linkType: hard

"import-in-the-middle@npm:^1.8.1":
  version: 1.14.2
  resolution: "import-in-the-middle@npm:1.14.2"
  dependencies:
    acorn: "npm:^8.14.0"
    acorn-import-attributes: "npm:^1.9.5"
    cjs-module-lexer: "npm:^1.2.2"
    module-details-from-path: "npm:^1.0.3"
  checksum: 10c0/ee614a09d3772ad39f8005d798458e541cbfb897b8f97623f4bac527eecc51984586cfbf8f195715df5c463ec0b7fce8086489dc12f758ffe5ea485b8a29da01
  languageName: node
  linkType: hard

"import-local@npm:^3.0.2":
  version: 3.2.0
  resolution: "import-local@npm:3.2.0"
  dependencies:
    pkg-dir: "npm:^4.2.0"
    resolve-cwd: "npm:^3.0.0"
  bin:
    import-local-fixture: fixtures/cli.js
  checksum: 10c0/94cd6367a672b7e0cb026970c85b76902d2710a64896fa6de93bd5c571dd03b228c5759308959de205083e3b1c61e799f019c9e36ee8e9c523b993e1057f0433
  languageName: node
  linkType: hard

"imurmurhash@npm:^0.1.4":
  version: 0.1.4
  resolution: "imurmurhash@npm:0.1.4"
  checksum: 10c0/8b51313850dd33605c6c9d3fd9638b714f4c4c40250cff658209f30d40da60f78992fb2df5dabee4acf589a6a82bbc79ad5486550754bd9ec4e3fc0d4a57d6a6
  languageName: node
  linkType: hard

"inflight@npm:^1.0.4":
  version: 1.0.6
  resolution: "inflight@npm:1.0.6"
  dependencies:
    once: "npm:^1.3.0"
    wrappy: "npm:1"
  checksum: 10c0/7faca22584600a9dc5b9fca2cd5feb7135ac8c935449837b315676b4c90aa4f391ec4f42240178244b5a34e8bede1948627fda392ca3191522fc46b34e985ab2
  languageName: node
  linkType: hard

"inherits@npm:2, inherits@npm:2.0.4, inherits@npm:^2.0.3":
  version: 2.0.4
  resolution: "inherits@npm:2.0.4"
  checksum: 10c0/4e531f648b29039fb7426fb94075e6545faa1eb9fe83c29f0b6d9e7263aceb4289d2d4557db0d428188eeb449cc7c5e77b0a0b2c4e248ff2a65933a0dee49ef2
  languageName: node
  linkType: hard

"ioredis@npm:^5.4.1, ioredis@npm:^5.6.1":
  version: 5.6.1
  resolution: "ioredis@npm:5.6.1"
  dependencies:
    "@ioredis/commands": "npm:^1.1.1"
    cluster-key-slot: "npm:^1.1.0"
    debug: "npm:^4.3.4"
    denque: "npm:^2.1.0"
    lodash.defaults: "npm:^4.2.0"
    lodash.isarguments: "npm:^3.1.0"
    redis-errors: "npm:^1.2.0"
    redis-parser: "npm:^3.0.0"
    standard-as-callback: "npm:^2.1.0"
  checksum: 10c0/26ae49cf448e807e454a9bdea5a9dfdcf669e2fdbf2df341900a0fb693c5662fea7e39db3227ce8972d1bda0ba7da9b7410e5163b12d8878a579548d847220ac
  languageName: node
  linkType: hard

"ip-address@npm:^9.0.5":
  version: 9.0.5
  resolution: "ip-address@npm:9.0.5"
  dependencies:
    jsbn: "npm:1.1.0"
    sprintf-js: "npm:^1.1.3"
  checksum: 10c0/331cd07fafcb3b24100613e4b53e1a2b4feab11e671e655d46dc09ee233da5011284d09ca40c4ecbdfe1d0004f462958675c224a804259f2f78d2465a87824bc
  languageName: node
  linkType: hard

"ip-regex@npm:^4.1.0":
  version: 4.3.0
  resolution: "ip-regex@npm:4.3.0"
  checksum: 10c0/f9ef1f5d0df05b9133a882974e572ae525ccd205260cb103dae337f1fc7451ed783391acc6ad688e56dd2598f769e8e72ecbb650ec34763396af822a91768562
  languageName: node
  linkType: hard

"ipaddr.js@npm:1.9.1":
  version: 1.9.1
  resolution: "ipaddr.js@npm:1.9.1"
  checksum: 10c0/0486e775047971d3fdb5fb4f063829bac45af299ae0b82dcf3afa2145338e08290563a2a70f34b732d795ecc8311902e541a8530eeb30d75860a78ff4e94ce2a
  languageName: node
  linkType: hard

"is-arrayish@npm:^0.2.1":
  version: 0.2.1
  resolution: "is-arrayish@npm:0.2.1"
  checksum: 10c0/e7fb686a739068bb70f860b39b67afc62acc62e36bb61c5f965768abce1873b379c563e61dd2adad96ebb7edf6651111b385e490cf508378959b0ed4cac4e729
  languageName: node
  linkType: hard

"is-binary-path@npm:~2.1.0":
  version: 2.1.0
  resolution: "is-binary-path@npm:2.1.0"
  dependencies:
    binary-extensions: "npm:^2.0.0"
  checksum: 10c0/a16eaee59ae2b315ba36fad5c5dcaf8e49c3e27318f8ab8fa3cdb8772bf559c8d1ba750a589c2ccb096113bb64497084361a25960899cb6172a6925ab6123d38
  languageName: node
  linkType: hard

"is-core-module@npm:^2.16.0":
  version: 2.16.1
  resolution: "is-core-module@npm:2.16.1"
  dependencies:
    hasown: "npm:^2.0.2"
  checksum: 10c0/898443c14780a577e807618aaae2b6f745c8538eca5c7bc11388a3f2dc6de82b9902bcc7eb74f07be672b11bbe82dd6a6edded44a00cb3d8f933d0459905eedd
  languageName: node
  linkType: hard

"is-docker@npm:^3.0.0":
  version: 3.0.0
  resolution: "is-docker@npm:3.0.0"
  bin:
    is-docker: cli.js
  checksum: 10c0/d2c4f8e6d3e34df75a5defd44991b6068afad4835bb783b902fa12d13ebdb8f41b2a199dcb0b5ed2cb78bfee9e4c0bbdb69c2d9646f4106464674d3e697a5856
  languageName: node
  linkType: hard

"is-extglob@npm:^2.1.1":
  version: 2.1.1
  resolution: "is-extglob@npm:2.1.1"
  checksum: 10c0/5487da35691fbc339700bbb2730430b07777a3c21b9ebaecb3072512dfd7b4ba78ac2381a87e8d78d20ea08affb3f1971b4af629173a6bf435ff8a4c47747912
  languageName: node
  linkType: hard

"is-fullwidth-code-point@npm:^3.0.0":
  version: 3.0.0
  resolution: "is-fullwidth-code-point@npm:3.0.0"
  checksum: 10c0/bb11d825e049f38e04c06373a8d72782eee0205bda9d908cc550ccb3c59b99d750ff9537982e01733c1c94a58e35400661f57042158ff5e8f3e90cf936daf0fc
  languageName: node
  linkType: hard

"is-generator-fn@npm:^2.0.0":
  version: 2.1.0
  resolution: "is-generator-fn@npm:2.1.0"
  checksum: 10c0/2957cab387997a466cd0bf5c1b6047bd21ecb32bdcfd8996b15747aa01002c1c88731802f1b3d34ac99f4f6874b626418bd118658cf39380fe5fff32a3af9c4d
  languageName: node
  linkType: hard

"is-glob@npm:^4.0.1, is-glob@npm:~4.0.1":
  version: 4.0.3
  resolution: "is-glob@npm:4.0.3"
  dependencies:
    is-extglob: "npm:^2.1.1"
  checksum: 10c0/17fb4014e22be3bbecea9b2e3a76e9e34ff645466be702f1693e8f1ee1adac84710d0be0bd9f967d6354036fd51ab7c2741d954d6e91dae6bb69714de92c197a
  languageName: node
  linkType: hard

"is-inside-container@npm:^1.0.0":
  version: 1.0.0
  resolution: "is-inside-container@npm:1.0.0"
  dependencies:
    is-docker: "npm:^3.0.0"
  bin:
    is-inside-container: cli.js
  checksum: 10c0/a8efb0e84f6197e6ff5c64c52890fa9acb49b7b74fed4da7c95383965da6f0fa592b4dbd5e38a79f87fc108196937acdbcd758fcefc9b140e479b39ce1fcd1cd
  languageName: node
  linkType: hard

"is-module@npm:^1.0.0":
  version: 1.0.0
  resolution: "is-module@npm:1.0.0"
  checksum: 10c0/795a3914bcae7c26a1c23a1e5574c42eac13429625045737bf3e324ce865c0601d61aee7a5afbca1bee8cb300c7d9647e7dc98860c9bdbc3b7fdc51d8ac0bffc
  languageName: node
  linkType: hard

"is-number@npm:^7.0.0":
  version: 7.0.0
  resolution: "is-number@npm:7.0.0"
  checksum: 10c0/b4686d0d3053146095ccd45346461bc8e53b80aeb7671cc52a4de02dbbf7dc0d1d2a986e2fe4ae206984b4d34ef37e8b795ebc4f4295c978373e6575e295d811
  languageName: node
  linkType: hard

"is-plain-obj@npm:^4.1.0":
  version: 4.1.0
  resolution: "is-plain-obj@npm:4.1.0"
  checksum: 10c0/32130d651d71d9564dc88ba7e6fda0e91a1010a3694648e9f4f47bb6080438140696d3e3e15c741411d712e47ac9edc1a8a9de1fe76f3487b0d90be06ac9975e
  languageName: node
  linkType: hard

"is-promise@npm:^4.0.0":
  version: 4.0.0
  resolution: "is-promise@npm:4.0.0"
  checksum: 10c0/ebd5c672d73db781ab33ccb155fb9969d6028e37414d609b115cc534654c91ccd061821d5b987eefaa97cf4c62f0b909bb2f04db88306de26e91bfe8ddc01503
  languageName: node
  linkType: hard

"is-reference@npm:1.2.1":
  version: 1.2.1
  resolution: "is-reference@npm:1.2.1"
  dependencies:
    "@types/estree": "npm:*"
  checksum: 10c0/7dc819fc8de7790264a0a5d531164f9f5b9ef5aa1cd05f35322d14db39c8a2ec78fd5d4bf57f9789f3ddd2b3abeea7728432b759636157a42db12a9e8c3b549b
  languageName: node
  linkType: hard

"is-stream@npm:^2.0.0":
  version: 2.0.1
  resolution: "is-stream@npm:2.0.1"
  checksum: 10c0/7c284241313fc6efc329b8d7f08e16c0efeb6baab1b4cd0ba579eb78e5af1aa5da11e68559896a2067cd6c526bd29241dda4eb1225e627d5aa1a89a76d4635a5
  languageName: node
  linkType: hard

"is-stream@npm:^3.0.0":
  version: 3.0.0
  resolution: "is-stream@npm:3.0.0"
  checksum: 10c0/eb2f7127af02ee9aa2a0237b730e47ac2de0d4e76a4a905a50a11557f2339df5765eaea4ceb8029f1efa978586abe776908720bfcb1900c20c6ec5145f6f29d8
  languageName: node
  linkType: hard

"is-stream@npm:^4.0.1":
  version: 4.0.1
  resolution: "is-stream@npm:4.0.1"
  checksum: 10c0/2706c7f19b851327ba374687bc4a3940805e14ca496dc672b9629e744d143b1ad9c6f1b162dece81c7bfbc0f83b32b61ccc19ad2e05aad2dd7af347408f60c7f
  languageName: node
  linkType: hard

"is-unicode-supported@npm:^2.0.0":
  version: 2.1.0
  resolution: "is-unicode-supported@npm:2.1.0"
  checksum: 10c0/a0f53e9a7c1fdbcf2d2ef6e40d4736fdffff1c9f8944c75e15425118ff3610172c87bf7bc6c34d3903b04be59790bb2212ddbe21ee65b5a97030fc50370545a5
  languageName: node
  linkType: hard

"is-url@npm:^1.2.4":
  version: 1.2.4
  resolution: "is-url@npm:1.2.4"
  checksum: 10c0/0157a79874f8f95fdd63540e3f38c8583c2ef572661cd0693cda80ae3e42dfe8e9a4a972ec1b827f861d9a9acf75b37f7d58a37f94a8a053259642912c252bc3
  languageName: node
  linkType: hard

"is-what@npm:^4.1.8":
  version: 4.1.16
  resolution: "is-what@npm:4.1.16"
  checksum: 10c0/611f1947776826dcf85b57cfb7bd3b3ea6f4b94a9c2f551d4a53f653cf0cb9d1e6518846648256d46ee6c91d114b6d09d2ac8a07306f7430c5900f87466aae5b
  languageName: node
  linkType: hard

"is-wsl@npm:^3.1.0":
  version: 3.1.0
  resolution: "is-wsl@npm:3.1.0"
  dependencies:
    is-inside-container: "npm:^1.0.0"
  checksum: 10c0/d3317c11995690a32c362100225e22ba793678fe8732660c6de511ae71a0ff05b06980cf21f98a6bf40d7be0e9e9506f859abe00a1118287d63e53d0a3d06947
  languageName: node
  linkType: hard

"is2@npm:^2.0.6":
  version: 2.0.9
  resolution: "is2@npm:2.0.9"
  dependencies:
    deep-is: "npm:^0.1.3"
    ip-regex: "npm:^4.1.0"
    is-url: "npm:^1.2.4"
  checksum: 10c0/51090a2ad046651c1523e6aec98843c2be4b61fdafa5a68d89966b7d3b7116fdc68cfb218cfc3825eb20175fa741de2f89249546352dbc4ac1d86847fa4a084a
  languageName: node
  linkType: hard

"isexe@npm:^2.0.0":
  version: 2.0.0
  resolution: "isexe@npm:2.0.0"
  checksum: 10c0/228cfa503fadc2c31596ab06ed6aa82c9976eec2bfd83397e7eaf06d0ccf42cd1dfd6743bf9aeb01aebd4156d009994c5f76ea898d2832c1fe342da923ca457d
  languageName: node
  linkType: hard

"isexe@npm:^3.1.1":
  version: 3.1.1
  resolution: "isexe@npm:3.1.1"
  checksum: 10c0/9ec257654093443eb0a528a9c8cbba9c0ca7616ccb40abd6dde7202734d96bb86e4ac0d764f0f8cd965856aacbff2f4ce23e730dc19dfb41e3b0d865ca6fdcc7
  languageName: node
  linkType: hard

"isstream@npm:0.1.x":
  version: 0.1.2
  resolution: "isstream@npm:0.1.2"
  checksum: 10c0/a6686a878735ca0a48e0d674dd6d8ad31aedfaf70f07920da16ceadc7577b46d67179a60b313f2e6860cb097a2c2eb3cbd0b89e921ae89199a59a17c3273d66f
  languageName: node
  linkType: hard

"istanbul-lib-coverage@npm:^3.0.0, istanbul-lib-coverage@npm:^3.2.0":
  version: 3.2.2
  resolution: "istanbul-lib-coverage@npm:3.2.2"
  checksum: 10c0/6c7ff2106769e5f592ded1fb418f9f73b4411fd5a084387a5410538332b6567cd1763ff6b6cadca9b9eb2c443cce2f7ea7d7f1b8d315f9ce58539793b1e0922b
  languageName: node
  linkType: hard

"istanbul-lib-instrument@npm:^5.0.4":
  version: 5.2.1
  resolution: "istanbul-lib-instrument@npm:5.2.1"
  dependencies:
    "@babel/core": "npm:^7.12.3"
    "@babel/parser": "npm:^7.14.7"
    "@istanbuljs/schema": "npm:^0.1.2"
    istanbul-lib-coverage: "npm:^3.2.0"
    semver: "npm:^6.3.0"
  checksum: 10c0/8a1bdf3e377dcc0d33ec32fe2b6ecacdb1e4358fd0eb923d4326bb11c67622c0ceb99600a680f3dad5d29c66fc1991306081e339b4d43d0b8a2ab2e1d910a6ee
  languageName: node
  linkType: hard

"istanbul-lib-instrument@npm:^6.0.0":
  version: 6.0.3
  resolution: "istanbul-lib-instrument@npm:6.0.3"
  dependencies:
    "@babel/core": "npm:^7.23.9"
    "@babel/parser": "npm:^7.23.9"
    "@istanbuljs/schema": "npm:^0.1.3"
    istanbul-lib-coverage: "npm:^3.2.0"
    semver: "npm:^7.5.4"
  checksum: 10c0/a1894e060dd2a3b9f046ffdc87b44c00a35516f5e6b7baf4910369acca79e506fc5323a816f811ae23d82334b38e3ddeb8b3b331bd2c860540793b59a8689128
  languageName: node
  linkType: hard

"istanbul-lib-report@npm:^3.0.0":
  version: 3.0.1
  resolution: "istanbul-lib-report@npm:3.0.1"
  dependencies:
    istanbul-lib-coverage: "npm:^3.0.0"
    make-dir: "npm:^4.0.0"
    supports-color: "npm:^7.1.0"
  checksum: 10c0/84323afb14392de8b6a5714bd7e9af845cfbd56cfe71ed276cda2f5f1201aea673c7111901227ee33e68e4364e288d73861eb2ed48f6679d1e69a43b6d9b3ba7
  languageName: node
  linkType: hard

"istanbul-lib-source-maps@npm:^4.0.0":
  version: 4.0.1
  resolution: "istanbul-lib-source-maps@npm:4.0.1"
  dependencies:
    debug: "npm:^4.1.1"
    istanbul-lib-coverage: "npm:^3.0.0"
    source-map: "npm:^0.6.1"
  checksum: 10c0/19e4cc405016f2c906dff271a76715b3e881fa9faeb3f09a86cb99b8512b3a5ed19cadfe0b54c17ca0e54c1142c9c6de9330d65506e35873994e06634eebeb66
  languageName: node
  linkType: hard

"istanbul-reports@npm:^3.1.3":
  version: 3.1.7
  resolution: "istanbul-reports@npm:3.1.7"
  dependencies:
    html-escaper: "npm:^2.0.0"
    istanbul-lib-report: "npm:^3.0.0"
  checksum: 10c0/a379fadf9cf8dc5dfe25568115721d4a7eb82fbd50b005a6672aff9c6989b20cc9312d7865814e0859cd8df58cbf664482e1d3604be0afde1f7fc3ccc1394a51
  languageName: node
  linkType: hard

"jackspeak@npm:^3.1.2":
  version: 3.4.3
  resolution: "jackspeak@npm:3.4.3"
  dependencies:
    "@isaacs/cliui": "npm:^8.0.2"
    "@pkgjs/parseargs": "npm:^0.11.0"
  dependenciesMeta:
    "@pkgjs/parseargs":
      optional: true
  checksum: 10c0/6acc10d139eaefdbe04d2f679e6191b3abf073f111edf10b1de5302c97ec93fffeb2fdd8681ed17f16268aa9dd4f8c588ed9d1d3bffbbfa6e8bf897cbb3149b9
  languageName: node
  linkType: hard

"jake@npm:^10.8.5":
  version: 10.9.2
  resolution: "jake@npm:10.9.2"
  dependencies:
    async: "npm:^3.2.3"
    chalk: "npm:^4.0.2"
    filelist: "npm:^1.0.4"
    minimatch: "npm:^3.1.2"
  bin:
    jake: bin/cli.js
  checksum: 10c0/c4597b5ed9b6a908252feab296485a4f87cba9e26d6c20e0ca144fb69e0c40203d34a2efddb33b3d297b8bd59605e6c1f44f6221ca1e10e69175ecbf3ff5fe31
  languageName: node
  linkType: hard

"jest-changed-files@npm:^29.7.0":
  version: 29.7.0
  resolution: "jest-changed-files@npm:29.7.0"
  dependencies:
    execa: "npm:^5.0.0"
    jest-util: "npm:^29.7.0"
    p-limit: "npm:^3.1.0"
  checksum: 10c0/e071384d9e2f6bb462231ac53f29bff86f0e12394c1b49ccafbad225ce2ab7da226279a8a94f421949920bef9be7ef574fd86aee22e8adfa149be73554ab828b
  languageName: node
  linkType: hard

"jest-circus@npm:^29.7.0":
  version: 29.7.0
  resolution: "jest-circus@npm:29.7.0"
  dependencies:
    "@jest/environment": "npm:^29.7.0"
    "@jest/expect": "npm:^29.7.0"
    "@jest/test-result": "npm:^29.7.0"
    "@jest/types": "npm:^29.6.3"
    "@types/node": "npm:*"
    chalk: "npm:^4.0.0"
    co: "npm:^4.6.0"
    dedent: "npm:^1.0.0"
    is-generator-fn: "npm:^2.0.0"
    jest-each: "npm:^29.7.0"
    jest-matcher-utils: "npm:^29.7.0"
    jest-message-util: "npm:^29.7.0"
    jest-runtime: "npm:^29.7.0"
    jest-snapshot: "npm:^29.7.0"
    jest-util: "npm:^29.7.0"
    p-limit: "npm:^3.1.0"
    pretty-format: "npm:^29.7.0"
    pure-rand: "npm:^6.0.0"
    slash: "npm:^3.0.0"
    stack-utils: "npm:^2.0.3"
  checksum: 10c0/8d15344cf7a9f14e926f0deed64ed190c7a4fa1ed1acfcd81e4cc094d3cc5bf7902ebb7b874edc98ada4185688f90c91e1747e0dfd7ac12463b097968ae74b5e
  languageName: node
  linkType: hard

"jest-cli@npm:^29.7.0":
  version: 29.7.0
  resolution: "jest-cli@npm:29.7.0"
  dependencies:
    "@jest/core": "npm:^29.7.0"
    "@jest/test-result": "npm:^29.7.0"
    "@jest/types": "npm:^29.6.3"
    chalk: "npm:^4.0.0"
    create-jest: "npm:^29.7.0"
    exit: "npm:^0.1.2"
    import-local: "npm:^3.0.2"
    jest-config: "npm:^29.7.0"
    jest-util: "npm:^29.7.0"
    jest-validate: "npm:^29.7.0"
    yargs: "npm:^17.3.1"
  peerDependencies:
    node-notifier: ^8.0.1 || ^9.0.0 || ^10.0.0
  peerDependenciesMeta:
    node-notifier:
      optional: true
  bin:
    jest: bin/jest.js
  checksum: 10c0/a658fd55050d4075d65c1066364595962ead7661711495cfa1dfeecf3d6d0a8ffec532f3dbd8afbb3e172dd5fd2fb2e813c5e10256e7cf2fea766314942fb43a
  languageName: node
  linkType: hard

"jest-config@npm:^29.7.0":
  version: 29.7.0
  resolution: "jest-config@npm:29.7.0"
  dependencies:
    "@babel/core": "npm:^7.11.6"
    "@jest/test-sequencer": "npm:^29.7.0"
    "@jest/types": "npm:^29.6.3"
    babel-jest: "npm:^29.7.0"
    chalk: "npm:^4.0.0"
    ci-info: "npm:^3.2.0"
    deepmerge: "npm:^4.2.2"
    glob: "npm:^7.1.3"
    graceful-fs: "npm:^4.2.9"
    jest-circus: "npm:^29.7.0"
    jest-environment-node: "npm:^29.7.0"
    jest-get-type: "npm:^29.6.3"
    jest-regex-util: "npm:^29.6.3"
    jest-resolve: "npm:^29.7.0"
    jest-runner: "npm:^29.7.0"
    jest-util: "npm:^29.7.0"
    jest-validate: "npm:^29.7.0"
    micromatch: "npm:^4.0.4"
    parse-json: "npm:^5.2.0"
    pretty-format: "npm:^29.7.0"
    slash: "npm:^3.0.0"
    strip-json-comments: "npm:^3.1.1"
  peerDependencies:
    "@types/node": "*"
    ts-node: ">=9.0.0"
  peerDependenciesMeta:
    "@types/node":
      optional: true
    ts-node:
      optional: true
  checksum: 10c0/bab23c2eda1fff06e0d104b00d6adfb1d1aabb7128441899c9bff2247bd26710b050a5364281ce8d52b46b499153bf7e3ee88b19831a8f3451f1477a0246a0f1
  languageName: node
  linkType: hard

"jest-diff@npm:^29.7.0":
  version: 29.7.0
  resolution: "jest-diff@npm:29.7.0"
  dependencies:
    chalk: "npm:^4.0.0"
    diff-sequences: "npm:^29.6.3"
    jest-get-type: "npm:^29.6.3"
    pretty-format: "npm:^29.7.0"
  checksum: 10c0/89a4a7f182590f56f526443dde69acefb1f2f0c9e59253c61d319569856c4931eae66b8a3790c443f529267a0ddba5ba80431c585deed81827032b2b2a1fc999
  languageName: node
  linkType: hard

"jest-docblock@npm:^29.7.0":
  version: 29.7.0
  resolution: "jest-docblock@npm:29.7.0"
  dependencies:
    detect-newline: "npm:^3.0.0"
  checksum: 10c0/d932a8272345cf6b6142bb70a2bb63e0856cc0093f082821577ea5bdf4643916a98744dfc992189d2b1417c38a11fa42466f6111526bc1fb81366f56410f3be9
  languageName: node
  linkType: hard

"jest-each@npm:^29.7.0":
  version: 29.7.0
  resolution: "jest-each@npm:29.7.0"
  dependencies:
    "@jest/types": "npm:^29.6.3"
    chalk: "npm:^4.0.0"
    jest-get-type: "npm:^29.6.3"
    jest-util: "npm:^29.7.0"
    pretty-format: "npm:^29.7.0"
  checksum: 10c0/f7f9a90ebee80cc688e825feceb2613627826ac41ea76a366fa58e669c3b2403d364c7c0a74d862d469b103c843154f8456d3b1c02b487509a12afa8b59edbb4
  languageName: node
  linkType: hard

"jest-environment-node@npm:^29.7.0":
  version: 29.7.0
  resolution: "jest-environment-node@npm:29.7.0"
  dependencies:
    "@jest/environment": "npm:^29.7.0"
    "@jest/fake-timers": "npm:^29.7.0"
    "@jest/types": "npm:^29.6.3"
    "@types/node": "npm:*"
    jest-mock: "npm:^29.7.0"
    jest-util: "npm:^29.7.0"
  checksum: 10c0/61f04fec077f8b1b5c1a633e3612fc0c9aa79a0ab7b05600683428f1e01a4d35346c474bde6f439f9fcc1a4aa9a2861ff852d079a43ab64b02105d1004b2592b
  languageName: node
  linkType: hard

"jest-get-type@npm:^29.6.3":
  version: 29.6.3
  resolution: "jest-get-type@npm:29.6.3"
  checksum: 10c0/552e7a97a983d3c2d4e412a44eb7de0430ff773dd99f7500962c268d6dfbfa431d7d08f919c9d960530e5f7f78eb47f267ad9b318265e5092b3ff9ede0db7c2b
  languageName: node
  linkType: hard

"jest-haste-map@npm:^29.7.0":
  version: 29.7.0
  resolution: "jest-haste-map@npm:29.7.0"
  dependencies:
    "@jest/types": "npm:^29.6.3"
    "@types/graceful-fs": "npm:^4.1.3"
    "@types/node": "npm:*"
    anymatch: "npm:^3.0.3"
    fb-watchman: "npm:^2.0.0"
    fsevents: "npm:^2.3.2"
    graceful-fs: "npm:^4.2.9"
    jest-regex-util: "npm:^29.6.3"
    jest-util: "npm:^29.7.0"
    jest-worker: "npm:^29.7.0"
    micromatch: "npm:^4.0.4"
    walker: "npm:^1.0.8"
  dependenciesMeta:
    fsevents:
      optional: true
  checksum: 10c0/2683a8f29793c75a4728787662972fedd9267704c8f7ef9d84f2beed9a977f1cf5e998c07b6f36ba5603f53cb010c911fe8cd0ac9886e073fe28ca66beefd30c
  languageName: node
  linkType: hard

"jest-leak-detector@npm:^29.7.0":
  version: 29.7.0
  resolution: "jest-leak-detector@npm:29.7.0"
  dependencies:
    jest-get-type: "npm:^29.6.3"
    pretty-format: "npm:^29.7.0"
  checksum: 10c0/71bb9f77fc489acb842a5c7be030f2b9acb18574dc9fb98b3100fc57d422b1abc55f08040884bd6e6dbf455047a62f7eaff12aa4058f7cbdc11558718ca6a395
  languageName: node
  linkType: hard

"jest-matcher-utils@npm:^29.7.0":
  version: 29.7.0
  resolution: "jest-matcher-utils@npm:29.7.0"
  dependencies:
    chalk: "npm:^4.0.0"
    jest-diff: "npm:^29.7.0"
    jest-get-type: "npm:^29.6.3"
    pretty-format: "npm:^29.7.0"
  checksum: 10c0/0d0e70b28fa5c7d4dce701dc1f46ae0922102aadc24ed45d594dd9b7ae0a8a6ef8b216718d1ab79e451291217e05d4d49a82666e1a3cc2b428b75cd9c933244e
  languageName: node
  linkType: hard

"jest-message-util@npm:^29.7.0":
  version: 29.7.0
  resolution: "jest-message-util@npm:29.7.0"
  dependencies:
    "@babel/code-frame": "npm:^7.12.13"
    "@jest/types": "npm:^29.6.3"
    "@types/stack-utils": "npm:^2.0.0"
    chalk: "npm:^4.0.0"
    graceful-fs: "npm:^4.2.9"
    micromatch: "npm:^4.0.4"
    pretty-format: "npm:^29.7.0"
    slash: "npm:^3.0.0"
    stack-utils: "npm:^2.0.3"
  checksum: 10c0/850ae35477f59f3e6f27efac5215f706296e2104af39232bb14e5403e067992afb5c015e87a9243ec4d9df38525ef1ca663af9f2f4766aa116f127247008bd22
  languageName: node
  linkType: hard

"jest-mock@npm:^29.7.0":
  version: 29.7.0
  resolution: "jest-mock@npm:29.7.0"
  dependencies:
    "@jest/types": "npm:^29.6.3"
    "@types/node": "npm:*"
    jest-util: "npm:^29.7.0"
  checksum: 10c0/7b9f8349ee87695a309fe15c46a74ab04c853369e5c40952d68061d9dc3159a0f0ed73e215f81b07ee97a9faaf10aebe5877a9d6255068a0977eae6a9ff1d5ac
  languageName: node
  linkType: hard

"jest-pnp-resolver@npm:^1.2.2":
  version: 1.2.3
  resolution: "jest-pnp-resolver@npm:1.2.3"
  peerDependencies:
    jest-resolve: "*"
  peerDependenciesMeta:
    jest-resolve:
      optional: true
  checksum: 10c0/86eec0c78449a2de733a6d3e316d49461af6a858070e113c97f75fb742a48c2396ea94150cbca44159ffd4a959f743a47a8b37a792ef6fdad2cf0a5cba973fac
  languageName: node
  linkType: hard

"jest-regex-util@npm:^29.6.3":
  version: 29.6.3
  resolution: "jest-regex-util@npm:29.6.3"
  checksum: 10c0/4e33fb16c4f42111159cafe26397118dcfc4cf08bc178a67149fb05f45546a91928b820894572679d62559839d0992e21080a1527faad65daaae8743a5705a3b
  languageName: node
  linkType: hard

"jest-resolve-dependencies@npm:^29.7.0":
  version: 29.7.0
  resolution: "jest-resolve-dependencies@npm:29.7.0"
  dependencies:
    jest-regex-util: "npm:^29.6.3"
    jest-snapshot: "npm:^29.7.0"
  checksum: 10c0/b6e9ad8ae5b6049474118ea6441dfddd385b6d1fc471db0136f7c8fbcfe97137a9665e4f837a9f49f15a29a1deb95a14439b7aec812f3f99d08f228464930f0d
  languageName: node
  linkType: hard

"jest-resolve@npm:^29.7.0":
  version: 29.7.0
  resolution: "jest-resolve@npm:29.7.0"
  dependencies:
    chalk: "npm:^4.0.0"
    graceful-fs: "npm:^4.2.9"
    jest-haste-map: "npm:^29.7.0"
    jest-pnp-resolver: "npm:^1.2.2"
    jest-util: "npm:^29.7.0"
    jest-validate: "npm:^29.7.0"
    resolve: "npm:^1.20.0"
    resolve.exports: "npm:^2.0.0"
    slash: "npm:^3.0.0"
  checksum: 10c0/59da5c9c5b50563e959a45e09e2eace783d7f9ac0b5dcc6375dea4c0db938d2ebda97124c8161310082760e8ebbeff9f6b177c15ca2f57fb424f637a5d2adb47
  languageName: node
  linkType: hard

"jest-runner@npm:^29.7.0":
  version: 29.7.0
  resolution: "jest-runner@npm:29.7.0"
  dependencies:
    "@jest/console": "npm:^29.7.0"
    "@jest/environment": "npm:^29.7.0"
    "@jest/test-result": "npm:^29.7.0"
    "@jest/transform": "npm:^29.7.0"
    "@jest/types": "npm:^29.6.3"
    "@types/node": "npm:*"
    chalk: "npm:^4.0.0"
    emittery: "npm:^0.13.1"
    graceful-fs: "npm:^4.2.9"
    jest-docblock: "npm:^29.7.0"
    jest-environment-node: "npm:^29.7.0"
    jest-haste-map: "npm:^29.7.0"
    jest-leak-detector: "npm:^29.7.0"
    jest-message-util: "npm:^29.7.0"
    jest-resolve: "npm:^29.7.0"
    jest-runtime: "npm:^29.7.0"
    jest-util: "npm:^29.7.0"
    jest-watcher: "npm:^29.7.0"
    jest-worker: "npm:^29.7.0"
    p-limit: "npm:^3.1.0"
    source-map-support: "npm:0.5.13"
  checksum: 10c0/2194b4531068d939f14c8d3274fe5938b77fa73126aedf9c09ec9dec57d13f22c72a3b5af01ac04f5c1cf2e28d0ac0b4a54212a61b05f10b5d6b47f2a1097bb4
  languageName: node
  linkType: hard

"jest-runtime@npm:^29.7.0":
  version: 29.7.0
  resolution: "jest-runtime@npm:29.7.0"
  dependencies:
    "@jest/environment": "npm:^29.7.0"
    "@jest/fake-timers": "npm:^29.7.0"
    "@jest/globals": "npm:^29.7.0"
    "@jest/source-map": "npm:^29.6.3"
    "@jest/test-result": "npm:^29.7.0"
    "@jest/transform": "npm:^29.7.0"
    "@jest/types": "npm:^29.6.3"
    "@types/node": "npm:*"
    chalk: "npm:^4.0.0"
    cjs-module-lexer: "npm:^1.0.0"
    collect-v8-coverage: "npm:^1.0.0"
    glob: "npm:^7.1.3"
    graceful-fs: "npm:^4.2.9"
    jest-haste-map: "npm:^29.7.0"
    jest-message-util: "npm:^29.7.0"
    jest-mock: "npm:^29.7.0"
    jest-regex-util: "npm:^29.6.3"
    jest-resolve: "npm:^29.7.0"
    jest-snapshot: "npm:^29.7.0"
    jest-util: "npm:^29.7.0"
    slash: "npm:^3.0.0"
    strip-bom: "npm:^4.0.0"
  checksum: 10c0/7cd89a1deda0bda7d0941835434e44f9d6b7bd50b5c5d9b0fc9a6c990b2d4d2cab59685ab3cb2850ed4cc37059f6de903af5a50565d7f7f1192a77d3fd6dd2a6
  languageName: node
  linkType: hard

"jest-snapshot@npm:^29.7.0":
  version: 29.7.0
  resolution: "jest-snapshot@npm:29.7.0"
  dependencies:
    "@babel/core": "npm:^7.11.6"
    "@babel/generator": "npm:^7.7.2"
    "@babel/plugin-syntax-jsx": "npm:^7.7.2"
    "@babel/plugin-syntax-typescript": "npm:^7.7.2"
    "@babel/types": "npm:^7.3.3"
    "@jest/expect-utils": "npm:^29.7.0"
    "@jest/transform": "npm:^29.7.0"
    "@jest/types": "npm:^29.6.3"
    babel-preset-current-node-syntax: "npm:^1.0.0"
    chalk: "npm:^4.0.0"
    expect: "npm:^29.7.0"
    graceful-fs: "npm:^4.2.9"
    jest-diff: "npm:^29.7.0"
    jest-get-type: "npm:^29.6.3"
    jest-matcher-utils: "npm:^29.7.0"
    jest-message-util: "npm:^29.7.0"
    jest-util: "npm:^29.7.0"
    natural-compare: "npm:^1.4.0"
    pretty-format: "npm:^29.7.0"
    semver: "npm:^7.5.3"
  checksum: 10c0/6e9003c94ec58172b4a62864a91c0146513207bedf4e0a06e1e2ac70a4484088a2683e3a0538d8ea913bcfd53dc54a9b98a98cdfa562e7fe1d1339aeae1da570
  languageName: node
  linkType: hard

"jest-util@npm:^29.7.0":
  version: 29.7.0
  resolution: "jest-util@npm:29.7.0"
  dependencies:
    "@jest/types": "npm:^29.6.3"
    "@types/node": "npm:*"
    chalk: "npm:^4.0.0"
    ci-info: "npm:^3.2.0"
    graceful-fs: "npm:^4.2.9"
    picomatch: "npm:^2.2.3"
  checksum: 10c0/bc55a8f49fdbb8f51baf31d2a4f312fb66c9db1483b82f602c9c990e659cdd7ec529c8e916d5a89452ecbcfae4949b21b40a7a59d4ffc0cd813a973ab08c8150
  languageName: node
  linkType: hard

"jest-validate@npm:^29.7.0":
  version: 29.7.0
  resolution: "jest-validate@npm:29.7.0"
  dependencies:
    "@jest/types": "npm:^29.6.3"
    camelcase: "npm:^6.2.0"
    chalk: "npm:^4.0.0"
    jest-get-type: "npm:^29.6.3"
    leven: "npm:^3.1.0"
    pretty-format: "npm:^29.7.0"
  checksum: 10c0/a20b930480c1ed68778c739f4739dce39423131bc070cd2505ddede762a5570a256212e9c2401b7ae9ba4d7b7c0803f03c5b8f1561c62348213aba18d9dbece2
  languageName: node
  linkType: hard

"jest-watcher@npm:^29.7.0":
  version: 29.7.0
  resolution: "jest-watcher@npm:29.7.0"
  dependencies:
    "@jest/test-result": "npm:^29.7.0"
    "@jest/types": "npm:^29.6.3"
    "@types/node": "npm:*"
    ansi-escapes: "npm:^4.2.1"
    chalk: "npm:^4.0.0"
    emittery: "npm:^0.13.1"
    jest-util: "npm:^29.7.0"
    string-length: "npm:^4.0.1"
  checksum: 10c0/ec6c75030562fc8f8c727cb8f3b94e75d831fc718785abfc196e1f2a2ebc9a2e38744a15147170039628a853d77a3b695561ce850375ede3a4ee6037a2574567
  languageName: node
  linkType: hard

"jest-worker@npm:^29.7.0":
  version: 29.7.0
  resolution: "jest-worker@npm:29.7.0"
  dependencies:
    "@types/node": "npm:*"
    jest-util: "npm:^29.7.0"
    merge-stream: "npm:^2.0.0"
    supports-color: "npm:^8.0.0"
  checksum: 10c0/5570a3a005b16f46c131968b8a5b56d291f9bbb85ff4217e31c80bd8a02e7de799e59a54b95ca28d5c302f248b54cbffde2d177c2f0f52ffcee7504c6eabf660
  languageName: node
  linkType: hard

"jest@npm:^29.7.0":
  version: 29.7.0
  resolution: "jest@npm:29.7.0"
  dependencies:
    "@jest/core": "npm:^29.7.0"
    "@jest/types": "npm:^29.6.3"
    import-local: "npm:^3.0.2"
    jest-cli: "npm:^29.7.0"
  peerDependencies:
    node-notifier: ^8.0.1 || ^9.0.0 || ^10.0.0
  peerDependenciesMeta:
    node-notifier:
      optional: true
  bin:
    jest: bin/jest.js
  checksum: 10c0/f40eb8171cf147c617cc6ada49d062fbb03b4da666cb8d39cdbfb739a7d75eea4c3ca150fb072d0d273dce0c753db4d0467d54906ad0293f59c54f9db4a09d8b
  languageName: node
  linkType: hard

"jose@npm:^5.4.0":
  version: 5.10.0
  resolution: "jose@npm:5.10.0"
  checksum: 10c0/e20d9fc58d7e402f2e5f04e824b8897d5579aae60e64cb88ebdea1395311c24537bf4892f7de413fab1acf11e922797fb1b42269bc8fc65089a3749265ccb7b0
  languageName: node
  linkType: hard

"joycon@npm:^3.1.1":
  version: 3.1.1
  resolution: "joycon@npm:3.1.1"
  checksum: 10c0/131fb1e98c9065d067fd49b6e685487ac4ad4d254191d7aa2c9e3b90f4e9ca70430c43cad001602bdbdabcf58717d3b5c5b7461c1bd8e39478c8de706b3fe6ae
  languageName: node
  linkType: hard

"js-base64@npm:3.7.2":
  version: 3.7.2
  resolution: "js-base64@npm:3.7.2"
  checksum: 10c0/d61dae292bc3aa2fa8df5c5cb684c4a582f03fc3e9ec7a05e940cfe207b434c1b17d84d75b10931554e2b562851b76237270e4231b5efb5b4354b24030b34e60
  languageName: node
  linkType: hard

"js-tiktoken@npm:^1.0.20":
  version: 1.0.20
  resolution: "js-tiktoken@npm:1.0.20"
  dependencies:
    base64-js: "npm:^1.5.1"
  checksum: 10c0/846c9257b25efa153695bb2a4a85f35e2e747fa76f03d04dcaffee5b64ce22ccd61db83af099492558375eabf18c8070369d0f88de07a9fcf4cb494bb9759dbd
  languageName: node
  linkType: hard

"js-tokens@npm:^4.0.0":
  version: 4.0.0
  resolution: "js-tokens@npm:4.0.0"
  checksum: 10c0/e248708d377aa058eacf2037b07ded847790e6de892bbad3dac0abba2e759cb9f121b00099a65195616badcb6eca8d14d975cb3e89eb1cfda644756402c8aeed
  languageName: node
  linkType: hard

"js-yaml@npm:^3.13.1":
  version: 3.14.1
  resolution: "js-yaml@npm:3.14.1"
  dependencies:
    argparse: "npm:^1.0.7"
    esprima: "npm:^4.0.0"
  bin:
    js-yaml: bin/js-yaml.js
  checksum: 10c0/6746baaaeac312c4db8e75fa22331d9a04cccb7792d126ed8ce6a0bbcfef0cedaddd0c5098fade53db067c09fe00aa1c957674b4765610a8b06a5a189e46433b
  languageName: node
  linkType: hard

"js-yaml@npm:^4.1.0":
  version: 4.1.0
  resolution: "js-yaml@npm:4.1.0"
  dependencies:
    argparse: "npm:^2.0.1"
  bin:
    js-yaml: bin/js-yaml.js
  checksum: 10c0/184a24b4eaacfce40ad9074c64fd42ac83cf74d8c8cd137718d456ced75051229e5061b8633c3366b8aada17945a7a356b337828c19da92b51ae62126575018f
  languageName: node
  linkType: hard

"jsbn@npm:1.1.0":
  version: 1.1.0
  resolution: "jsbn@npm:1.1.0"
  checksum: 10c0/4f907fb78d7b712e11dea8c165fe0921f81a657d3443dde75359ed52eb2b5d33ce6773d97985a089f09a65edd80b11cb75c767b57ba47391fee4c969f7215c96
  languageName: node
  linkType: hard

"jsesc@npm:^3.0.2":
  version: 3.1.0
  resolution: "jsesc@npm:3.1.0"
  bin:
    jsesc: bin/jsesc
  checksum: 10c0/531779df5ec94f47e462da26b4cbf05eb88a83d9f08aac2ba04206508fc598527a153d08bd462bae82fc78b3eaa1a908e1a4a79f886e9238641c4cdefaf118b1
  languageName: node
  linkType: hard

"json-bigint@npm:^1.0.0":
  version: 1.0.0
  resolution: "json-bigint@npm:1.0.0"
  dependencies:
    bignumber.js: "npm:^9.0.0"
  checksum: 10c0/e3f34e43be3284b573ea150a3890c92f06d54d8ded72894556357946aeed9877fd795f62f37fe16509af189fd314ab1104d0fd0f163746ad231b9f378f5b33f4
  languageName: node
  linkType: hard

"json-parse-even-better-errors@npm:^2.3.0":
  version: 2.3.1
  resolution: "json-parse-even-better-errors@npm:2.3.1"
  checksum: 10c0/140932564c8f0b88455432e0f33c4cb4086b8868e37524e07e723f4eaedb9425bdc2bafd71bd1d9765bd15fd1e2d126972bc83990f55c467168c228c24d665f3
  languageName: node
  linkType: hard

"json-schema-to-zod@npm:^2.6.1":
  version: 2.6.1
  resolution: "json-schema-to-zod@npm:2.6.1"
  bin:
    json-schema-to-zod: dist/cjs/cli.js
  checksum: 10c0/4edbb6de157b463407d935c53a5b0b24e2c547dc44495cb514e946e0022385a1a18db97b2714a140431e34f56c8090d373612812ccb7c45312368ca9a36ed341
  languageName: node
  linkType: hard

"json-schema-traverse@npm:^0.4.1":
  version: 0.4.1
  resolution: "json-schema-traverse@npm:0.4.1"
  checksum: 10c0/108fa90d4cc6f08243aedc6da16c408daf81793bf903e9fd5ab21983cda433d5d2da49e40711da016289465ec2e62e0324dcdfbc06275a607fe3233fde4942ce
  languageName: node
  linkType: hard

"json-schema-walker@npm:^2.0.0":
  version: 2.0.0
  resolution: "json-schema-walker@npm:2.0.0"
  dependencies:
    "@apidevtools/json-schema-ref-parser": "npm:^11.1.0"
    clone: "npm:^2.1.2"
  checksum: 10c0/be257826bbaa615349dbf6594826403938c50a9906e52407fcd8191b4f31cfcf22310efbd762382e8c9cbdcc6a75d4f3f9348c3bd6763a7b0eb4f70009b92f6d
  languageName: node
  linkType: hard

"json-schema@npm:^0.4.0":
  version: 0.4.0
  resolution: "json-schema@npm:0.4.0"
  checksum: 10c0/d4a637ec1d83544857c1c163232f3da46912e971d5bf054ba44fdb88f07d8d359a462b4aec46f2745efbc57053365608d88bc1d7b1729f7b4fc3369765639ed3
  languageName: node
  linkType: hard

"json5@npm:^2.2.3":
  version: 2.2.3
  resolution: "json5@npm:2.2.3"
  bin:
    json5: lib/cli.js
  checksum: 10c0/5a04eed94810fa55c5ea138b2f7a5c12b97c3750bc63d11e511dcecbfef758003861522a070c2272764ee0f4e3e323862f386945aeb5b85b87ee43f084ba586c
  languageName: node
  linkType: hard

"jsondiffpatch@npm:0.6.0":
  version: 0.6.0
  resolution: "jsondiffpatch@npm:0.6.0"
  dependencies:
    "@types/diff-match-patch": "npm:^1.0.36"
    chalk: "npm:^5.3.0"
    diff-match-patch: "npm:^1.0.5"
  bin:
    jsondiffpatch: bin/jsondiffpatch.js
  checksum: 10c0/f7822e48a8ef8b9f7c6024cc59b7d3707a9fe6d84fd776d169de5a1803ad551ffe7cfdc7587f3900f224bc70897355884ed43eb1c8ccd02e7f7b43a7ebcfed4f
  languageName: node
  linkType: hard

"jsonfile@npm:^6.0.1":
  version: 6.1.0
  resolution: "jsonfile@npm:6.1.0"
  dependencies:
    graceful-fs: "npm:^4.1.6"
    universalify: "npm:^2.0.0"
  dependenciesMeta:
    graceful-fs:
      optional: true
  checksum: 10c0/4f95b5e8a5622b1e9e8f33c96b7ef3158122f595998114d1e7f03985649ea99cb3cd99ce1ed1831ae94c8c8543ab45ebd044207612f31a56fd08462140e46865
  languageName: node
  linkType: hard

"kleur@npm:^3.0.3":
  version: 3.0.3
  resolution: "kleur@npm:3.0.3"
  checksum: 10c0/cd3a0b8878e7d6d3799e54340efe3591ca787d9f95f109f28129bdd2915e37807bf8918bb295ab86afb8c82196beec5a1adcaf29042ce3f2bd932b038fe3aa4b
  languageName: node
  linkType: hard

"langfuse-core@npm:^3.37.4":
  version: 3.37.4
  resolution: "langfuse-core@npm:3.37.4"
  dependencies:
    mustache: "npm:^4.2.0"
  checksum: 10c0/443f6c320499a958f32b0c5c94d6fd3995dc1a7772503c08cee940405c7a2d8d1347af0b9b4abe3b9b8df7fb899f27365c85c4919f99d888994afa838c3d8649
  languageName: node
  linkType: hard

"langfuse-vercel@npm:^3.37.2":
  version: 3.37.4
  resolution: "langfuse-vercel@npm:3.37.4"
  dependencies:
    langfuse: "npm:^3.37.4"
    langfuse-core: "npm:^3.37.4"
  peerDependencies:
    ai: ">=3.2.44"
  checksum: 10c0/b4d594352e5dddb6935934912deb63d112120bbadce2b88d52aa8401fea90e6e6e7117b8d026c771783866556bbb28e52fc9d5a6a738e2b7c208a954cc48d2d9
  languageName: node
  linkType: hard

"langfuse@npm:^3.37.4":
  version: 3.37.4
  resolution: "langfuse@npm:3.37.4"
  dependencies:
    langfuse-core: "npm:^3.37.4"
  checksum: 10c0/d63342d08a99ca813873a45e54e7203b018d5fb86bfb54ec7ebb7544c2d87e8cb98e4f143ad83dab447d13839807d0602a0a11c8982631686a45df05da12dc3e
  languageName: node
  linkType: hard

"leven@npm:^3.1.0":
  version: 3.1.0
  resolution: "leven@npm:3.1.0"
  checksum: 10c0/cd778ba3fbab0f4d0500b7e87d1f6e1f041507c56fdcd47e8256a3012c98aaee371d4c15e0a76e0386107af2d42e2b7466160a2d80688aaa03e66e49949f42df
  languageName: node
  linkType: hard

"lines-and-columns@npm:^1.1.6":
  version: 1.2.4
  resolution: "lines-and-columns@npm:1.2.4"
  checksum: 10c0/3da6ee62d4cd9f03f5dc90b4df2540fb85b352081bee77fe4bbcd12c9000ead7f35e0a38b8d09a9bb99b13223446dd8689ff3c4959807620726d788701a83d2d
  languageName: node
  linkType: hard

"locate-path@npm:^5.0.0":
  version: 5.0.0
  resolution: "locate-path@npm:5.0.0"
  dependencies:
    p-locate: "npm:^4.1.0"
  checksum: 10c0/33a1c5247e87e022f9713e6213a744557a3e9ec32c5d0b5efb10aa3a38177615bf90221a5592674857039c1a0fd2063b82f285702d37b792d973e9e72ace6c59
  languageName: node
  linkType: hard

"lodash.camelcase@npm:^4.3.0":
  version: 4.3.0
  resolution: "lodash.camelcase@npm:4.3.0"
  checksum: 10c0/fcba15d21a458076dd309fce6b1b4bf611d84a0ec252cb92447c948c533ac250b95d2e00955801ebc367e5af5ed288b996d75d37d2035260a937008e14eaf432
  languageName: node
  linkType: hard

"lodash.clonedeep@npm:^4.5.0":
  version: 4.5.0
  resolution: "lodash.clonedeep@npm:4.5.0"
  checksum: 10c0/2caf0e4808f319d761d2939ee0642fa6867a4bbf2cfce43276698828380756b99d4c4fa226d881655e6ac298dd453fe12a5ec8ba49861777759494c534936985
  languageName: node
  linkType: hard

"lodash.defaults@npm:^4.2.0":
  version: 4.2.0
  resolution: "lodash.defaults@npm:4.2.0"
  checksum: 10c0/d5b77aeb702caa69b17be1358faece33a84497bcca814897383c58b28a2f8dfc381b1d9edbec239f8b425126a3bbe4916223da2a576bb0411c2cefd67df80707
  languageName: node
  linkType: hard

"lodash.isarguments@npm:^3.1.0":
  version: 3.1.0
  resolution: "lodash.isarguments@npm:3.1.0"
  checksum: 10c0/5e8f95ba10975900a3920fb039a3f89a5a79359a1b5565e4e5b4310ed6ebe64011e31d402e34f577eca983a1fc01ff86c926e3cbe602e1ddfc858fdd353e62d8
  languageName: node
  linkType: hard

"lodash.memoize@npm:^4.1.2":
  version: 4.1.2
  resolution: "lodash.memoize@npm:4.1.2"
  checksum: 10c0/c8713e51eccc650422716a14cece1809cfe34bc5ab5e242b7f8b4e2241c2483697b971a604252807689b9dd69bfe3a98852e19a5b89d506b000b4187a1285df8
  languageName: node
  linkType: hard

"lodash.merge@npm:^4.6.2":
  version: 4.6.2
  resolution: "lodash.merge@npm:4.6.2"
  checksum: 10c0/402fa16a1edd7538de5b5903a90228aa48eb5533986ba7fa26606a49db2572bf414ff73a2c9f5d5fd36b31c46a5d5c7e1527749c07cbcf965ccff5fbdf32c506
  languageName: node
  linkType: hard

"lodash@npm:^4.17.11, lodash@npm:^4.17.14, lodash@npm:^4.17.21":
  version: 4.17.21
  resolution: "lodash@npm:4.17.21"
  checksum: 10c0/d8cbea072bb08655bb4c989da418994b073a608dffa608b09ac04b43a791b12aeae7cd7ad919aa4c925f33b48490b5cfe6c1f71d827956071dae2e7bb3a6b74c
  languageName: node
  linkType: hard

"long@npm:^5.0.0, long@npm:^5.2.4":
  version: 5.3.2
  resolution: "long@npm:5.3.2"
  checksum: 10c0/7130fe1cbce2dca06734b35b70d380ca3f70271c7f8852c922a7c62c86c4e35f0c39290565eca7133c625908d40e126ac57c02b1b1a4636b9457d77e1e60b981
  languageName: node
  linkType: hard

"lru-cache@npm:^10.0.1, lru-cache@npm:^10.2.0":
  version: 10.4.3
  resolution: "lru-cache@npm:10.4.3"
  checksum: 10c0/ebd04fbca961e6c1d6c0af3799adcc966a1babe798f685bb84e6599266599cd95d94630b10262f5424539bc4640107e8a33aa28585374abf561d30d16f4b39fb
  languageName: node
  linkType: hard

"lru-cache@npm:^5.1.1":
  version: 5.1.1
  resolution: "lru-cache@npm:5.1.1"
  dependencies:
    yallist: "npm:^3.0.2"
  checksum: 10c0/89b2ef2ef45f543011e38737b8a8622a2f8998cddf0e5437174ef8f1f70a8b9d14a918ab3e232cb3ba343b7abddffa667f0b59075b2b80e6b4d63c3de6127482
  languageName: node
  linkType: hard

"luxon@npm:^3.2.1":
  version: 3.6.1
  resolution: "luxon@npm:3.6.1"
  checksum: 10c0/906d57a9dc4d1de9383f2e9223e378c298607c1b4d17b6657b836a3cd120feb1c1de3b5d06d846a3417e1ca764de8476e8c23b3cd4083b5cdb870adcb06a99d5
  languageName: node
  linkType: hard

"magic-string@npm:^0.30.3":
  version: 0.30.17
  resolution: "magic-string@npm:0.30.17"
  dependencies:
    "@jridgewell/sourcemap-codec": "npm:^1.5.0"
  checksum: 10c0/16826e415d04b88378f200fe022b53e638e3838b9e496edda6c0e086d7753a44a6ed187adc72d19f3623810589bf139af1a315541cd6a26ae0771a0193eaf7b8
  languageName: node
  linkType: hard

"make-dir@npm:^4.0.0":
  version: 4.0.0
  resolution: "make-dir@npm:4.0.0"
  dependencies:
    semver: "npm:^7.5.3"
  checksum: 10c0/69b98a6c0b8e5c4fe9acb61608a9fbcfca1756d910f51e5dbe7a9e5cfb74fca9b8a0c8a0ffdf1294a740826c1ab4871d5bf3f62f72a3049e5eac6541ddffed68
  languageName: node
  linkType: hard

"make-error@npm:^1.1.1, make-error@npm:^1.3.6":
  version: 1.3.6
  resolution: "make-error@npm:1.3.6"
  checksum: 10c0/171e458d86854c6b3fc46610cfacf0b45149ba043782558c6875d9f42f222124384ad0b468c92e996d815a8a2003817a710c0a160e49c1c394626f76fa45396f
  languageName: node
  linkType: hard

"make-fetch-happen@npm:^14.0.3":
  version: 14.0.3
  resolution: "make-fetch-happen@npm:14.0.3"
  dependencies:
    "@npmcli/agent": "npm:^3.0.0"
    cacache: "npm:^19.0.1"
    http-cache-semantics: "npm:^4.1.1"
    minipass: "npm:^7.0.2"
    minipass-fetch: "npm:^4.0.0"
    minipass-flush: "npm:^1.0.5"
    minipass-pipeline: "npm:^1.2.4"
    negotiator: "npm:^1.0.0"
    proc-log: "npm:^5.0.0"
    promise-retry: "npm:^2.0.1"
    ssri: "npm:^12.0.0"
  checksum: 10c0/c40efb5e5296e7feb8e37155bde8eb70bc57d731b1f7d90e35a092fde403d7697c56fb49334d92d330d6f1ca29a98142036d6480a12681133a0a1453164cb2f0
  languageName: node
  linkType: hard

"makeerror@npm:1.0.12":
  version: 1.0.12
  resolution: "makeerror@npm:1.0.12"
  dependencies:
    tmpl: "npm:1.0.5"
  checksum: 10c0/b0e6e599780ce6bab49cc413eba822f7d1f0dfebd1c103eaa3785c59e43e22c59018323cf9e1708f0ef5329e94a745d163fcbb6bff8e4c6742f9be9e86f3500c
  languageName: node
  linkType: hard

"mastra@npm:^0.10.6":
  version: 0.10.6
  resolution: "mastra@npm:0.10.6"
  dependencies:
    "@clack/prompts": "npm:^0.11.0"
    "@lukeed/uuid": "npm:^2.0.1"
    "@mastra/deployer": "npm:^0.10.6"
    "@mastra/loggers": "npm:^0.10.2"
    "@mastra/mcp": "npm:^0.10.4"
    "@opentelemetry/instrumentation": "npm:^0.202.0"
    "@webcontainer/env": "npm:^1.1.1"
    commander: "npm:^12.1.0"
    dotenv: "npm:^16.5.0"
    execa: "npm:^9.6.0"
    fs-extra: "npm:^11.3.0"
    get-port: "npm:^7.1.0"
    json-schema-to-zod: "npm:^2.6.1"
    open: "npm:^10.1.2"
    picocolors: "npm:^1.1.1"
    posthog-node: "npm:4.18.0"
    prettier: "npm:^3.5.3"
    prompt: "npm:^1.3.0"
    shell-quote: "npm:^1.8.3"
    shiki: "npm:^1.29.2"
    strip-json-comments: "npm:^5.0.2"
    superjson: "npm:^2.2.2"
    swr: "npm:^2.3.3"
    tcp-port-used: "npm:^1.0.2"
    yocto-spinner: "npm:^0.2.3"
    zod: "npm:^3.25.57"
    zod-to-json-schema: "npm:^3.24.5"
  peerDependencies:
    "@mastra/core": ^0.10.2-alpha.0
  bin:
    mastra: dist/index.js
  checksum: 10c0/3a148d28b091b029f75c7a2723cdbad250bc6377a061654cf24ee66d82f568ccf28738992c43c511b88cf156df7cad5b411f54c4c116b21fcf177b4a4f3cc643
  languageName: node
  linkType: hard

"math-intrinsics@npm:^1.1.0":
  version: 1.1.0
  resolution: "math-intrinsics@npm:1.1.0"
  checksum: 10c0/7579ff94e899e2f76ab64491d76cf606274c874d8f2af4a442c016bd85688927fcfca157ba6bf74b08e9439dc010b248ce05b96cc7c126a354c3bae7fcb48b7f
  languageName: node
  linkType: hard

"mdast-util-to-hast@npm:^13.0.0":
  version: 13.2.0
  resolution: "mdast-util-to-hast@npm:13.2.0"
  dependencies:
    "@types/hast": "npm:^3.0.0"
    "@types/mdast": "npm:^4.0.0"
    "@ungap/structured-clone": "npm:^1.0.0"
    devlop: "npm:^1.0.0"
    micromark-util-sanitize-uri: "npm:^2.0.0"
    trim-lines: "npm:^3.0.0"
    unist-util-position: "npm:^5.0.0"
    unist-util-visit: "npm:^5.0.0"
    vfile: "npm:^6.0.0"
  checksum: 10c0/9ee58def9287df8350cbb6f83ced90f9c088d72d4153780ad37854f87144cadc6f27b20347073b285173b1649b0723ddf0b9c78158608a804dcacb6bda6e1816
  languageName: node
  linkType: hard

"media-typer@npm:0.3.0":
  version: 0.3.0
  resolution: "media-typer@npm:0.3.0"
  checksum: 10c0/d160f31246907e79fed398470285f21bafb45a62869dc469b1c8877f3f064f5eabc4bcc122f9479b8b605bc5c76187d7871cf84c4ee3ecd3e487da1993279928
  languageName: node
  linkType: hard

"media-typer@npm:^1.1.0":
  version: 1.1.0
  resolution: "media-typer@npm:1.1.0"
  checksum: 10c0/7b4baa40b25964bb90e2121ee489ec38642127e48d0cc2b6baa442688d3fde6262bfdca86d6bbf6ba708784afcac168c06840c71facac70e390f5f759ac121b9
  languageName: node
  linkType: hard

"merge-descriptors@npm:1.0.3":
  version: 1.0.3
  resolution: "merge-descriptors@npm:1.0.3"
  checksum: 10c0/866b7094afd9293b5ea5dcd82d71f80e51514bed33b4c4e9f516795dc366612a4cbb4dc94356e943a8a6914889a914530badff27f397191b9b75cda20b6bae93
  languageName: node
  linkType: hard

"merge-descriptors@npm:^2.0.0":
  version: 2.0.0
  resolution: "merge-descriptors@npm:2.0.0"
  checksum: 10c0/95389b7ced3f9b36fbdcf32eb946dc3dd1774c2fdf164609e55b18d03aa499b12bd3aae3a76c1c7185b96279e9803525550d3eb292b5224866060a288f335cb3
  languageName: node
  linkType: hard

"merge-stream@npm:^2.0.0":
  version: 2.0.0
  resolution: "merge-stream@npm:2.0.0"
  checksum: 10c0/867fdbb30a6d58b011449b8885601ec1690c3e41c759ecd5a9d609094f7aed0096c37823ff4a7190ef0b8f22cc86beb7049196ff68c016e3b3c671d0dac91ce5
  languageName: node
  linkType: hard

"merge2@npm:^1.3.0":
  version: 1.4.1
  resolution: "merge2@npm:1.4.1"
  checksum: 10c0/254a8a4605b58f450308fc474c82ac9a094848081bf4c06778200207820e5193726dc563a0d2c16468810516a5c97d9d3ea0ca6585d23c58ccfff2403e8dbbeb
  languageName: node
  linkType: hard

"methods@npm:^1.1.2, methods@npm:~1.1.2":
  version: 1.1.2
  resolution: "methods@npm:1.1.2"
  checksum: 10c0/bdf7cc72ff0a33e3eede03708c08983c4d7a173f91348b4b1e4f47d4cdbf734433ad971e7d1e8c77247d9e5cd8adb81ea4c67b0a2db526b758b2233d7814b8b2
  languageName: node
  linkType: hard

"micromark-util-character@npm:^2.0.0":
  version: 2.1.1
  resolution: "micromark-util-character@npm:2.1.1"
  dependencies:
    micromark-util-symbol: "npm:^2.0.0"
    micromark-util-types: "npm:^2.0.0"
  checksum: 10c0/d3fe7a5e2c4060fc2a076f9ce699c82a2e87190a3946e1e5eea77f563869b504961f5668d9c9c014724db28ac32fa909070ea8b30c3a39bd0483cc6c04cc76a1
  languageName: node
  linkType: hard

"micromark-util-encode@npm:^2.0.0":
  version: 2.0.1
  resolution: "micromark-util-encode@npm:2.0.1"
  checksum: 10c0/b2b29f901093845da8a1bf997ea8b7f5e061ffdba85070dfe14b0197c48fda64ffcf82bfe53c90cf9dc185e69eef8c5d41cae3ba918b96bc279326921b59008a
  languageName: node
  linkType: hard

"micromark-util-sanitize-uri@npm:^2.0.0":
  version: 2.0.1
  resolution: "micromark-util-sanitize-uri@npm:2.0.1"
  dependencies:
    micromark-util-character: "npm:^2.0.0"
    micromark-util-encode: "npm:^2.0.0"
    micromark-util-symbol: "npm:^2.0.0"
  checksum: 10c0/60e92166e1870fd4f1961468c2651013ff760617342918e0e0c3c4e872433aa2e60c1e5a672bfe5d89dc98f742d6b33897585cf86ae002cda23e905a3c02527c
  languageName: node
  linkType: hard

"micromark-util-symbol@npm:^2.0.0":
  version: 2.0.1
  resolution: "micromark-util-symbol@npm:2.0.1"
  checksum: 10c0/f2d1b207771e573232436618e78c5e46cd4b5c560dd4a6d63863d58018abbf49cb96ec69f7007471e51434c60de3c9268ef2bf46852f26ff4aacd10f9da16fe9
  languageName: node
  linkType: hard

"micromark-util-types@npm:^2.0.0":
  version: 2.0.2
  resolution: "micromark-util-types@npm:2.0.2"
  checksum: 10c0/c8c15b96c858db781c4393f55feec10004bf7df95487636c9a9f7209e51002a5cca6a047c5d2a5dc669ff92da20e57aaa881e81a268d9ccadb647f9dce305298
  languageName: node
  linkType: hard

"micromatch@npm:^4.0.4, micromatch@npm:^4.0.8":
  version: 4.0.8
  resolution: "micromatch@npm:4.0.8"
  dependencies:
    braces: "npm:^3.0.3"
    picomatch: "npm:^2.3.1"
  checksum: 10c0/166fa6eb926b9553f32ef81f5f531d27b4ce7da60e5baf8c021d043b27a388fb95e46a8038d5045877881e673f8134122b59624d5cecbd16eb50a42e7a6b5ca8
  languageName: node
  linkType: hard

"mime-db@npm:1.52.0":
  version: 1.52.0
  resolution: "mime-db@npm:1.52.0"
  checksum: 10c0/0557a01deebf45ac5f5777fe7740b2a5c309c6d62d40ceab4e23da9f821899ce7a900b7ac8157d4548ddbb7beffe9abc621250e6d182b0397ec7f10c7b91a5aa
  languageName: node
  linkType: hard

"mime-db@npm:^1.54.0":
  version: 1.54.0
  resolution: "mime-db@npm:1.54.0"
  checksum: 10c0/8d907917bc2a90fa2df842cdf5dfeaf509adc15fe0531e07bb2f6ab15992416479015828d6a74200041c492e42cce3ebf78e5ce714388a0a538ea9c53eece284
  languageName: node
  linkType: hard

"mime-types@npm:^2.1.12, mime-types@npm:~2.1.24, mime-types@npm:~2.1.34":
  version: 2.1.35
  resolution: "mime-types@npm:2.1.35"
  dependencies:
    mime-db: "npm:1.52.0"
  checksum: 10c0/82fb07ec56d8ff1fc999a84f2f217aa46cb6ed1033fefaabd5785b9a974ed225c90dc72fff460259e66b95b73648596dbcc50d51ed69cdf464af2d237d3149b2
  languageName: node
  linkType: hard

"mime-types@npm:^3.0.0, mime-types@npm:^3.0.1":
  version: 3.0.1
  resolution: "mime-types@npm:3.0.1"
  dependencies:
    mime-db: "npm:^1.54.0"
  checksum: 10c0/bd8c20d3694548089cf229016124f8f40e6a60bbb600161ae13e45f793a2d5bb40f96bbc61f275836696179c77c1d6bf4967b2a75e0a8ad40fe31f4ed5be4da5
  languageName: node
  linkType: hard

"mime@npm:1.6.0":
  version: 1.6.0
  resolution: "mime@npm:1.6.0"
  bin:
    mime: cli.js
  checksum: 10c0/b92cd0adc44888c7135a185bfd0dddc42c32606401c72896a842ae15da71eb88858f17669af41e498b463cd7eb998f7b48939a25b08374c7924a9c8a6f8a81b0
  languageName: node
  linkType: hard

"mime@npm:2.6.0":
  version: 2.6.0
  resolution: "mime@npm:2.6.0"
  bin:
    mime: cli.js
  checksum: 10c0/a7f2589900d9c16e3bdf7672d16a6274df903da958c1643c9c45771f0478f3846dcb1097f31eb9178452570271361e2149310931ec705c037210fc69639c8e6c
  languageName: node
  linkType: hard

"mimic-fn@npm:^2.1.0":
  version: 2.1.0
  resolution: "mimic-fn@npm:2.1.0"
  checksum: 10c0/b26f5479d7ec6cc2bce275a08f146cf78f5e7b661b18114e2506dd91ec7ec47e7a25bf4360e5438094db0560bcc868079fb3b1fb3892b833c1ecbf63f80c95a4
  languageName: node
  linkType: hard

"mimic-fn@npm:^4.0.0":
  version: 4.0.0
  resolution: "mimic-fn@npm:4.0.0"
  checksum: 10c0/de9cc32be9996fd941e512248338e43407f63f6d497abe8441fa33447d922e927de54d4cc3c1a3c6d652857acd770389d5a3823f311a744132760ce2be15ccbf
  languageName: node
  linkType: hard

"minimal-polyfills@npm:^2.2.3":
  version: 2.2.3
  resolution: "minimal-polyfills@npm:2.2.3"
  checksum: 10c0/de8668b7441afc7b691028a9488744e6ead371dbfffdd29b9dd30764984aff4ad95f54c5ceabe8044f6a639da6be2f4047ead3ee8734536701029c7652e87ee5
  languageName: node
  linkType: hard

"minimatch@npm:^3.0.4, minimatch@npm:^3.1.1, minimatch@npm:^3.1.2":
  version: 3.1.2
  resolution: "minimatch@npm:3.1.2"
  dependencies:
    brace-expansion: "npm:^1.1.7"
  checksum: 10c0/0262810a8fc2e72cca45d6fd86bd349eee435eb95ac6aa45c9ea2180e7ee875ef44c32b55b5973ceabe95ea12682f6e3725cbb63d7a2d1da3ae1163c8b210311
  languageName: node
  linkType: hard

"minimatch@npm:^5.0.1":
  version: 5.1.6
  resolution: "minimatch@npm:5.1.6"
  dependencies:
    brace-expansion: "npm:^2.0.1"
  checksum: 10c0/3defdfd230914f22a8da203747c42ee3c405c39d4d37ffda284dac5e45b7e1f6c49aa8be606509002898e73091ff2a3bbfc59c2c6c71d4660609f63aa92f98e3
  languageName: node
  linkType: hard

"minimatch@npm:^9.0.4":
  version: 9.0.5
  resolution: "minimatch@npm:9.0.5"
  dependencies:
    brace-expansion: "npm:^2.0.1"
  checksum: 10c0/de96cf5e35bdf0eab3e2c853522f98ffbe9a36c37797778d2665231ec1f20a9447a7e567cb640901f89e4daaa95ae5d70c65a9e8aa2bb0019b6facbc3c0575ed
  languageName: node
  linkType: hard

"minimist@npm:^1.2.6":
  version: 1.2.8
  resolution: "minimist@npm:1.2.8"
  checksum: 10c0/19d3fcdca050087b84c2029841a093691a91259a47def2f18222f41e7645a0b7c44ef4b40e88a1e58a40c84d2ef0ee6047c55594d298146d0eb3f6b737c20ce6
  languageName: node
  linkType: hard

"minipass-collect@npm:^2.0.1":
  version: 2.0.1
  resolution: "minipass-collect@npm:2.0.1"
  dependencies:
    minipass: "npm:^7.0.3"
  checksum: 10c0/5167e73f62bb74cc5019594709c77e6a742051a647fe9499abf03c71dca75515b7959d67a764bdc4f8b361cf897fbf25e2d9869ee039203ed45240f48b9aa06e
  languageName: node
  linkType: hard

"minipass-fetch@npm:^4.0.0":
  version: 4.0.1
  resolution: "minipass-fetch@npm:4.0.1"
  dependencies:
    encoding: "npm:^0.1.13"
    minipass: "npm:^7.0.3"
    minipass-sized: "npm:^1.0.3"
    minizlib: "npm:^3.0.1"
  dependenciesMeta:
    encoding:
      optional: true
  checksum: 10c0/a3147b2efe8e078c9bf9d024a0059339c5a09c5b1dded6900a219c218cc8b1b78510b62dae556b507304af226b18c3f1aeb1d48660283602d5b6586c399eed5c
  languageName: node
  linkType: hard

"minipass-flush@npm:^1.0.5":
  version: 1.0.5
  resolution: "minipass-flush@npm:1.0.5"
  dependencies:
    minipass: "npm:^3.0.0"
  checksum: 10c0/2a51b63feb799d2bb34669205eee7c0eaf9dce01883261a5b77410c9408aa447e478efd191b4de6fc1101e796ff5892f8443ef20d9544385819093dbb32d36bd
  languageName: node
  linkType: hard

"minipass-pipeline@npm:^1.2.4":
  version: 1.2.4
  resolution: "minipass-pipeline@npm:1.2.4"
  dependencies:
    minipass: "npm:^3.0.0"
  checksum: 10c0/cbda57cea20b140b797505dc2cac71581a70b3247b84480c1fed5ca5ba46c25ecc25f68bfc9e6dcb1a6e9017dab5c7ada5eab73ad4f0a49d84e35093e0c643f2
  languageName: node
  linkType: hard

"minipass-sized@npm:^1.0.3":
  version: 1.0.3
  resolution: "minipass-sized@npm:1.0.3"
  dependencies:
    minipass: "npm:^3.0.0"
  checksum: 10c0/298f124753efdc745cfe0f2bdfdd81ba25b9f4e753ca4a2066eb17c821f25d48acea607dfc997633ee5bf7b6dfffb4eee4f2051eb168663f0b99fad2fa4829cb
  languageName: node
  linkType: hard

"minipass@npm:^3.0.0":
  version: 3.3.6
  resolution: "minipass@npm:3.3.6"
  dependencies:
    yallist: "npm:^4.0.0"
  checksum: 10c0/a114746943afa1dbbca8249e706d1d38b85ed1298b530f5808ce51f8e9e941962e2a5ad2e00eae7dd21d8a4aae6586a66d4216d1a259385e9d0358f0c1eba16c
  languageName: node
  linkType: hard

"minipass@npm:^5.0.0 || ^6.0.2 || ^7.0.0, minipass@npm:^7.0.2, minipass@npm:^7.0.3, minipass@npm:^7.0.4, minipass@npm:^7.1.2":
  version: 7.1.2
  resolution: "minipass@npm:7.1.2"
  checksum: 10c0/b0fd20bb9fb56e5fa9a8bfac539e8915ae07430a619e4b86ff71f5fc757ef3924b23b2c4230393af1eda647ed3d75739e4e0acb250a6b1eb277cf7f8fe449557
  languageName: node
  linkType: hard

"minizlib@npm:^3.0.1":
  version: 3.0.2
  resolution: "minizlib@npm:3.0.2"
  dependencies:
    minipass: "npm:^7.1.2"
  checksum: 10c0/9f3bd35e41d40d02469cb30470c55ccc21cae0db40e08d1d0b1dff01cc8cc89a6f78e9c5d2b7c844e485ec0a8abc2238111213fdc5b2038e6d1012eacf316f78
  languageName: node
  linkType: hard

"mkdirp@npm:^0.5.6":
  version: 0.5.6
  resolution: "mkdirp@npm:0.5.6"
  dependencies:
    minimist: "npm:^1.2.6"
  bin:
    mkdirp: bin/cmd.js
  checksum: 10c0/e2e2be789218807b58abced04e7b49851d9e46e88a2f9539242cc8a92c9b5c3a0b9bab360bd3014e02a140fc4fbc58e31176c408b493f8a2a6f4986bd7527b01
  languageName: node
  linkType: hard

"mkdirp@npm:^3.0.1":
  version: 3.0.1
  resolution: "mkdirp@npm:3.0.1"
  bin:
    mkdirp: dist/cjs/src/bin.js
  checksum: 10c0/9f2b975e9246351f5e3a40dcfac99fcd0baa31fbfab615fe059fb11e51f10e4803c63de1f384c54d656e4db31d000e4767e9ef076a22e12a641357602e31d57d
  languageName: node
  linkType: hard

"mlly@npm:^1.7.4":
  version: 1.7.4
  resolution: "mlly@npm:1.7.4"
  dependencies:
    acorn: "npm:^8.14.0"
    pathe: "npm:^2.0.1"
    pkg-types: "npm:^1.3.0"
    ufo: "npm:^1.5.4"
  checksum: 10c0/69e738218a13d6365caf930e0ab4e2b848b84eec261597df9788cefb9930f3e40667be9cb58a4718834ba5f97a6efeef31d3b5a95f4388143fd4e0d0deff72ff
  languageName: node
  linkType: hard

"module-details-from-path@npm:^1.0.3":
  version: 1.0.4
  resolution: "module-details-from-path@npm:1.0.4"
  checksum: 10c0/10863413e96dab07dee917eae07afe46f7bf853065cc75a7d2a718adf67574857fb64f8a2c0c9af12ac733a9a8cf652db7ed39b95f7a355d08106cb9cc50c83b
  languageName: node
  linkType: hard

"ms@npm:2.0.0":
  version: 2.0.0
  resolution: "ms@npm:2.0.0"
  checksum: 10c0/f8fda810b39fd7255bbdc451c46286e549794fcc700dc9cd1d25658bbc4dc2563a5de6fe7c60f798a16a60c6ceb53f033cb353f493f0cf63e5199b702943159d
  languageName: node
  linkType: hard

"ms@npm:2.1.2":
  version: 2.1.2
  resolution: "ms@npm:2.1.2"
  checksum: 10c0/a437714e2f90dbf881b5191d35a6db792efbca5badf112f87b9e1c712aace4b4b9b742dd6537f3edf90fd6f684de897cec230abde57e87883766712ddda297cc
  languageName: node
  linkType: hard

"ms@npm:2.1.3, ms@npm:^2.1.3":
  version: 2.1.3
  resolution: "ms@npm:2.1.3"
  checksum: 10c0/d924b57e7312b3b63ad21fc5b3dc0af5e78d61a1fc7cfb5457edaf26326bf62be5307cc87ffb6862ef1c2b33b0233cdb5d4f01c4c958cc0d660948b65a287a48
  languageName: node
  linkType: hard

"msgpackr-extract@npm:^3.0.2":
  version: 3.0.3
  resolution: "msgpackr-extract@npm:3.0.3"
  dependencies:
    "@msgpackr-extract/msgpackr-extract-darwin-arm64": "npm:3.0.3"
    "@msgpackr-extract/msgpackr-extract-darwin-x64": "npm:3.0.3"
    "@msgpackr-extract/msgpackr-extract-linux-arm": "npm:3.0.3"
    "@msgpackr-extract/msgpackr-extract-linux-arm64": "npm:3.0.3"
    "@msgpackr-extract/msgpackr-extract-linux-x64": "npm:3.0.3"
    "@msgpackr-extract/msgpackr-extract-win32-x64": "npm:3.0.3"
    node-gyp: "npm:latest"
    node-gyp-build-optional-packages: "npm:5.2.2"
  dependenciesMeta:
    "@msgpackr-extract/msgpackr-extract-darwin-arm64":
      optional: true
    "@msgpackr-extract/msgpackr-extract-darwin-x64":
      optional: true
    "@msgpackr-extract/msgpackr-extract-linux-arm":
      optional: true
    "@msgpackr-extract/msgpackr-extract-linux-arm64":
      optional: true
    "@msgpackr-extract/msgpackr-extract-linux-x64":
      optional: true
    "@msgpackr-extract/msgpackr-extract-win32-x64":
      optional: true
  bin:
    download-msgpackr-prebuilds: bin/download-prebuilds.js
  checksum: 10c0/e504fd8bf86a29d7527c83776530ee6dc92dcb0273bb3679fd4a85173efead7f0ee32fb82c8410a13c33ef32828c45f81118ffc0fbed5d6842e72299894623b4
  languageName: node
  linkType: hard

"msgpackr@npm:^1.11.2":
  version: 1.11.4
  resolution: "msgpackr@npm:1.11.4"
  dependencies:
    msgpackr-extract: "npm:^3.0.2"
  dependenciesMeta:
    msgpackr-extract:
      optional: true
  checksum: 10c0/171f6e15b628e91969cbb715c076e218886dc505fdac9ce31aa9e8641877cb5cf52d89fe0ca2930520711b1bbc9f792e10d0a9fc08806ad5d543c50abfab322c
  languageName: node
  linkType: hard

"multer@npm:^2.0.0":
  version: 2.0.1
  resolution: "multer@npm:2.0.1"
  dependencies:
    append-field: "npm:^1.0.0"
    busboy: "npm:^1.6.0"
    concat-stream: "npm:^2.0.0"
    mkdirp: "npm:^0.5.6"
    object-assign: "npm:^4.1.1"
    type-is: "npm:^1.6.18"
    xtend: "npm:^4.0.2"
  checksum: 10c0/2b5ab16a2bc6070690cff1f30589bb0d1218ed62051d65fdb1a8d9c65c63238c07af81ae8921de449f921ff10c849f3f6830fd07ef5640c46aaaca5c94044d25
  languageName: node
  linkType: hard

"mustache@npm:^4.2.0":
  version: 4.2.0
  resolution: "mustache@npm:4.2.0"
  bin:
    mustache: bin/mustache
  checksum: 10c0/1f8197e8a19e63645a786581d58c41df7853da26702dbc005193e2437c98ca49b255345c173d50c08fe4b4dbb363e53cb655ecc570791f8deb09887248dd34a2
  languageName: node
  linkType: hard

"mute-stream@npm:~0.0.4":
  version: 0.0.8
  resolution: "mute-stream@npm:0.0.8"
  checksum: 10c0/18d06d92e5d6d45e2b63c0e1b8f25376af71748ac36f53c059baa8b76ffac31c5ab225480494e7d35d30215ecdb18fed26ec23cafcd2f7733f2f14406bcd19e2
  languageName: node
  linkType: hard

"nanoid@npm:^3.3.4, nanoid@npm:^3.3.8":
  version: 3.3.11
  resolution: "nanoid@npm:3.3.11"
  bin:
    nanoid: bin/nanoid.cjs
  checksum: 10c0/40e7f70b3d15f725ca072dfc4f74e81fcf1fbb02e491cf58ac0c79093adc9b0a73b152bcde57df4b79cd097e13023d7504acb38404a4da7bc1cd8e887b82fe0b
  languageName: node
  linkType: hard

"natural-compare@npm:^1.4.0":
  version: 1.4.0
  resolution: "natural-compare@npm:1.4.0"
  checksum: 10c0/f5f9a7974bfb28a91afafa254b197f0f22c684d4a1731763dda960d2c8e375b36c7d690e0d9dc8fba774c537af14a7e979129bca23d88d052fbeb9466955e447
  languageName: node
  linkType: hard

"negotiator@npm:0.6.3":
  version: 0.6.3
  resolution: "negotiator@npm:0.6.3"
  checksum: 10c0/3ec9fd413e7bf071c937ae60d572bc67155262068ed522cf4b3be5edbe6ddf67d095ec03a3a14ebf8fc8e95f8e1d61be4869db0dbb0de696f6b837358bd43fc2
  languageName: node
  linkType: hard

"negotiator@npm:^1.0.0":
  version: 1.0.0
  resolution: "negotiator@npm:1.0.0"
  checksum: 10c0/4c559dd52669ea48e1914f9d634227c561221dd54734070791f999c52ed0ff36e437b2e07d5c1f6e32909fc625fe46491c16e4a8f0572567d4dd15c3a4fda04b
  languageName: node
  linkType: hard

"nice-grpc-client-middleware-retry@npm:^3.1.10":
  version: 3.1.11
  resolution: "nice-grpc-client-middleware-retry@npm:3.1.11"
  dependencies:
    abort-controller-x: "npm:^0.4.0"
    nice-grpc-common: "npm:^2.0.2"
  checksum: 10c0/0d9c704a1a5c399f8243753c75a7db86c9eb414ca5bae1920cd66f60ea235190c5ea667daa1c161345ae4bf86817085f3add4438ca67f9144c5e57b9542ef5c5
  languageName: node
  linkType: hard

"nice-grpc-common@npm:^2.0.2":
  version: 2.0.2
  resolution: "nice-grpc-common@npm:2.0.2"
  dependencies:
    ts-error: "npm:^1.0.6"
  checksum: 10c0/9eb8a44e1a5c7051cf0e4a06dc7fda2c7abb6cfbcbb746806418c2c58f3f0075212c61bbce54239a204e6a552065f0fa92dfedcf3402dc16220b2ffaee4ab857
  languageName: node
  linkType: hard

"nice-grpc@npm:^2.1.11":
  version: 2.1.12
  resolution: "nice-grpc@npm:2.1.12"
  dependencies:
    "@grpc/grpc-js": "npm:^1.13.1"
    abort-controller-x: "npm:^0.4.0"
    nice-grpc-common: "npm:^2.0.2"
  checksum: 10c0/7a8e720a42a0297315bfafa0c93297e36d341927eaddae9e5a06c8ea2863b16d701a642dc9610e3e768d19cc9569afe5b99de2dfaeb1648042d32a139a7ba773
  languageName: node
  linkType: hard

"node-abort-controller@npm:^3.1.1":
  version: 3.1.1
  resolution: "node-abort-controller@npm:3.1.1"
  checksum: 10c0/f7ad0e7a8e33809d4f3a0d1d65036a711c39e9d23e0319d80ebe076b9a3b4432b4d6b86a7fab65521de3f6872ffed36fc35d1327487c48eb88c517803403eda3
  languageName: node
  linkType: hard

"node-fetch@npm:2.7.0, node-fetch@npm:^2.6.9, node-fetch@npm:^2.7.0":
  version: 2.7.0
  resolution: "node-fetch@npm:2.7.0"
  dependencies:
    whatwg-url: "npm:^5.0.0"
  peerDependencies:
    encoding: ^0.1.0
  peerDependenciesMeta:
    encoding:
      optional: true
  checksum: 10c0/b55786b6028208e6fbe594ccccc213cab67a72899c9234eb59dba51062a299ea853210fcf526998eaa2867b0963ad72338824450905679ff0fa304b8c5093ae8
  languageName: node
  linkType: hard

"node-gyp-build-optional-packages@npm:5.2.2":
  version: 5.2.2
  resolution: "node-gyp-build-optional-packages@npm:5.2.2"
  dependencies:
    detect-libc: "npm:^2.0.1"
  bin:
    node-gyp-build-optional-packages: bin.js
    node-gyp-build-optional-packages-optional: optional.js
    node-gyp-build-optional-packages-test: build-test.js
  checksum: 10c0/c81128c6f91873381be178c5eddcbdf66a148a6a89a427ce2bcd457593ce69baf2a8662b6d22cac092d24aa9c43c230dec4e69b3a0da604503f4777cd77e282b
  languageName: node
  linkType: hard

"node-gyp@npm:latest":
  version: 11.2.0
  resolution: "node-gyp@npm:11.2.0"
  dependencies:
    env-paths: "npm:^2.2.0"
    exponential-backoff: "npm:^3.1.1"
    graceful-fs: "npm:^4.2.6"
    make-fetch-happen: "npm:^14.0.3"
    nopt: "npm:^8.0.0"
    proc-log: "npm:^5.0.0"
    semver: "npm:^7.3.5"
    tar: "npm:^7.4.3"
    tinyglobby: "npm:^0.2.12"
    which: "npm:^5.0.0"
  bin:
    node-gyp: bin/node-gyp.js
  checksum: 10c0/bd8d8c76b06be761239b0c8680f655f6a6e90b48e44d43415b11c16f7e8c15be346fba0cbf71588c7cdfb52c419d928a7d3db353afc1d952d19756237d8f10b9
  languageName: node
  linkType: hard

"node-int64@npm:^0.4.0":
  version: 0.4.0
  resolution: "node-int64@npm:0.4.0"
  checksum: 10c0/a6a4d8369e2f2720e9c645255ffde909c0fbd41c92ea92a5607fc17055955daac99c1ff589d421eee12a0d24e99f7bfc2aabfeb1a4c14742f6c099a51863f31a
  languageName: node
  linkType: hard

"node-releases@npm:^2.0.19":
  version: 2.0.19
  resolution: "node-releases@npm:2.0.19"
  checksum: 10c0/52a0dbd25ccf545892670d1551690fe0facb6a471e15f2cfa1b20142a5b255b3aa254af5f59d6ecb69c2bec7390bc643c43aa63b13bf5e64b6075952e716b1aa
  languageName: node
  linkType: hard

"nodemon@npm:^3.1.10":
  version: 3.1.10
  resolution: "nodemon@npm:3.1.10"
  dependencies:
    chokidar: "npm:^3.5.2"
    debug: "npm:^4"
    ignore-by-default: "npm:^1.0.1"
    minimatch: "npm:^3.1.2"
    pstree.remy: "npm:^1.1.8"
    semver: "npm:^7.5.3"
    simple-update-notifier: "npm:^2.0.0"
    supports-color: "npm:^5.5.0"
    touch: "npm:^3.1.0"
    undefsafe: "npm:^2.0.5"
  bin:
    nodemon: bin/nodemon.js
  checksum: 10c0/95b64d647f2c22e85e375b250517b0a4b32c2d2392ad898444e331f70d6b1ab43b17f53a8a1d68d5879ab8401fc6cd6e26f0d2a8736240984f6b5a8435b407c0
  languageName: node
  linkType: hard

"nopt@npm:^8.0.0":
  version: 8.1.0
  resolution: "nopt@npm:8.1.0"
  dependencies:
    abbrev: "npm:^3.0.0"
  bin:
    nopt: bin/nopt.js
  checksum: 10c0/62e9ea70c7a3eb91d162d2c706b6606c041e4e7b547cbbb48f8b3695af457dd6479904d7ace600856bf923dd8d1ed0696f06195c8c20f02ac87c1da0e1d315ef
  languageName: node
  linkType: hard

"normalize-path@npm:^3.0.0, normalize-path@npm:~3.0.0":
  version: 3.0.0
  resolution: "normalize-path@npm:3.0.0"
  checksum: 10c0/e008c8142bcc335b5e38cf0d63cfd39d6cf2d97480af9abdbe9a439221fd4d749763bab492a8ee708ce7a194bb00c9da6d0a115018672310850489137b3da046
  languageName: node
  linkType: hard

"npm-run-path@npm:^4.0.1":
  version: 4.0.1
  resolution: "npm-run-path@npm:4.0.1"
  dependencies:
    path-key: "npm:^3.0.0"
  checksum: 10c0/6f9353a95288f8455cf64cbeb707b28826a7f29690244c1e4bb61ec573256e021b6ad6651b394eb1ccfd00d6ec50147253aba2c5fe58a57ceb111fad62c519ac
  languageName: node
  linkType: hard

"npm-run-path@npm:^5.1.0":
  version: 5.3.0
  resolution: "npm-run-path@npm:5.3.0"
  dependencies:
    path-key: "npm:^4.0.0"
  checksum: 10c0/124df74820c40c2eb9a8612a254ea1d557ddfab1581c3e751f825e3e366d9f00b0d76a3c94ecd8398e7f3eee193018622677e95816e8491f0797b21e30b2deba
  languageName: node
  linkType: hard

"npm-run-path@npm:^6.0.0":
  version: 6.0.0
  resolution: "npm-run-path@npm:6.0.0"
  dependencies:
    path-key: "npm:^4.0.0"
    unicorn-magic: "npm:^0.3.0"
  checksum: 10c0/b223c8a0dcd608abf95363ea5c3c0ccc3cd877daf0102eaf1b0f2390d6858d8337fbb7c443af2403b067a7d2c116d10691ecd22ab3c5273c44da1ff8d07753bd
  languageName: node
  linkType: hard

"object-assign@npm:^4, object-assign@npm:^4.1.1":
  version: 4.1.1
  resolution: "object-assign@npm:4.1.1"
  checksum: 10c0/1f4df9945120325d041ccf7b86f31e8bcc14e73d29171e37a7903050e96b81323784ec59f93f102ec635bcf6fa8034ba3ea0a8c7e69fa202b87ae3b6cec5a414
  languageName: node
  linkType: hard

"object-inspect@npm:^1.13.3":
  version: 1.13.4
  resolution: "object-inspect@npm:1.13.4"
  checksum: 10c0/d7f8711e803b96ea3191c745d6f8056ce1f2496e530e6a19a0e92d89b0fa3c76d910c31f0aa270432db6bd3b2f85500a376a83aaba849a8d518c8845b3211692
  languageName: node
  linkType: hard

"on-exit-leak-free@npm:^2.1.0":
  version: 2.1.2
  resolution: "on-exit-leak-free@npm:2.1.2"
  checksum: 10c0/faea2e1c9d696ecee919026c32be8d6a633a7ac1240b3b87e944a380e8a11dc9c95c4a1f8fb0568de7ab8db3823e790f12bda45296b1d111e341aad3922a0570
  languageName: node
  linkType: hard

"on-finished@npm:2.4.1, on-finished@npm:^2.4.1":
  version: 2.4.1
  resolution: "on-finished@npm:2.4.1"
  dependencies:
    ee-first: "npm:1.1.1"
  checksum: 10c0/46fb11b9063782f2d9968863d9cbba33d77aa13c17f895f56129c274318b86500b22af3a160fe9995aa41317efcd22941b6eba747f718ced08d9a73afdb087b4
  languageName: node
  linkType: hard

"once@npm:^1.3.0, once@npm:^1.3.1, once@npm:^1.4.0":
  version: 1.4.0
  resolution: "once@npm:1.4.0"
  dependencies:
    wrappy: "npm:1"
  checksum: 10c0/5d48aca287dfefabd756621c5dfce5c91a549a93e9fdb7b8246bc4c4790aa2ec17b34a260530474635147aeb631a2dcc8b32c613df0675f96041cbb8244517d0
  languageName: node
  linkType: hard

"onetime@npm:^5.1.2":
  version: 5.1.2
  resolution: "onetime@npm:5.1.2"
  dependencies:
    mimic-fn: "npm:^2.1.0"
  checksum: 10c0/ffcef6fbb2692c3c40749f31ea2e22677a876daea92959b8a80b521d95cca7a668c884d8b2045d1d8ee7d56796aa405c405462af112a1477594cc63531baeb8f
  languageName: node
  linkType: hard

"onetime@npm:^6.0.0":
  version: 6.0.0
  resolution: "onetime@npm:6.0.0"
  dependencies:
    mimic-fn: "npm:^4.0.0"
  checksum: 10c0/4eef7c6abfef697dd4479345a4100c382d73c149d2d56170a54a07418c50816937ad09500e1ed1e79d235989d073a9bade8557122aee24f0576ecde0f392bb6c
  languageName: node
  linkType: hard

"oniguruma-to-es@npm:^2.2.0":
  version: 2.3.0
  resolution: "oniguruma-to-es@npm:2.3.0"
  dependencies:
    emoji-regex-xs: "npm:^1.0.0"
    regex: "npm:^5.1.1"
    regex-recursion: "npm:^5.1.1"
  checksum: 10c0/57ad95f3e9a50be75e7d54e582d8d4da4003f983fd04d99ccc9d17d2dc04e30ea64126782f2e758566bcef2c4c55db0d6a3d344f35ca179dd92ea5ca92fc0313
  languageName: node
  linkType: hard

"open@npm:^10.1.2":
  version: 10.1.2
  resolution: "open@npm:10.1.2"
  dependencies:
    default-browser: "npm:^5.2.1"
    define-lazy-prop: "npm:^3.0.0"
    is-inside-container: "npm:^1.0.0"
    is-wsl: "npm:^3.1.0"
  checksum: 10c0/1bee796f06e549ce764f693272100323fbc04da8fa3c5b0402d6c2d11b3d76fa0aac0be7535e710015ff035326638e3b9a563f3b0e7ac3266473ed5663caae6d
  languageName: node
  linkType: hard

"p-limit@npm:^2.2.0":
  version: 2.3.0
  resolution: "p-limit@npm:2.3.0"
  dependencies:
    p-try: "npm:^2.0.0"
  checksum: 10c0/8da01ac53efe6a627080fafc127c873da40c18d87b3f5d5492d465bb85ec7207e153948df6b9cbaeb130be70152f874229b8242ee2be84c0794082510af97f12
  languageName: node
  linkType: hard

"p-limit@npm:^3.1.0":
  version: 3.1.0
  resolution: "p-limit@npm:3.1.0"
  dependencies:
    yocto-queue: "npm:^0.1.0"
  checksum: 10c0/9db675949dbdc9c3763c89e748d0ef8bdad0afbb24d49ceaf4c46c02c77d30db4e0652ed36d0a0a7a95154335fab810d95c86153105bb73b3a90448e2bb14e1a
  languageName: node
  linkType: hard

"p-locate@npm:^4.1.0":
  version: 4.1.0
  resolution: "p-locate@npm:4.1.0"
  dependencies:
    p-limit: "npm:^2.2.0"
  checksum: 10c0/1b476ad69ad7f6059744f343b26d51ce091508935c1dbb80c4e0a2f397ffce0ca3a1f9f5cd3c7ce19d7929a09719d5c65fe70d8ee289c3f267cd36f2881813e9
  languageName: node
  linkType: hard

"p-map@npm:^7.0.2":
  version: 7.0.3
  resolution: "p-map@npm:7.0.3"
  checksum: 10c0/46091610da2b38ce47bcd1d8b4835a6fa4e832848a6682cf1652bc93915770f4617afc844c10a77d1b3e56d2472bb2d5622353fa3ead01a7f42b04fc8e744a5c
  languageName: node
  linkType: hard

"p-try@npm:^2.0.0":
  version: 2.2.0
  resolution: "p-try@npm:2.2.0"
  checksum: 10c0/c36c19907734c904b16994e6535b02c36c2224d433e01a2f1ab777237f4d86e6289fd5fd464850491e940379d4606ed850c03e0f9ab600b0ebddb511312e177f
  languageName: node
  linkType: hard

"package-json-from-dist@npm:^1.0.0":
  version: 1.0.1
  resolution: "package-json-from-dist@npm:1.0.1"
  checksum: 10c0/62ba2785eb655fec084a257af34dbe24292ab74516d6aecef97ef72d4897310bc6898f6c85b5cd22770eaa1ce60d55a0230e150fb6a966e3ecd6c511e23d164b
  languageName: node
  linkType: hard

"parse-json@npm:^5.2.0":
  version: 5.2.0
  resolution: "parse-json@npm:5.2.0"
  dependencies:
    "@babel/code-frame": "npm:^7.0.0"
    error-ex: "npm:^1.3.1"
    json-parse-even-better-errors: "npm:^2.3.0"
    lines-and-columns: "npm:^1.1.6"
  checksum: 10c0/77947f2253005be7a12d858aedbafa09c9ae39eb4863adf330f7b416ca4f4a08132e453e08de2db46459256fb66afaac5ee758b44fe6541b7cdaf9d252e59585
  languageName: node
  linkType: hard

"parse-ms@npm:^4.0.0":
  version: 4.0.0
  resolution: "parse-ms@npm:4.0.0"
  checksum: 10c0/a7900f4f1ebac24cbf5e9708c16fb2fd482517fad353aecd7aefb8c2ba2f85ce017913ccb8925d231770404780df46244ea6fec598b3bde6490882358b4d2d16
  languageName: node
  linkType: hard

"parseurl@npm:^1.3.3, parseurl@npm:~1.3.3":
  version: 1.3.3
  resolution: "parseurl@npm:1.3.3"
  checksum: 10c0/90dd4760d6f6174adb9f20cf0965ae12e23879b5f5464f38e92fce8073354341e4b3b76fa3d878351efe7d01e617121955284cfd002ab087fba1a0726ec0b4f5
  languageName: node
  linkType: hard

"path-exists@npm:^4.0.0":
  version: 4.0.0
  resolution: "path-exists@npm:4.0.0"
  checksum: 10c0/8c0bd3f5238188197dc78dced15207a4716c51cc4e3624c44fc97acf69558f5ebb9a2afff486fe1b4ee148e0c133e96c5e11a9aa5c48a3006e3467da070e5e1b
  languageName: node
  linkType: hard

"path-is-absolute@npm:^1.0.0":
  version: 1.0.1
  resolution: "path-is-absolute@npm:1.0.1"
  checksum: 10c0/127da03c82172a2a50099cddbf02510c1791fc2cc5f7713ddb613a56838db1e8168b121a920079d052e0936c23005562059756d653b7c544c53185efe53be078
  languageName: node
  linkType: hard

"path-key@npm:^3.0.0, path-key@npm:^3.1.0":
  version: 3.1.1
  resolution: "path-key@npm:3.1.1"
  checksum: 10c0/748c43efd5a569c039d7a00a03b58eecd1d75f3999f5a28303d75f521288df4823bc057d8784eb72358b2895a05f29a070bc9f1f17d28226cc4e62494cc58c4c
  languageName: node
  linkType: hard

"path-key@npm:^4.0.0":
  version: 4.0.0
  resolution: "path-key@npm:4.0.0"
  checksum: 10c0/794efeef32863a65ac312f3c0b0a99f921f3e827ff63afa5cb09a377e202c262b671f7b3832a4e64731003fa94af0263713962d317b9887bd1e0c48a342efba3
  languageName: node
  linkType: hard

"path-parse@npm:^1.0.7":
  version: 1.0.7
  resolution: "path-parse@npm:1.0.7"
  checksum: 10c0/11ce261f9d294cc7a58d6a574b7f1b935842355ec66fba3c3fd79e0f036462eaf07d0aa95bb74ff432f9afef97ce1926c720988c6a7451d8a584930ae7de86e1
  languageName: node
  linkType: hard

"path-scurry@npm:^1.11.1":
  version: 1.11.1
  resolution: "path-scurry@npm:1.11.1"
  dependencies:
    lru-cache: "npm:^10.2.0"
    minipass: "npm:^5.0.0 || ^6.0.2 || ^7.0.0"
  checksum: 10c0/32a13711a2a505616ae1cc1b5076801e453e7aae6ac40ab55b388bb91b9d0547a52f5aaceff710ea400205f18691120d4431e520afbe4266b836fadede15872d
  languageName: node
  linkType: hard

"path-to-regexp@npm:0.1.12":
  version: 0.1.12
  resolution: "path-to-regexp@npm:0.1.12"
  checksum: 10c0/1c6ff10ca169b773f3bba943bbc6a07182e332464704572962d277b900aeee81ac6aa5d060ff9e01149636c30b1f63af6e69dd7786ba6e0ddb39d4dee1f0645b
  languageName: node
  linkType: hard

"path-to-regexp@npm:^8.0.0":
  version: 8.2.0
  resolution: "path-to-regexp@npm:8.2.0"
  checksum: 10c0/ef7d0a887b603c0a142fad16ccebdcdc42910f0b14830517c724466ad676107476bba2fe9fffd28fd4c141391ccd42ea426f32bb44c2c82ecaefe10c37b90f5a
  languageName: node
  linkType: hard

"path-type@npm:^6.0.0":
  version: 6.0.0
  resolution: "path-type@npm:6.0.0"
  checksum: 10c0/55baa8b1187d6dc683d5a9cfcc866168d6adff58e5db91126795376d818eee46391e00b2a4d53e44d844c7524a7d96aa68cc68f4f3e500d3d069a39e6535481c
  languageName: node
  linkType: hard

"pathe@npm:^2.0.1, pathe@npm:^2.0.2":
  version: 2.0.3
  resolution: "pathe@npm:2.0.3"
  checksum: 10c0/c118dc5a8b5c4166011b2b70608762e260085180bb9e33e80a50dcdb1e78c010b1624f4280c492c92b05fc276715a4c357d1f9edc570f8f1b3d90b6839ebaca1
  languageName: node
  linkType: hard

"pg-cloudflare@npm:^1.1.1, pg-cloudflare@npm:^1.2.6":
  version: 1.2.6
  resolution: "pg-cloudflare@npm:1.2.6"
  checksum: 10c0/db339518ed763982c45a94c96cedba1b25819fe32e9d3a4df6f82cd647c716ff9b5009f3c8b90f2940b0a1889387710ef764c9e3e7ddd8397d217309d663a2c8
  languageName: node
  linkType: hard

"pg-connection-string@npm:^2.7.0, pg-connection-string@npm:^2.9.1":
  version: 2.9.1
  resolution: "pg-connection-string@npm:2.9.1"
  checksum: 10c0/9a646529bbc0843806fc5de98ce93735a4612b571f11867178a85665d11989a827e6fd157388ca0e34ec948098564fce836c178cfd499b9f0e8cd9972b8e2e5c
  languageName: node
  linkType: hard

"pg-int8@npm:1.0.1":
  version: 1.0.1
  resolution: "pg-int8@npm:1.0.1"
  checksum: 10c0/be6a02d851fc2a4ae3e9de81710d861de3ba35ac927268973eb3cb618873a05b9424656df464dd43bd7dc3fc5295c3f5b3c8349494f87c7af50ec59ef14e0b98
  languageName: node
  linkType: hard

"pg-minify@npm:1.7.0":
  version: 1.7.0
  resolution: "pg-minify@npm:1.7.0"
  checksum: 10c0/213b2da902de56423d636ff72e131ef9b1ec8ed2e9471febeb335bbf44e03b90f0afed189922276aa84d7abc53bbb1f9c78cd67152b90b9c631a462d0e646477
  languageName: node
  linkType: hard

"pg-pool@npm:^3.10.0, pg-pool@npm:^3.10.1, pg-pool@npm:^3.8.0":
  version: 3.10.1
  resolution: "pg-pool@npm:3.10.1"
  peerDependencies:
    pg: ">=8.0"
  checksum: 10c0/a00916b7df64226cc597fe769e3a757ff9b11562dc87ce5b0a54101a18c1fe282daaa2accaf27221e81e1e4cdf4da6a33dab09614734d32904d6c4e11c44a079
  languageName: node
  linkType: hard

"pg-promise@npm:^11.14.0":
  version: 11.14.0
  resolution: "pg-promise@npm:11.14.0"
  dependencies:
    assert-options: "npm:0.8.3"
    pg: "npm:8.14.1"
    pg-minify: "npm:1.7.0"
    spex: "npm:3.4.1"
  peerDependencies:
    pg-query-stream: 4.8.1
  checksum: 10c0/496a8921d0e533cd160692b1681d9a4ac63642ecae0892417591e7b0705d6c76d49dba206e0b676060e53ee5cdbc0b4177edfde5657cdac5ba691e3c34cafac9
  languageName: node
  linkType: hard

"pg-protocol@npm:*, pg-protocol@npm:^1.10.2, pg-protocol@npm:^1.8.0":
  version: 1.10.2
  resolution: "pg-protocol@npm:1.10.2"
  checksum: 10c0/3f9b5aba3f356190738ea25ecded3cd033cd2218789acf9c67b75788932c4b594eeb7043481822b69eaae4d84401e00142a2ef156297a8347987a78a52afd50e
  languageName: node
  linkType: hard

"pg-types@npm:2.2.0, pg-types@npm:^2.1.0, pg-types@npm:^2.2.0":
  version: 2.2.0
  resolution: "pg-types@npm:2.2.0"
  dependencies:
    pg-int8: "npm:1.0.1"
    postgres-array: "npm:~2.0.0"
    postgres-bytea: "npm:~1.0.0"
    postgres-date: "npm:~1.0.4"
    postgres-interval: "npm:^1.1.0"
  checksum: 10c0/ab3f8069a323f601cd2d2279ca8c425447dab3f9b61d933b0601d7ffc00d6200df25e26a4290b2b0783b59278198f7dd2ed03e94c4875797919605116a577c65
  languageName: node
  linkType: hard

"pg@npm:8.14.1":
  version: 8.14.1
  resolution: "pg@npm:8.14.1"
  dependencies:
    pg-cloudflare: "npm:^1.1.1"
    pg-connection-string: "npm:^2.7.0"
    pg-pool: "npm:^3.8.0"
    pg-protocol: "npm:^1.8.0"
    pg-types: "npm:^2.1.0"
    pgpass: "npm:1.x"
  peerDependencies:
    pg-native: ">=3.0.1"
  dependenciesMeta:
    pg-cloudflare:
      optional: true
  peerDependenciesMeta:
    pg-native:
      optional: true
  checksum: 10c0/221741cfcea4ab32c8b57bd60703bc36cfb5622dcac56c19e45f504ef8669f2f2e0429af8850f58079cfc89055da35b5a5e12de19e0505e3f61a4b4349388dcb
  languageName: node
  linkType: hard

"pg@npm:^8.16.0":
  version: 8.16.2
  resolution: "pg@npm:8.16.2"
  dependencies:
    pg-cloudflare: "npm:^1.2.6"
    pg-connection-string: "npm:^2.9.1"
    pg-pool: "npm:^3.10.1"
    pg-protocol: "npm:^1.10.2"
    pg-types: "npm:2.2.0"
    pgpass: "npm:1.0.5"
  peerDependencies:
    pg-native: ">=3.0.1"
  dependenciesMeta:
    pg-cloudflare:
      optional: true
  peerDependenciesMeta:
    pg-native:
      optional: true
  checksum: 10c0/e444103fda2fa236bb7951e534bbce52d81df8f0ca43b36cdd32da2e558946ae011fa6a0fcfea2e48d935addba821868e76d3f4f670b313f639a3d24fcd420af
  languageName: node
  linkType: hard

"pgpass@npm:1.0.5, pgpass@npm:1.x":
  version: 1.0.5
  resolution: "pgpass@npm:1.0.5"
  dependencies:
    split2: "npm:^4.1.0"
  checksum: 10c0/5ea6c9b2de04c33abb08d33a2dded303c4a3c7162a9264519cbe85c0a9857d712463140ba42fad0c7cd4b21f644dd870b45bb2e02fcbe505b4de0744fd802c1d
  languageName: node
  linkType: hard

"picocolors@npm:^1.0.0, picocolors@npm:^1.1.1":
  version: 1.1.1
  resolution: "picocolors@npm:1.1.1"
  checksum: 10c0/e2e3e8170ab9d7c7421969adaa7e1b31434f789afb9b3f115f6b96d91945041ac3ceb02e9ec6fe6510ff036bcc0bf91e69a1772edc0b707e12b19c0f2d6bcf58
  languageName: node
  linkType: hard

"picomatch@npm:^2.0.4, picomatch@npm:^2.2.1, picomatch@npm:^2.2.3, picomatch@npm:^2.3.1":
  version: 2.3.1
  resolution: "picomatch@npm:2.3.1"
  checksum: 10c0/26c02b8d06f03206fc2ab8d16f19960f2ff9e81a658f831ecb656d8f17d9edc799e8364b1f4a7873e89d9702dff96204be0fa26fe4181f6843f040f819dac4be
  languageName: node
  linkType: hard

"picomatch@npm:^4.0.2":
  version: 4.0.2
  resolution: "picomatch@npm:4.0.2"
  checksum: 10c0/7c51f3ad2bb42c776f49ebf964c644958158be30d0a510efd5a395e8d49cb5acfed5b82c0c5b365523ce18e6ab85013c9ebe574f60305892ec3fa8eee8304ccc
  languageName: node
  linkType: hard

"pino-abstract-transport@npm:^2.0.0":
  version: 2.0.0
  resolution: "pino-abstract-transport@npm:2.0.0"
  dependencies:
    split2: "npm:^4.0.0"
  checksum: 10c0/02c05b8f2ffce0d7c774c8e588f61e8b77de8ccb5f8125afd4a7325c9ea0e6af7fb78168999657712ae843e4462bb70ac550dfd6284f930ee57f17f486f25a9f
  languageName: node
  linkType: hard

"pino-pretty@npm:^13.0.0":
  version: 13.0.0
  resolution: "pino-pretty@npm:13.0.0"
  dependencies:
    colorette: "npm:^2.0.7"
    dateformat: "npm:^4.6.3"
    fast-copy: "npm:^3.0.2"
    fast-safe-stringify: "npm:^2.1.1"
    help-me: "npm:^5.0.0"
    joycon: "npm:^3.1.1"
    minimist: "npm:^1.2.6"
    on-exit-leak-free: "npm:^2.1.0"
    pino-abstract-transport: "npm:^2.0.0"
    pump: "npm:^3.0.0"
    secure-json-parse: "npm:^2.4.0"
    sonic-boom: "npm:^4.0.1"
    strip-json-comments: "npm:^3.1.1"
  bin:
    pino-pretty: bin.js
  checksum: 10c0/015dac25006c1b9820b9e01fccb8a392a019e12b30e6bfc3f3f61ecca8dbabcd000a8f3f64410b620b7f5d08579ba85e6ef137f7fbeaad70d46397a97a5f75ea
  languageName: node
  linkType: hard

"pino-std-serializers@npm:^7.0.0":
  version: 7.0.0
  resolution: "pino-std-serializers@npm:7.0.0"
  checksum: 10c0/73e694d542e8de94445a03a98396cf383306de41fd75ecc07085d57ed7a57896198508a0dec6eefad8d701044af21eb27253ccc352586a03cf0d4a0bd25b4133
  languageName: node
  linkType: hard

"pino@npm:^9.7.0":
  version: 9.7.0
  resolution: "pino@npm:9.7.0"
  dependencies:
    atomic-sleep: "npm:^1.0.0"
    fast-redact: "npm:^3.1.1"
    on-exit-leak-free: "npm:^2.1.0"
    pino-abstract-transport: "npm:^2.0.0"
    pino-std-serializers: "npm:^7.0.0"
    process-warning: "npm:^5.0.0"
    quick-format-unescaped: "npm:^4.0.3"
    real-require: "npm:^0.2.0"
    safe-stable-stringify: "npm:^2.3.1"
    sonic-boom: "npm:^4.0.1"
    thread-stream: "npm:^3.0.0"
  bin:
    pino: bin.js
  checksum: 10c0/c7f8a83a9a9d728b4eff6d0f4b9367f031c91bcaa5806fbf0eedcc8e77faba593d59baf11a8fba0dd1c778bb17ca7ed01418ac1df4ec129faeedd4f3ecaff66f
  languageName: node
  linkType: hard

"pirates@npm:^4.0.4":
  version: 4.0.7
  resolution: "pirates@npm:4.0.7"
  checksum: 10c0/a51f108dd811beb779d58a76864bbd49e239fa40c7984cd11596c75a121a8cc789f1c8971d8bb15f0dbf9d48b76c05bb62fcbce840f89b688c0fa64b37e8478a
  languageName: node
  linkType: hard

"pkce-challenge@npm:^5.0.0":
  version: 5.0.0
  resolution: "pkce-challenge@npm:5.0.0"
  checksum: 10c0/c6706d627fdbb6f22bf8cc5d60d96d6b6a7bb481399b336a3d3f4e9bfba3e167a2c32f8ec0b5e74be686a0ba3bcc9894865d4c2dd1b91cea4c05dba1f28602c3
  languageName: node
  linkType: hard

"pkg-dir@npm:^4.2.0":
  version: 4.2.0
  resolution: "pkg-dir@npm:4.2.0"
  dependencies:
    find-up: "npm:^4.0.0"
  checksum: 10c0/c56bda7769e04907a88423feb320babaed0711af8c436ce3e56763ab1021ba107c7b0cafb11cde7529f669cfc22bffcaebffb573645cbd63842ea9fb17cd7728
  languageName: node
  linkType: hard

"pkg-types@npm:^1.0.3, pkg-types@npm:^1.1.3, pkg-types@npm:^1.3.0":
  version: 1.3.1
  resolution: "pkg-types@npm:1.3.1"
  dependencies:
    confbox: "npm:^0.1.8"
    mlly: "npm:^1.7.4"
    pathe: "npm:^2.0.1"
  checksum: 10c0/19e6cb8b66dcc66c89f2344aecfa47f2431c988cfa3366bdfdcfb1dd6695f87dcce37fbd90fe9d1605e2f4440b77f391e83c23255347c35cf84e7fd774d7fcea
  languageName: node
  linkType: hard

"postgres-array@npm:~2.0.0":
  version: 2.0.0
  resolution: "postgres-array@npm:2.0.0"
  checksum: 10c0/cbd56207e4141d7fbf08c86f2aebf21fa7064943d3f808ec85f442ff94b48d891e7a144cc02665fb2de5dbcb9b8e3183a2ac749959e794b4a4cfd379d7a21d08
  languageName: node
  linkType: hard

"postgres-bytea@npm:~1.0.0":
  version: 1.0.0
  resolution: "postgres-bytea@npm:1.0.0"
  checksum: 10c0/febf2364b8a8953695cac159eeb94542ead5886792a9627b97e33f6b5bb6e263bc0706ab47ec221516e79fbd6b2452d668841830fb3b49ec6c0fc29be61892ce
  languageName: node
  linkType: hard

"postgres-date@npm:~1.0.4":
  version: 1.0.7
  resolution: "postgres-date@npm:1.0.7"
  checksum: 10c0/0ff91fccc64003e10b767fcfeefb5eaffbc522c93aa65d5051c49b3c4ce6cb93ab091a7d22877a90ad60b8874202c6f1d0f935f38a7235ed3b258efd54b97ca9
  languageName: node
  linkType: hard

"postgres-interval@npm:^1.1.0":
  version: 1.2.0
  resolution: "postgres-interval@npm:1.2.0"
  dependencies:
    xtend: "npm:^4.0.0"
  checksum: 10c0/c1734c3cb79e7f22579af0b268a463b1fa1d084e742a02a7a290c4f041e349456f3bee3b4ee0bb3f226828597f7b76deb615c1b857db9a742c45520100456272
  languageName: node
  linkType: hard

"postgres@npm:^3.4.5, postgres@npm:^3.4.7":
  version: 3.4.7
  resolution: "postgres@npm:3.4.7"
  checksum: 10c0/b2e61b1064d38e7e1df8291f6d5a7e11f892a3240e00cf2b5e5542bf9abbfe97f3963164aeb56b42c1ab6b8aae3454c57f5bbc1791df0769375542740a7cde72
  languageName: node
  linkType: hard

"posthog-node@npm:4.18.0":
  version: 4.18.0
  resolution: "posthog-node@npm:4.18.0"
  dependencies:
    axios: "npm:^1.8.2"
  checksum: 10c0/aa30427599d15b98357b5a88096a8b38b0812c71af49eb54fce0a46164521c247c3b064973931c3471cf84cb7f0e391bfc5d6174cd038f75f8c02cb8a9626cff
  languageName: node
  linkType: hard

"prettier@npm:^3.5.3":
  version: 3.5.3
  resolution: "prettier@npm:3.5.3"
  bin:
    prettier: bin/prettier.cjs
  checksum: 10c0/3880cb90b9dc0635819ab52ff571518c35bd7f15a6e80a2054c05dbc8a3aa6e74f135519e91197de63705bcb38388ded7e7230e2178432a1468005406238b877
  languageName: node
  linkType: hard

"pretty-format@npm:^29.0.0, pretty-format@npm:^29.7.0":
  version: 29.7.0
  resolution: "pretty-format@npm:29.7.0"
  dependencies:
    "@jest/schemas": "npm:^29.6.3"
    ansi-styles: "npm:^5.0.0"
    react-is: "npm:^18.0.0"
  checksum: 10c0/edc5ff89f51916f036c62ed433506b55446ff739358de77207e63e88a28ca2894caac6e73dcb68166a606e51c8087d32d400473e6a9fdd2dbe743f46c9c0276f
  languageName: node
  linkType: hard

"pretty-ms@npm:^9.2.0":
  version: 9.2.0
  resolution: "pretty-ms@npm:9.2.0"
  dependencies:
    parse-ms: "npm:^4.0.0"
  checksum: 10c0/ab6d066f90e9f77020426986e1b018369f41575674544c539aabec2e63a20fec01166d8cf6571d0e165ad11cfe5a8134a2a48a36d42ab291c59c6deca5264cbb
  languageName: node
  linkType: hard

"proc-log@npm:^5.0.0":
  version: 5.0.0
  resolution: "proc-log@npm:5.0.0"
  checksum: 10c0/bbe5edb944b0ad63387a1d5b1911ae93e05ce8d0f60de1035b218cdcceedfe39dbd2c697853355b70f1a090f8f58fe90da487c85216bf9671f9499d1a897e9e3
  languageName: node
  linkType: hard

"process-warning@npm:^5.0.0":
  version: 5.0.0
  resolution: "process-warning@npm:5.0.0"
  checksum: 10c0/941f48863d368ec161e0b5890ba0c6af94170078f3d6b5e915c19b36fb59edb0dc2f8e834d25e0d375a8bf368a49d490f080508842168832b93489d17843ec29
  languageName: node
  linkType: hard

"process@npm:^0.11.10":
  version: 0.11.10
  resolution: "process@npm:0.11.10"
  checksum: 10c0/40c3ce4b7e6d4b8c3355479df77aeed46f81b279818ccdc500124e6a5ab882c0cc81ff7ea16384873a95a74c4570b01b120f287abbdd4c877931460eca6084b3
  languageName: node
  linkType: hard

"promise-queue@npm:^2.2.5":
  version: 2.2.5
  resolution: "promise-queue@npm:2.2.5"
  checksum: 10c0/6b29c1c717399c7e10d9527f3a6af74ea7f811aa297dab27c22e5718a824b1cb012d179e1f5d609bf2db051aaf49630fe8222317dedc2174b24971026ab39969
  languageName: node
  linkType: hard

"promise-retry@npm:^2.0.1":
  version: 2.0.1
  resolution: "promise-retry@npm:2.0.1"
  dependencies:
    err-code: "npm:^2.0.2"
    retry: "npm:^0.12.0"
  checksum: 10c0/9c7045a1a2928094b5b9b15336dcd2a7b1c052f674550df63cc3f36cd44028e5080448175b6f6ca32b642de81150f5e7b1a98b728f15cb069f2dd60ac2616b96
  languageName: node
  linkType: hard

"prompt@npm:^1.3.0":
  version: 1.3.0
  resolution: "prompt@npm:1.3.0"
  dependencies:
    "@colors/colors": "npm:1.5.0"
    async: "npm:3.2.3"
    read: "npm:1.0.x"
    revalidator: "npm:0.1.x"
    winston: "npm:2.x"
  checksum: 10c0/f2c67178ffd82563dff958b7d9502e6346464675539158e378bd10e236093cbed395099fcfaeb5df8492b06bfd218f46f2ae75796679a127fd6705ee608e72d9
  languageName: node
  linkType: hard

"prompts@npm:^2.0.1":
  version: 2.4.2
  resolution: "prompts@npm:2.4.2"
  dependencies:
    kleur: "npm:^3.0.3"
    sisteransi: "npm:^1.0.5"
  checksum: 10c0/16f1ac2977b19fe2cf53f8411cc98db7a3c8b115c479b2ca5c82b5527cd937aa405fa04f9a5960abeb9daef53191b53b4d13e35c1f5d50e8718c76917c5f1ea4
  languageName: node
  linkType: hard

"property-information@npm:^7.0.0":
  version: 7.1.0
  resolution: "property-information@npm:7.1.0"
  checksum: 10c0/e0fe22cff26103260ad0e82959229106563fa115a54c4d6c183f49d88054e489cc9f23452d3ad584179dc13a8b7b37411a5df873746b5e4086c865874bfa968e
  languageName: node
  linkType: hard

"protobufjs@npm:^7.2.5, protobufjs@npm:^7.3.0":
  version: 7.5.3
  resolution: "protobufjs@npm:7.5.3"
  dependencies:
    "@protobufjs/aspromise": "npm:^1.1.2"
    "@protobufjs/base64": "npm:^1.1.2"
    "@protobufjs/codegen": "npm:^2.0.4"
    "@protobufjs/eventemitter": "npm:^1.1.0"
    "@protobufjs/fetch": "npm:^1.1.0"
    "@protobufjs/float": "npm:^1.0.2"
    "@protobufjs/inquire": "npm:^1.1.0"
    "@protobufjs/path": "npm:^1.1.2"
    "@protobufjs/pool": "npm:^1.1.0"
    "@protobufjs/utf8": "npm:^1.1.0"
    "@types/node": "npm:>=13.7.0"
    long: "npm:^5.0.0"
  checksum: 10c0/9dc131a7e7a610b8291a0b0033b313f8754ef419b57c44d27874dd4edf1afc2a9a77d7a5bc2df9a744cba014de67c92756759c73200b702a11f13360e907b0dd
  languageName: node
  linkType: hard

"proxy-addr@npm:^2.0.7, proxy-addr@npm:~2.0.7":
  version: 2.0.7
  resolution: "proxy-addr@npm:2.0.7"
  dependencies:
    forwarded: "npm:0.2.0"
    ipaddr.js: "npm:1.9.1"
  checksum: 10c0/c3eed999781a35f7fd935f398b6d8920b6fb00bbc14287bc6de78128ccc1a02c89b95b56742bf7cf0362cc333c61d138532049c7dedc7a328ef13343eff81210
  languageName: node
  linkType: hard

"proxy-from-env@npm:^1.1.0":
  version: 1.1.0
  resolution: "proxy-from-env@npm:1.1.0"
  checksum: 10c0/fe7dd8b1bdbbbea18d1459107729c3e4a2243ca870d26d34c2c1bcd3e4425b7bcc5112362df2d93cc7fb9746f6142b5e272fd1cc5c86ddf8580175186f6ad42b
  languageName: node
  linkType: hard

"pstree.remy@npm:^1.1.8":
  version: 1.1.8
  resolution: "pstree.remy@npm:1.1.8"
  checksum: 10c0/30f78c88ce6393cb3f7834216cb6e282eb83c92ccb227430d4590298ab2811bc4a4745f850a27c5178e79a8f3e316591de0fec87abc19da648c2b3c6eb766d14
  languageName: node
  linkType: hard

"pump@npm:^3.0.0":
  version: 3.0.3
  resolution: "pump@npm:3.0.3"
  dependencies:
    end-of-stream: "npm:^1.1.0"
    once: "npm:^1.3.1"
  checksum: 10c0/ada5cdf1d813065bbc99aa2c393b8f6beee73b5de2890a8754c9f488d7323ffd2ca5f5a0943b48934e3fcbd97637d0337369c3c631aeb9614915db629f1c75c9
  languageName: node
  linkType: hard

"punycode@npm:^2.1.0":
  version: 2.3.1
  resolution: "punycode@npm:2.3.1"
  checksum: 10c0/14f76a8206bc3464f794fb2e3d3cc665ae416c01893ad7a02b23766eb07159144ee612ad67af5e84fa4479ccfe67678c4feb126b0485651b302babf66f04f9e9
  languageName: node
  linkType: hard

"pure-rand@npm:^6.0.0":
  version: 6.1.0
  resolution: "pure-rand@npm:6.1.0"
  checksum: 10c0/1abe217897bf74dcb3a0c9aba3555fe975023147b48db540aa2faf507aee91c03bf54f6aef0eb2bf59cc259a16d06b28eca37f0dc426d94f4692aeff02fb0e65
  languageName: node
  linkType: hard

"qs@npm:6.11.2":
  version: 6.11.2
  resolution: "qs@npm:6.11.2"
  dependencies:
    side-channel: "npm:^1.0.4"
  checksum: 10c0/4f95d4ff18ed480befcafa3390022817ffd3087fc65f146cceb40fc5edb9fa96cb31f648cae2fa96ca23818f0798bd63ad4ca369a0e22702fcd41379b3ab6571
  languageName: node
  linkType: hard

"qs@npm:6.13.0":
  version: 6.13.0
  resolution: "qs@npm:6.13.0"
  dependencies:
    side-channel: "npm:^1.0.6"
  checksum: 10c0/62372cdeec24dc83a9fb240b7533c0fdcf0c5f7e0b83343edd7310f0ab4c8205a5e7c56406531f2e47e1b4878a3821d652be4192c841de5b032ca83619d8f860
  languageName: node
  linkType: hard

"qs@npm:^6.11.0, qs@npm:^6.14.0":
  version: 6.14.0
  resolution: "qs@npm:6.14.0"
  dependencies:
    side-channel: "npm:^1.1.0"
  checksum: 10c0/8ea5d91bf34f440598ee389d4a7d95820e3b837d3fd9f433871f7924801becaa0cd3b3b4628d49a7784d06a8aea9bc4554d2b6d8d584e2d221dc06238a42909c
  languageName: node
  linkType: hard

"queue-microtask@npm:^1.2.2":
  version: 1.2.3
  resolution: "queue-microtask@npm:1.2.3"
  checksum: 10c0/900a93d3cdae3acd7d16f642c29a642aea32c2026446151f0778c62ac089d4b8e6c986811076e1ae180a694cedf077d453a11b58ff0a865629a4f82ab558e102
  languageName: node
  linkType: hard

"quick-format-unescaped@npm:^4.0.3":
  version: 4.0.4
  resolution: "quick-format-unescaped@npm:4.0.4"
  checksum: 10c0/fe5acc6f775b172ca5b4373df26f7e4fd347975578199e7d74b2ae4077f0af05baa27d231de1e80e8f72d88275ccc6028568a7a8c9ee5e7368ace0e18eff93a4
  languageName: node
  linkType: hard

"radash@npm:^12.1.0":
  version: 12.1.1
  resolution: "radash@npm:12.1.1"
  checksum: 10c0/f8cbe85c0a8a444f1d2892c82875e00333e58a7b8afdbf3d8d04574d0532d895484bae20650c197c87dd91abfbd1e0af1c819cd12e6dc306633acec30222af7e
  languageName: node
  linkType: hard

"range-parser@npm:^1.2.1, range-parser@npm:~1.2.1":
  version: 1.2.1
  resolution: "range-parser@npm:1.2.1"
  checksum: 10c0/96c032ac2475c8027b7a4e9fe22dc0dfe0f6d90b85e496e0f016fbdb99d6d066de0112e680805075bd989905e2123b3b3d002765149294dce0c1f7f01fcc2ea0
  languageName: node
  linkType: hard

"raw-body@npm:2.5.2":
  version: 2.5.2
  resolution: "raw-body@npm:2.5.2"
  dependencies:
    bytes: "npm:3.1.2"
    http-errors: "npm:2.0.0"
    iconv-lite: "npm:0.4.24"
    unpipe: "npm:1.0.0"
  checksum: 10c0/b201c4b66049369a60e766318caff5cb3cc5a900efd89bdac431463822d976ad0670912c931fdbdcf5543207daf6f6833bca57aa116e1661d2ea91e12ca692c4
  languageName: node
  linkType: hard

"raw-body@npm:^3.0.0":
  version: 3.0.0
  resolution: "raw-body@npm:3.0.0"
  dependencies:
    bytes: "npm:3.1.2"
    http-errors: "npm:2.0.0"
    iconv-lite: "npm:0.6.3"
    unpipe: "npm:1.0.0"
  checksum: 10c0/f8daf4b724064a4811d118745a781ca0fb4676298b8adadfd6591155549cfea0a067523cf7dd3baeb1265fecc9ce5dfb2fc788c12c66b85202a336593ece0f87
  languageName: node
  linkType: hard

"react-is@npm:^18.0.0":
  version: 18.3.1
  resolution: "react-is@npm:18.3.1"
  checksum: 10c0/f2f1e60010c683479e74c63f96b09fb41603527cd131a9959e2aee1e5a8b0caf270b365e5ca77d4a6b18aae659b60a86150bb3979073528877029b35aecd2072
  languageName: node
  linkType: hard

"read@npm:1.0.x":
  version: 1.0.7
  resolution: "read@npm:1.0.7"
  dependencies:
    mute-stream: "npm:~0.0.4"
  checksum: 10c0/443533f05d5bb11b36ef1c6d625aae4e2ced8967e93cf546f35aa77b4eb6bd157f4256619e446bae43467f8f6619c7bc5c76983348dffaf36afedf4224f46216
  languageName: node
  linkType: hard

"readable-stream@npm:^3.0.2":
  version: 3.6.2
  resolution: "readable-stream@npm:3.6.2"
  dependencies:
    inherits: "npm:^2.0.3"
    string_decoder: "npm:^1.1.1"
    util-deprecate: "npm:^1.0.1"
  checksum: 10c0/e37be5c79c376fdd088a45fa31ea2e423e5d48854be7a22a58869b4e84d25047b193f6acb54f1012331e1bcd667ffb569c01b99d36b0bd59658fb33f513511b7
  languageName: node
  linkType: hard

"readable-stream@npm:^4.5.2":
  version: 4.7.0
  resolution: "readable-stream@npm:4.7.0"
  dependencies:
    abort-controller: "npm:^3.0.0"
    buffer: "npm:^6.0.3"
    events: "npm:^3.3.0"
    process: "npm:^0.11.10"
    string_decoder: "npm:^1.3.0"
  checksum: 10c0/fd86d068da21cfdb10f7a4479f2e47d9c0a9b0c862fc0c840a7e5360201580a55ac399c764b12a4f6fa291f8cee74d9c4b7562e0d53b3c4b2769f2c98155d957
  languageName: node
  linkType: hard

"readdirp@npm:~3.6.0":
  version: 3.6.0
  resolution: "readdirp@npm:3.6.0"
  dependencies:
    picomatch: "npm:^2.2.1"
  checksum: 10c0/6fa848cf63d1b82ab4e985f4cf72bd55b7dcfd8e0a376905804e48c3634b7e749170940ba77b32804d5fe93b3cc521aa95a8d7e7d725f830da6d93f3669ce66b
  languageName: node
  linkType: hard

"real-require@npm:^0.2.0":
  version: 0.2.0
  resolution: "real-require@npm:0.2.0"
  checksum: 10c0/23eea5623642f0477412ef8b91acd3969015a1501ed34992ada0e3af521d3c865bb2fe4cdbfec5fe4b505f6d1ef6a03e5c3652520837a8c3b53decff7e74b6a0
  languageName: node
  linkType: hard

"redis-errors@npm:^1.0.0, redis-errors@npm:^1.2.0":
  version: 1.2.0
  resolution: "redis-errors@npm:1.2.0"
  checksum: 10c0/5b316736e9f532d91a35bff631335137a4f974927bb2fb42bf8c2f18879173a211787db8ac4c3fde8f75ed6233eb0888e55d52510b5620e30d69d7d719c8b8a7
  languageName: node
  linkType: hard

"redis-info@npm:^3.1.0":
  version: 3.1.0
  resolution: "redis-info@npm:3.1.0"
  dependencies:
    lodash: "npm:^4.17.11"
  checksum: 10c0/ec0f31d97893c5828cec7166486d74198c92160c60073b6f2fe805cdf575a10ddcccc7641737d44b8f451355f0ab5b6c7b0d79e8fc24742b75dd625f91ffee38
  languageName: node
  linkType: hard

"redis-parser@npm:^3.0.0":
  version: 3.0.0
  resolution: "redis-parser@npm:3.0.0"
  dependencies:
    redis-errors: "npm:^1.0.0"
  checksum: 10c0/ee16ac4c7b2a60b1f42a2cdaee22b005bd4453eb2d0588b8a4939718997ae269da717434da5d570fe0b05030466eeb3f902a58cf2e8e1ca058bf6c9c596f632f
  languageName: node
  linkType: hard

"redis-server@npm:^1.2.2":
  version: 1.2.2
  resolution: "redis-server@npm:1.2.2"
  dependencies:
    promise-queue: "npm:^2.2.5"
  checksum: 10c0/ccd43c65ab7ce124bcee75ceb1f5ccff11dc912769f33e85d7c2de2823d42b1d07a4ba763f4c62f918fee2c63cc5fdd0a9f64bb52ea1e569c026f2c79f2406d6
  languageName: node
  linkType: hard

"redis@npm:^4.7.1":
  version: 4.7.1
  resolution: "redis@npm:4.7.1"
  dependencies:
    "@redis/bloom": "npm:1.2.0"
    "@redis/client": "npm:1.6.1"
    "@redis/graph": "npm:1.1.1"
    "@redis/json": "npm:1.0.7"
    "@redis/search": "npm:1.2.0"
    "@redis/time-series": "npm:1.1.0"
  checksum: 10c0/36fc11b7b305e73cafd0a32c5e7070aef72d3a33f2b34f609a1c295afb7d1a022d74eeda96e8d887ef596bbe8ce10811450d02b63175a338d2ff258ff34bd237
  languageName: node
  linkType: hard

"regex-recursion@npm:^5.1.1":
  version: 5.1.1
  resolution: "regex-recursion@npm:5.1.1"
  dependencies:
    regex: "npm:^5.1.1"
    regex-utilities: "npm:^2.3.0"
  checksum: 10c0/c61c284bc41f2b271dfa0549d657a5a26397108b860d7cdb15b43080196681c0092bf8cf920a8836213e239d1195c4ccf6db9be9298bce4e68c9daab1febeab9
  languageName: node
  linkType: hard

"regex-utilities@npm:^2.3.0":
  version: 2.3.0
  resolution: "regex-utilities@npm:2.3.0"
  checksum: 10c0/78c550a80a0af75223244fff006743922591bd8f61d91fef7c86b9b56cf9bbf8ee5d7adb6d8991b5e304c57c90103fc4818cf1e357b11c6c669b782839bd7893
  languageName: node
  linkType: hard

"regex@npm:^5.1.1":
  version: 5.1.1
  resolution: "regex@npm:5.1.1"
  dependencies:
    regex-utilities: "npm:^2.3.0"
  checksum: 10c0/314e032f0fe09497ce7a160b99675c4a16c7524f0a24833f567cbbf3a2bebc26bf59737dc5c23f32af7c74aa7a6bd3f809fc72c90c49a05faf8be45677db508a
  languageName: node
  linkType: hard

"require-directory@npm:^2.1.1":
  version: 2.1.1
  resolution: "require-directory@npm:2.1.1"
  checksum: 10c0/83aa76a7bc1531f68d92c75a2ca2f54f1b01463cb566cf3fbc787d0de8be30c9dbc211d1d46be3497dac5785fe296f2dd11d531945ac29730643357978966e99
  languageName: node
  linkType: hard

"require-in-the-middle@npm:^7.1.1":
  version: 7.5.2
  resolution: "require-in-the-middle@npm:7.5.2"
  dependencies:
    debug: "npm:^4.3.5"
    module-details-from-path: "npm:^1.0.3"
    resolve: "npm:^1.22.8"
  checksum: 10c0/43a2dac5520e39d13c413650895715e102d6802e6cc6ff322017bd948f12a9657fe28435f7cbbcba437b167f02e192ac7af29fa35cabd5d0c375d071c0605e01
  languageName: node
  linkType: hard

"resolve-cwd@npm:^3.0.0":
  version: 3.0.0
  resolution: "resolve-cwd@npm:3.0.0"
  dependencies:
    resolve-from: "npm:^5.0.0"
  checksum: 10c0/e608a3ebd15356264653c32d7ecbc8fd702f94c6703ea4ac2fb81d9c359180cba0ae2e6b71faa446631ed6145454d5a56b227efc33a2d40638ac13f8beb20ee4
  languageName: node
  linkType: hard

"resolve-from@npm:^5.0.0":
  version: 5.0.0
  resolution: "resolve-from@npm:5.0.0"
  checksum: 10c0/b21cb7f1fb746de8107b9febab60095187781137fd803e6a59a76d421444b1531b641bba5857f5dc011974d8a5c635d61cec49e6bd3b7fc20e01f0fafc4efbf2
  languageName: node
  linkType: hard

"resolve-pkg-maps@npm:^1.0.0":
  version: 1.0.0
  resolution: "resolve-pkg-maps@npm:1.0.0"
  checksum: 10c0/fb8f7bbe2ca281a73b7ef423a1cbc786fb244bd7a95cbe5c3fba25b27d327150beca8ba02f622baea65919a57e061eb5005204daa5f93ed590d9b77463a567ab
  languageName: node
  linkType: hard

"resolve.exports@npm:^2.0.0":
  version: 2.0.3
  resolution: "resolve.exports@npm:2.0.3"
  checksum: 10c0/1ade1493f4642a6267d0a5e68faeac20b3d220f18c28b140343feb83694d8fed7a286852aef43689d16042c61e2ddb270be6578ad4a13990769e12065191200d
  languageName: node
  linkType: hard

"resolve@npm:^1.20.0, resolve@npm:^1.22.1, resolve@npm:^1.22.8":
  version: 1.22.10
  resolution: "resolve@npm:1.22.10"
  dependencies:
    is-core-module: "npm:^2.16.0"
    path-parse: "npm:^1.0.7"
    supports-preserve-symlinks-flag: "npm:^1.0.0"
  bin:
    resolve: bin/resolve
  checksum: 10c0/8967e1f4e2cc40f79b7e080b4582b9a8c5ee36ffb46041dccb20e6461161adf69f843b43067b4a375de926a2cd669157e29a29578191def399dd5ef89a1b5203
  languageName: node
  linkType: hard

"resolve@patch:resolve@npm%3A^1.20.0#optional!builtin<compat/resolve>, resolve@patch:resolve@npm%3A^1.22.1#optional!builtin<compat/resolve>, resolve@patch:resolve@npm%3A^1.22.8#optional!builtin<compat/resolve>":
  version: 1.22.10
  resolution: "resolve@patch:resolve@npm%3A1.22.10#optional!builtin<compat/resolve>::version=1.22.10&hash=c3c19d"
  dependencies:
    is-core-module: "npm:^2.16.0"
    path-parse: "npm:^1.0.7"
    supports-preserve-symlinks-flag: "npm:^1.0.0"
  bin:
    resolve: bin/resolve
  checksum: 10c0/52a4e505bbfc7925ac8f4cd91fd8c4e096b6a89728b9f46861d3b405ac9a1ccf4dcbf8befb4e89a2e11370dacd0160918163885cbc669369590f2f31f4c58939
  languageName: node
  linkType: hard

"retry@npm:^0.12.0":
  version: 0.12.0
  resolution: "retry@npm:0.12.0"
  checksum: 10c0/59933e8501727ba13ad73ef4a04d5280b3717fd650408460c987392efe9d7be2040778ed8ebe933c5cbd63da3dcc37919c141ef8af0a54a6e4fca5a2af177bfe
  languageName: node
  linkType: hard

"reusify@npm:^1.0.4":
  version: 1.1.0
  resolution: "reusify@npm:1.1.0"
  checksum: 10c0/4eff0d4a5f9383566c7d7ec437b671cc51b25963bd61bf127c3f3d3f68e44a026d99b8d2f1ad344afff8d278a8fe70a8ea092650a716d22287e8bef7126bb2fa
  languageName: node
  linkType: hard

"revalidator@npm:0.1.x":
  version: 0.1.8
  resolution: "revalidator@npm:0.1.8"
  checksum: 10c0/bb324a169dfd7a6a8503861474c48da55244214391c5e3fd20e37802d9a24ea395ab57d218d26715110e6a834b3ad2dbd3db12bb35e8facaabb876093e9ade2b
  languageName: node
  linkType: hard

"rimraf@npm:^5.0.5":
  version: 5.0.10
  resolution: "rimraf@npm:5.0.10"
  dependencies:
    glob: "npm:^10.3.7"
  bin:
    rimraf: dist/esm/bin.mjs
  checksum: 10c0/7da4fd0e15118ee05b918359462cfa1e7fe4b1228c7765195a45b55576e8c15b95db513b8466ec89129666f4af45ad978a3057a02139afba1a63512a2d9644cc
  languageName: node
  linkType: hard

"rollup-plugin-esbuild@npm:^6.2.1":
  version: 6.2.1
  resolution: "rollup-plugin-esbuild@npm:6.2.1"
  dependencies:
    debug: "npm:^4.4.0"
    es-module-lexer: "npm:^1.6.0"
    get-tsconfig: "npm:^4.10.0"
    unplugin-utils: "npm:^0.2.4"
  peerDependencies:
    esbuild: ">=0.18.0"
    rollup: ^1.20.0 || ^2.0.0 || ^3.0.0 || ^4.0.0
  checksum: 10c0/cbde8deb1926756b02ba6d5c4b400a54e8a7636669fb74f4350138091a31d58df8423cf7b2b16315a5118476d3789aca2942eab37c66adfeb7ec209bb61566cf
  languageName: node
  linkType: hard

"rollup-plugin-node-externals@npm:^8.0.0":
  version: 8.0.1
  resolution: "rollup-plugin-node-externals@npm:8.0.1"
  peerDependencies:
    rollup: ^4.0.0
  checksum: 10c0/ef8f9540e64264f3aa3c0e7fc984d8df554c7189c7c305aeb552a640209924813d70e1cda08f723a0395d33e66db5d1a5f40c2b55b8737d44ce92891483a630a
  languageName: node
  linkType: hard

"rollup@npm:^4.43.0":
  version: 4.44.0
  resolution: "rollup@npm:4.44.0"
  dependencies:
    "@rollup/rollup-android-arm-eabi": "npm:4.44.0"
    "@rollup/rollup-android-arm64": "npm:4.44.0"
    "@rollup/rollup-darwin-arm64": "npm:4.44.0"
    "@rollup/rollup-darwin-x64": "npm:4.44.0"
    "@rollup/rollup-freebsd-arm64": "npm:4.44.0"
    "@rollup/rollup-freebsd-x64": "npm:4.44.0"
    "@rollup/rollup-linux-arm-gnueabihf": "npm:4.44.0"
    "@rollup/rollup-linux-arm-musleabihf": "npm:4.44.0"
    "@rollup/rollup-linux-arm64-gnu": "npm:4.44.0"
    "@rollup/rollup-linux-arm64-musl": "npm:4.44.0"
    "@rollup/rollup-linux-loongarch64-gnu": "npm:4.44.0"
    "@rollup/rollup-linux-powerpc64le-gnu": "npm:4.44.0"
    "@rollup/rollup-linux-riscv64-gnu": "npm:4.44.0"
    "@rollup/rollup-linux-riscv64-musl": "npm:4.44.0"
    "@rollup/rollup-linux-s390x-gnu": "npm:4.44.0"
    "@rollup/rollup-linux-x64-gnu": "npm:4.44.0"
    "@rollup/rollup-linux-x64-musl": "npm:4.44.0"
    "@rollup/rollup-win32-arm64-msvc": "npm:4.44.0"
    "@rollup/rollup-win32-ia32-msvc": "npm:4.44.0"
    "@rollup/rollup-win32-x64-msvc": "npm:4.44.0"
    "@types/estree": "npm:1.0.8"
    fsevents: "npm:~2.3.2"
  dependenciesMeta:
    "@rollup/rollup-android-arm-eabi":
      optional: true
    "@rollup/rollup-android-arm64":
      optional: true
    "@rollup/rollup-darwin-arm64":
      optional: true
    "@rollup/rollup-darwin-x64":
      optional: true
    "@rollup/rollup-freebsd-arm64":
      optional: true
    "@rollup/rollup-freebsd-x64":
      optional: true
    "@rollup/rollup-linux-arm-gnueabihf":
      optional: true
    "@rollup/rollup-linux-arm-musleabihf":
      optional: true
    "@rollup/rollup-linux-arm64-gnu":
      optional: true
    "@rollup/rollup-linux-arm64-musl":
      optional: true
    "@rollup/rollup-linux-loongarch64-gnu":
      optional: true
    "@rollup/rollup-linux-powerpc64le-gnu":
      optional: true
    "@rollup/rollup-linux-riscv64-gnu":
      optional: true
    "@rollup/rollup-linux-riscv64-musl":
      optional: true
    "@rollup/rollup-linux-s390x-gnu":
      optional: true
    "@rollup/rollup-linux-x64-gnu":
      optional: true
    "@rollup/rollup-linux-x64-musl":
      optional: true
    "@rollup/rollup-win32-arm64-msvc":
      optional: true
    "@rollup/rollup-win32-ia32-msvc":
      optional: true
    "@rollup/rollup-win32-x64-msvc":
      optional: true
    fsevents:
      optional: true
  bin:
    rollup: dist/bin/rollup
  checksum: 10c0/ff3e0741f2fc7b7b183079628cf50fcfc9163bef86ecfbc9f4e4023adfdee375b7075940963514e2bc4969764688d38d67095bce038b0ad5d572207f114afff5
  languageName: node
  linkType: hard

"router@npm:^2.2.0":
  version: 2.2.0
  resolution: "router@npm:2.2.0"
  dependencies:
    debug: "npm:^4.4.0"
    depd: "npm:^2.0.0"
    is-promise: "npm:^4.0.0"
    parseurl: "npm:^1.3.3"
    path-to-regexp: "npm:^8.0.0"
  checksum: 10c0/3279de7450c8eae2f6e095e9edacbdeec0abb5cb7249c6e719faa0db2dba43574b4fff5892d9220631c9abaff52dd3cad648cfea2aaace845e1a071915ac8867
  languageName: node
  linkType: hard

"run-applescript@npm:^7.0.0":
  version: 7.0.0
  resolution: "run-applescript@npm:7.0.0"
  checksum: 10c0/bd821bbf154b8e6c8ecffeaf0c33cebbb78eb2987476c3f6b420d67ab4c5301faa905dec99ded76ebb3a7042b4e440189ae6d85bbbd3fc6e8d493347ecda8bfe
  languageName: node
  linkType: hard

"run-exclusive@npm:^2.2.19":
  version: 2.2.19
  resolution: "run-exclusive@npm:2.2.19"
  dependencies:
    minimal-polyfills: "npm:^2.2.3"
  checksum: 10c0/afde039737672d2490df12f6b5f151a258673abcfc37829a67e7a877c6e7402f0bb05372319d86142ddbe95c32170ac28919b6689ddd84ee6d3d0d9a1194bc94
  languageName: node
  linkType: hard

"run-parallel@npm:^1.1.9":
  version: 1.2.0
  resolution: "run-parallel@npm:1.2.0"
  dependencies:
    queue-microtask: "npm:^1.2.2"
  checksum: 10c0/200b5ab25b5b8b7113f9901bfe3afc347e19bb7475b267d55ad0eb86a62a46d77510cb0f232507c9e5d497ebda569a08a9867d0d14f57a82ad5564d991588b39
  languageName: node
  linkType: hard

"rxjs@npm:7.8.1":
  version: 7.8.1
  resolution: "rxjs@npm:7.8.1"
  dependencies:
    tslib: "npm:^2.1.0"
  checksum: 10c0/3c49c1ecd66170b175c9cacf5cef67f8914dcbc7cd0162855538d365c83fea631167cacb644b3ce533b2ea0e9a4d0b12175186985f89d75abe73dbd8f7f06f68
  languageName: node
  linkType: hard

"rxjs@npm:^7.8.1":
  version: 7.8.2
  resolution: "rxjs@npm:7.8.2"
  dependencies:
    tslib: "npm:^2.1.0"
  checksum: 10c0/1fcd33d2066ada98ba8f21fcbbcaee9f0b271de1d38dc7f4e256bfbc6ffcdde68c8bfb69093de7eeb46f24b1fb820620bf0223706cff26b4ab99a7ff7b2e2c45
  languageName: node
  linkType: hard

"safe-buffer@npm:5.2.1, safe-buffer@npm:~5.2.0":
  version: 5.2.1
  resolution: "safe-buffer@npm:5.2.1"
  checksum: 10c0/6501914237c0a86e9675d4e51d89ca3c21ffd6a31642efeba25ad65720bce6921c9e7e974e5be91a786b25aa058b5303285d3c15dbabf983a919f5f630d349f3
  languageName: node
  linkType: hard

"safe-stable-stringify@npm:^2.3.1":
  version: 2.5.0
  resolution: "safe-stable-stringify@npm:2.5.0"
  checksum: 10c0/baea14971858cadd65df23894a40588ed791769db21bafb7fd7608397dbdce9c5aac60748abae9995e0fc37e15f2061980501e012cd48859740796bea2987f49
  languageName: node
  linkType: hard

"safer-buffer@npm:>= 2.1.2 < 3, safer-buffer@npm:>= 2.1.2 < 3.0.0":
  version: 2.1.2
  resolution: "safer-buffer@npm:2.1.2"
  checksum: 10c0/7e3c8b2e88a1841c9671094bbaeebd94448111dd90a81a1f606f3f67708a6ec57763b3b47f06da09fc6054193e0e6709e77325415dc8422b04497a8070fa02d4
  languageName: node
  linkType: hard

"secure-json-parse@npm:^2.4.0, secure-json-parse@npm:^2.7.0":
  version: 2.7.0
  resolution: "secure-json-parse@npm:2.7.0"
  checksum: 10c0/f57eb6a44a38a3eeaf3548228585d769d788f59007454214fab9ed7f01fbf2e0f1929111da6db28cf0bcc1a2e89db5219a59e83eeaec3a54e413a0197ce879e4
  languageName: node
  linkType: hard

"semver@npm:^6.3.0, semver@npm:^6.3.1":
  version: 6.3.1
  resolution: "semver@npm:6.3.1"
  bin:
    semver: bin/semver.js
  checksum: 10c0/e3d79b609071caa78bcb6ce2ad81c7966a46a7431d9d58b8800cfa9cb6a63699b3899a0e4bcce36167a284578212d9ae6942b6929ba4aa5015c079a67751d42d
  languageName: node
  linkType: hard

"semver@npm:^7.0.0, semver@npm:^7.3.5, semver@npm:^7.5.2, semver@npm:^7.5.3, semver@npm:^7.5.4, semver@npm:^7.7.2":
  version: 7.7.2
  resolution: "semver@npm:7.7.2"
  bin:
    semver: bin/semver.js
  checksum: 10c0/aca305edfbf2383c22571cb7714f48cadc7ac95371b4b52362fb8eeffdfbc0de0669368b82b2b15978f8848f01d7114da65697e56cd8c37b0dab8c58e543f9ea
  languageName: node
  linkType: hard

"send@npm:0.19.0":
  version: 0.19.0
  resolution: "send@npm:0.19.0"
  dependencies:
    debug: "npm:2.6.9"
    depd: "npm:2.0.0"
    destroy: "npm:1.2.0"
    encodeurl: "npm:~1.0.2"
    escape-html: "npm:~1.0.3"
    etag: "npm:~1.8.1"
    fresh: "npm:0.5.2"
    http-errors: "npm:2.0.0"
    mime: "npm:1.6.0"
    ms: "npm:2.1.3"
    on-finished: "npm:2.4.1"
    range-parser: "npm:~1.2.1"
    statuses: "npm:2.0.1"
  checksum: 10c0/ea3f8a67a8f0be3d6bf9080f0baed6d2c51d11d4f7b4470de96a5029c598a7011c497511ccc28968b70ef05508675cebff27da9151dd2ceadd60be4e6cf845e3
  languageName: node
  linkType: hard

"send@npm:^1.1.0, send@npm:^1.2.0":
  version: 1.2.0
  resolution: "send@npm:1.2.0"
  dependencies:
    debug: "npm:^4.3.5"
    encodeurl: "npm:^2.0.0"
    escape-html: "npm:^1.0.3"
    etag: "npm:^1.8.1"
    fresh: "npm:^2.0.0"
    http-errors: "npm:^2.0.0"
    mime-types: "npm:^3.0.1"
    ms: "npm:^2.1.3"
    on-finished: "npm:^2.4.1"
    range-parser: "npm:^1.2.1"
    statuses: "npm:^2.0.1"
  checksum: 10c0/531bcfb5616948d3468d95a1fd0adaeb0c20818ba4a500f439b800ca2117971489e02074ce32796fd64a6772ea3e7235fe0583d8241dbd37a053dc3378eff9a5
  languageName: node
  linkType: hard

"serve-static@npm:1.16.2":
  version: 1.16.2
  resolution: "serve-static@npm:1.16.2"
  dependencies:
    encodeurl: "npm:~2.0.0"
    escape-html: "npm:~1.0.3"
    parseurl: "npm:~1.3.3"
    send: "npm:0.19.0"
  checksum: 10c0/528fff6f5e12d0c5a391229ad893910709bc51b5705962b09404a1d813857578149b8815f35d3ee5752f44cd378d0f31669d4b1d7e2d11f41e08283d5134bd1f
  languageName: node
  linkType: hard

"serve-static@npm:^2.2.0":
  version: 2.2.0
  resolution: "serve-static@npm:2.2.0"
  dependencies:
    encodeurl: "npm:^2.0.0"
    escape-html: "npm:^1.0.3"
    parseurl: "npm:^1.3.3"
    send: "npm:^1.2.0"
  checksum: 10c0/30e2ed1dbff1984836cfd0c65abf5d3f3f83bcd696c99d2d3c97edbd4e2a3ff4d3f87108a7d713640d290a7b6fe6c15ddcbc61165ab2eaad48ea8d3b52c7f913
  languageName: node
  linkType: hard

"setprototypeof@npm:1.2.0":
  version: 1.2.0
  resolution: "setprototypeof@npm:1.2.0"
  checksum: 10c0/68733173026766fa0d9ecaeb07f0483f4c2dc70ca376b3b7c40b7cda909f94b0918f6c5ad5ce27a9160bdfb475efaa9d5e705a11d8eaae18f9835d20976028bc
  languageName: node
  linkType: hard

"shebang-command@npm:^2.0.0":
  version: 2.0.0
  resolution: "shebang-command@npm:2.0.0"
  dependencies:
    shebang-regex: "npm:^3.0.0"
  checksum: 10c0/a41692e7d89a553ef21d324a5cceb5f686d1f3c040759c50aab69688634688c5c327f26f3ecf7001ebfd78c01f3c7c0a11a7c8bfd0a8bc9f6240d4f40b224e4e
  languageName: node
  linkType: hard

"shebang-regex@npm:^3.0.0":
  version: 3.0.0
  resolution: "shebang-regex@npm:3.0.0"
  checksum: 10c0/1dbed0726dd0e1152a92696c76c7f06084eb32a90f0528d11acd764043aacf76994b2fb30aa1291a21bd019d6699164d048286309a278855ee7bec06cf6fb690
  languageName: node
  linkType: hard

"shell-quote@npm:^1.8.1, shell-quote@npm:^1.8.3":
  version: 1.8.3
  resolution: "shell-quote@npm:1.8.3"
  checksum: 10c0/bee87c34e1e986cfb4c30846b8e6327d18874f10b535699866f368ade11ea4ee45433d97bf5eada22c4320c27df79c3a6a7eb1bf3ecfc47f2c997d9e5e2672fd
  languageName: node
  linkType: hard

"shiki@npm:^1.29.2":
  version: 1.29.2
  resolution: "shiki@npm:1.29.2"
  dependencies:
    "@shikijs/core": "npm:1.29.2"
    "@shikijs/engine-javascript": "npm:1.29.2"
    "@shikijs/engine-oniguruma": "npm:1.29.2"
    "@shikijs/langs": "npm:1.29.2"
    "@shikijs/themes": "npm:1.29.2"
    "@shikijs/types": "npm:1.29.2"
    "@shikijs/vscode-textmate": "npm:^10.0.1"
    "@types/hast": "npm:^3.0.4"
  checksum: 10c0/9ef452021582c405501077082c4ae8d877027dca6488d2c7a1963ed661567f121b4cc5dea9dfab26689504b612b8a961f3767805cbeaaae3c1d6faa5e6f37eb0
  languageName: node
  linkType: hard

"shimmer@npm:^1.2.1":
  version: 1.2.1
  resolution: "shimmer@npm:1.2.1"
  checksum: 10c0/ae8b27c389db2a00acfc8da90240f11577685a8f3e40008f826a3bea8b4f3b3ecd305c26be024b4a0fd3b123d132c1569d6e238097960a9a543b6c60760fb46a
  languageName: node
  linkType: hard

"side-channel-list@npm:^1.0.0":
  version: 1.0.0
  resolution: "side-channel-list@npm:1.0.0"
  dependencies:
    es-errors: "npm:^1.3.0"
    object-inspect: "npm:^1.13.3"
  checksum: 10c0/644f4ac893456c9490ff388bf78aea9d333d5e5bfc64cfb84be8f04bf31ddc111a8d4b83b85d7e7e8a7b845bc185a9ad02c052d20e086983cf59f0be517d9b3d
  languageName: node
  linkType: hard

"side-channel-map@npm:^1.0.1":
  version: 1.0.1
  resolution: "side-channel-map@npm:1.0.1"
  dependencies:
    call-bound: "npm:^1.0.2"
    es-errors: "npm:^1.3.0"
    get-intrinsic: "npm:^1.2.5"
    object-inspect: "npm:^1.13.3"
  checksum: 10c0/010584e6444dd8a20b85bc926d934424bd809e1a3af941cace229f7fdcb751aada0fb7164f60c2e22292b7fa3c0ff0bce237081fd4cdbc80de1dc68e95430672
  languageName: node
  linkType: hard

"side-channel-weakmap@npm:^1.0.2":
  version: 1.0.2
  resolution: "side-channel-weakmap@npm:1.0.2"
  dependencies:
    call-bound: "npm:^1.0.2"
    es-errors: "npm:^1.3.0"
    get-intrinsic: "npm:^1.2.5"
    object-inspect: "npm:^1.13.3"
    side-channel-map: "npm:^1.0.1"
  checksum: 10c0/71362709ac233e08807ccd980101c3e2d7efe849edc51455030327b059f6c4d292c237f94dc0685031dd11c07dd17a68afde235d6cf2102d949567f98ab58185
  languageName: node
  linkType: hard

"side-channel@npm:^1.0.4, side-channel@npm:^1.0.6, side-channel@npm:^1.1.0":
  version: 1.1.0
  resolution: "side-channel@npm:1.1.0"
  dependencies:
    es-errors: "npm:^1.3.0"
    object-inspect: "npm:^1.13.3"
    side-channel-list: "npm:^1.0.0"
    side-channel-map: "npm:^1.0.1"
    side-channel-weakmap: "npm:^1.0.2"
  checksum: 10c0/cb20dad41eb032e6c24c0982e1e5a24963a28aa6122b4f05b3f3d6bf8ae7fd5474ef382c8f54a6a3ab86e0cac4d41a23bd64ede3970e5bfb50326ba02a7996e6
  languageName: node
  linkType: hard

"sift@npm:^17.1.3":
  version: 17.1.3
  resolution: "sift@npm:17.1.3"
  checksum: 10c0/bb05d1d65cc9b549b402c1366ba1fcf685311808b6d5c2f4fa2f477d7b524218bbf6c99587562d5613d407820a6b5a7cad809f89c3f75c513ff5d8c0e0a0cead
  languageName: node
  linkType: hard

"signal-exit@npm:^3.0.3, signal-exit@npm:^3.0.7":
  version: 3.0.7
  resolution: "signal-exit@npm:3.0.7"
  checksum: 10c0/25d272fa73e146048565e08f3309d5b942c1979a6f4a58a8c59d5fa299728e9c2fcd1a759ec870863b1fd38653670240cd420dad2ad9330c71f36608a6a1c912
  languageName: node
  linkType: hard

"signal-exit@npm:^4.0.1, signal-exit@npm:^4.1.0":
  version: 4.1.0
  resolution: "signal-exit@npm:4.1.0"
  checksum: 10c0/41602dce540e46d599edba9d9860193398d135f7ff72cab629db5171516cfae628d21e7bfccde1bbfdf11c48726bc2a6d1a8fb8701125852fbfda7cf19c6aa83
  languageName: node
  linkType: hard

"simple-update-notifier@npm:^2.0.0":
  version: 2.0.0
  resolution: "simple-update-notifier@npm:2.0.0"
  dependencies:
    semver: "npm:^7.5.3"
  checksum: 10c0/2a00bd03bfbcbf8a737c47ab230d7920f8bfb92d1159d421bdd194479f6d01ebc995d13fbe13d45dace23066a78a3dc6642999b4e3b38b847e6664191575b20c
  languageName: node
  linkType: hard

"sisteransi@npm:^1.0.5":
  version: 1.0.5
  resolution: "sisteransi@npm:1.0.5"
  checksum: 10c0/230ac975cca485b7f6fe2b96a711aa62a6a26ead3e6fb8ba17c5a00d61b8bed0d7adc21f5626b70d7c33c62ff4e63933017a6462942c719d1980bb0b1207ad46
  languageName: node
  linkType: hard

"slash@npm:^3.0.0":
  version: 3.0.0
  resolution: "slash@npm:3.0.0"
  checksum: 10c0/e18488c6a42bdfd4ac5be85b2ced3ccd0224773baae6ad42cfbb9ec74fc07f9fa8396bd35ee638084ead7a2a0818eb5e7151111544d4731ce843019dab4be47b
  languageName: node
  linkType: hard

"slash@npm:^5.1.0":
  version: 5.1.0
  resolution: "slash@npm:5.1.0"
  checksum: 10c0/eb48b815caf0bdc390d0519d41b9e0556a14380f6799c72ba35caf03544d501d18befdeeef074bc9c052acf69654bc9e0d79d7f1de0866284137a40805299eb3
  languageName: node
  linkType: hard

"slug@npm:^6.0.0":
  version: 6.1.0
  resolution: "slug@npm:6.1.0"
  checksum: 10c0/b79650a3b38de94dc6063d73858172d1955fcfb4731746c8f77e87dded9a092c4423205e962fb31ff5332557fcd19cc2eb5331ddd854c053f91748022ed64449
  languageName: node
  linkType: hard

"smart-buffer@npm:^4.2.0":
  version: 4.2.0
  resolution: "smart-buffer@npm:4.2.0"
  checksum: 10c0/a16775323e1404dd43fabafe7460be13a471e021637bc7889468eb45ce6a6b207261f454e4e530a19500cc962c4cc5348583520843b363f4193cee5c00e1e539
  languageName: node
  linkType: hard

"socket.io-client@npm:4.7.5":
  version: 4.7.5
  resolution: "socket.io-client@npm:4.7.5"
  dependencies:
    "@socket.io/component-emitter": "npm:~3.1.0"
    debug: "npm:~4.3.2"
    engine.io-client: "npm:~6.5.2"
    socket.io-parser: "npm:~4.2.4"
  checksum: 10c0/d5dc90ee63755fbbb0a1cb3faf575c9ce20d98e809a43a4c9c3ce03a56b8810335ae38e678ceb0650ac434d55e72ea6449c2e5d6db8bc7258f7c529148fac99d
  languageName: node
  linkType: hard

"socket.io-parser@npm:~4.2.4":
  version: 4.2.4
  resolution: "socket.io-parser@npm:4.2.4"
  dependencies:
    "@socket.io/component-emitter": "npm:~3.1.0"
    debug: "npm:~4.3.1"
  checksum: 10c0/9383b30358fde4a801ea4ec5e6860915c0389a091321f1c1f41506618b5cf7cd685d0a31c587467a0c4ee99ef98c2b99fb87911f9dfb329716c43b587f29ca48
  languageName: node
  linkType: hard

"socks-proxy-agent@npm:^8.0.3":
  version: 8.0.5
  resolution: "socks-proxy-agent@npm:8.0.5"
  dependencies:
    agent-base: "npm:^7.1.2"
    debug: "npm:^4.3.4"
    socks: "npm:^2.8.3"
  checksum: 10c0/5d2c6cecba6821389aabf18728325730504bf9bb1d9e342e7987a5d13badd7a98838cc9a55b8ed3cb866ad37cc23e1086f09c4d72d93105ce9dfe76330e9d2a6
  languageName: node
  linkType: hard

"socks@npm:^2.8.3":
  version: 2.8.5
  resolution: "socks@npm:2.8.5"
  dependencies:
    ip-address: "npm:^9.0.5"
    smart-buffer: "npm:^4.2.0"
  checksum: 10c0/e427d0eb0451cfd04e20b9156ea8c0e9b5e38a8d70f21e55c30fbe4214eda37cfc25d782c63f9adc5fbdad6d062a0f127ef2cefc9a44b6fee2b9ea5d1ed10827
  languageName: node
  linkType: hard

"sonic-boom@npm:^4.0.1":
  version: 4.2.0
  resolution: "sonic-boom@npm:4.2.0"
  dependencies:
    atomic-sleep: "npm:^1.0.0"
  checksum: 10c0/ae897e6c2cd6d3cb7cdcf608bc182393b19c61c9413a85ce33ffd25891485589f39bece0db1de24381d0a38fc03d08c9862ded0c60f184f1b852f51f97af9684
  languageName: node
  linkType: hard

"source-map-support@npm:0.5.13":
  version: 0.5.13
  resolution: "source-map-support@npm:0.5.13"
  dependencies:
    buffer-from: "npm:^1.0.0"
    source-map: "npm:^0.6.0"
  checksum: 10c0/137539f8c453fa0f496ea42049ab5da4569f96781f6ac8e5bfda26937be9494f4e8891f523c5f98f0e85f71b35d74127a00c46f83f6a4f54672b58d53202565e
  languageName: node
  linkType: hard

"source-map@npm:^0.6.0, source-map@npm:^0.6.1":
  version: 0.6.1
  resolution: "source-map@npm:0.6.1"
  checksum: 10c0/ab55398007c5e5532957cb0beee2368529618ac0ab372d789806f5718123cc4367d57de3904b4e6a4170eb5a0b0f41373066d02ca0735a0c4d75c7d328d3e011
  languageName: node
  linkType: hard

"space-separated-tokens@npm:^2.0.0":
  version: 2.0.2
  resolution: "space-separated-tokens@npm:2.0.2"
  checksum: 10c0/6173e1d903dca41dcab6a2deed8b4caf61bd13b6d7af8374713500570aa929ff9414ae09a0519f4f8772df993300305a395d4871f35bc4ca72b6db57e1f30af8
  languageName: node
  linkType: hard

"spawn-command@npm:0.0.2":
  version: 0.0.2
  resolution: "spawn-command@npm:0.0.2"
  checksum: 10c0/b22f2d71239e6e628a400831861ba747750bbb40c0a53323754cf7b84330b73d81e40ff1f9055e6d1971818679510208a9302e13d9ff3b32feb67e74d7a1b3ef
  languageName: node
  linkType: hard

"spex@npm:3.4.1":
  version: 3.4.1
  resolution: "spex@npm:3.4.1"
  checksum: 10c0/eb8be72c6d795c789b63a240167f731f6d1f6656c26efaacdae91340ac8607c22565cd7bd152ea8e8670f2fb3940ebd0ed01b7a4ab2b948b865ee18438f016ee
  languageName: node
  linkType: hard

"split2@npm:^4.0.0, split2@npm:^4.1.0":
  version: 4.2.0
  resolution: "split2@npm:4.2.0"
  checksum: 10c0/b292beb8ce9215f8c642bb68be6249c5a4c7f332fc8ecadae7be5cbdf1ea95addc95f0459ef2e7ad9d45fd1064698a097e4eb211c83e772b49bc0ee423e91534
  languageName: node
  linkType: hard

"sprintf-js@npm:^1.1.3":
  version: 1.1.3
  resolution: "sprintf-js@npm:1.1.3"
  checksum: 10c0/09270dc4f30d479e666aee820eacd9e464215cdff53848b443964202bf4051490538e5dd1b42e1a65cf7296916ca17640aebf63dae9812749c7542ee5f288dec
  languageName: node
  linkType: hard

"sprintf-js@npm:~1.0.2":
  version: 1.0.3
  resolution: "sprintf-js@npm:1.0.3"
  checksum: 10c0/ecadcfe4c771890140da5023d43e190b7566d9cf8b2d238600f31bec0fc653f328da4450eb04bd59a431771a8e9cc0e118f0aa3974b683a4981b4e07abc2a5bb
  languageName: node
  linkType: hard

"ssri@npm:^12.0.0":
  version: 12.0.0
  resolution: "ssri@npm:12.0.0"
  dependencies:
    minipass: "npm:^7.0.3"
  checksum: 10c0/caddd5f544b2006e88fa6b0124d8d7b28208b83c72d7672d5ade44d794525d23b540f3396108c4eb9280dcb7c01f0bef50682f5b4b2c34291f7c5e211fd1417d
  languageName: node
  linkType: hard

"stack-trace@npm:0.0.x":
  version: 0.0.10
  resolution: "stack-trace@npm:0.0.10"
  checksum: 10c0/9ff3dabfad4049b635a85456f927a075c9d0c210e3ea336412d18220b2a86cbb9b13ec46d6c37b70a302a4ea4d49e30e5d4944dd60ae784073f1cde778ac8f4b
  languageName: node
  linkType: hard

"stack-utils@npm:^2.0.3":
  version: 2.0.6
  resolution: "stack-utils@npm:2.0.6"
  dependencies:
    escape-string-regexp: "npm:^2.0.0"
  checksum: 10c0/651c9f87667e077584bbe848acaecc6049bc71979f1e9a46c7b920cad4431c388df0f51b8ad7cfd6eed3db97a2878d0fc8b3122979439ea8bac29c61c95eec8a
  languageName: node
  linkType: hard

"standard-as-callback@npm:^2.1.0":
  version: 2.1.0
  resolution: "standard-as-callback@npm:2.1.0"
  checksum: 10c0/012677236e3d3fdc5689d29e64ea8a599331c4babe86956bf92fc5e127d53f85411c5536ee0079c52c43beb0026b5ce7aa1d834dd35dd026e82a15d1bcaead1f
  languageName: node
  linkType: hard

"statuses@npm:2.0.1":
  version: 2.0.1
  resolution: "statuses@npm:2.0.1"
  checksum: 10c0/34378b207a1620a24804ce8b5d230fea0c279f00b18a7209646d5d47e419d1cc23e7cbf33a25a1e51ac38973dc2ac2e1e9c647a8e481ef365f77668d72becfd0
  languageName: node
  linkType: hard

"statuses@npm:^2.0.1":
  version: 2.0.2
  resolution: "statuses@npm:2.0.2"
  checksum: 10c0/a9947d98ad60d01f6b26727570f3bcceb6c8fa789da64fe6889908fe2e294d57503b14bf2b5af7605c2d36647259e856635cd4c49eab41667658ec9d0080ec3f
  languageName: node
  linkType: hard

"streamsearch@npm:^1.1.0":
  version: 1.1.0
  resolution: "streamsearch@npm:1.1.0"
  checksum: 10c0/fbd9aecc2621364384d157f7e59426f4bfd385e8b424b5aaa79c83a6f5a1c8fd2e4e3289e95de1eb3511cb96bb333d6281a9919fafce760e4edb35b2cd2facab
  languageName: node
  linkType: hard

"string-length@npm:^4.0.1":
  version: 4.0.2
  resolution: "string-length@npm:4.0.2"
  dependencies:
    char-regex: "npm:^1.0.2"
    strip-ansi: "npm:^6.0.0"
  checksum: 10c0/1cd77409c3d7db7bc59406f6bcc9ef0783671dcbabb23597a1177c166906ef2ee7c8290f78cae73a8aec858768f189d2cb417797df5e15ec4eb5e16b3346340c
  languageName: node
  linkType: hard

"string-width-cjs@npm:string-width@^4.2.0, string-width@npm:^4.1.0, string-width@npm:^4.2.0, string-width@npm:^4.2.3":
  version: 4.2.3
  resolution: "string-width@npm:4.2.3"
  dependencies:
    emoji-regex: "npm:^8.0.0"
    is-fullwidth-code-point: "npm:^3.0.0"
    strip-ansi: "npm:^6.0.1"
  checksum: 10c0/1e525e92e5eae0afd7454086eed9c818ee84374bb80328fc41217ae72ff5f065ef1c9d7f72da41de40c75fa8bb3dee63d92373fd492c84260a552c636392a47b
  languageName: node
  linkType: hard

"string-width@npm:^5.0.1, string-width@npm:^5.1.2":
  version: 5.1.2
  resolution: "string-width@npm:5.1.2"
  dependencies:
    eastasianwidth: "npm:^0.2.0"
    emoji-regex: "npm:^9.2.2"
    strip-ansi: "npm:^7.0.1"
  checksum: 10c0/ab9c4264443d35b8b923cbdd513a089a60de339216d3b0ed3be3ba57d6880e1a192b70ae17225f764d7adbf5994e9bb8df253a944736c15a0240eff553c678ca
  languageName: node
  linkType: hard

"string_decoder@npm:^1.1.1, string_decoder@npm:^1.3.0":
  version: 1.3.0
  resolution: "string_decoder@npm:1.3.0"
  dependencies:
    safe-buffer: "npm:~5.2.0"
  checksum: 10c0/810614ddb030e271cd591935dcd5956b2410dd079d64ff92a1844d6b7588bf992b3e1b69b0f4d34a3e06e0bd73046ac646b5264c1987b20d0601f81ef35d731d
  languageName: node
  linkType: hard

"stringify-entities@npm:^4.0.0":
  version: 4.0.4
  resolution: "stringify-entities@npm:4.0.4"
  dependencies:
    character-entities-html4: "npm:^2.0.0"
    character-entities-legacy: "npm:^3.0.0"
  checksum: 10c0/537c7e656354192406bdd08157d759cd615724e9d0873602d2c9b2f6a5c0a8d0b1d73a0a08677848105c5eebac6db037b57c0b3a4ec86331117fa7319ed50448
  languageName: node
  linkType: hard

"strip-ansi-cjs@npm:strip-ansi@^6.0.1, strip-ansi@npm:^6.0.0, strip-ansi@npm:^6.0.1":
  version: 6.0.1
  resolution: "strip-ansi@npm:6.0.1"
  dependencies:
    ansi-regex: "npm:^5.0.1"
  checksum: 10c0/1ae5f212a126fe5b167707f716942490e3933085a5ff6c008ab97ab2f272c8025d3aa218b7bd6ab25729ca20cc81cddb252102f8751e13482a5199e873680952
  languageName: node
  linkType: hard

"strip-ansi@npm:^7.0.1":
  version: 7.1.0
  resolution: "strip-ansi@npm:7.1.0"
  dependencies:
    ansi-regex: "npm:^6.0.1"
  checksum: 10c0/a198c3762e8832505328cbf9e8c8381de14a4fa50a4f9b2160138158ea88c0f5549fb50cb13c651c3088f47e63a108b34622ec18c0499b6c8c3a5ddf6b305ac4
  languageName: node
  linkType: hard

"strip-bom@npm:^4.0.0":
  version: 4.0.0
  resolution: "strip-bom@npm:4.0.0"
  checksum: 10c0/26abad1172d6bc48985ab9a5f96c21e440f6e7e476686de49be813b5a59b3566dccb5c525b831ec54fe348283b47f3ffb8e080bc3f965fde12e84df23f6bb7ef
  languageName: node
  linkType: hard

"strip-final-newline@npm:^2.0.0":
  version: 2.0.0
  resolution: "strip-final-newline@npm:2.0.0"
  checksum: 10c0/bddf8ccd47acd85c0e09ad7375409d81653f645fda13227a9d459642277c253d877b68f2e5e4d819fe75733b0e626bac7e954c04f3236f6d196f79c94fa4a96f
  languageName: node
  linkType: hard

"strip-final-newline@npm:^3.0.0":
  version: 3.0.0
  resolution: "strip-final-newline@npm:3.0.0"
  checksum: 10c0/a771a17901427bac6293fd416db7577e2bc1c34a19d38351e9d5478c3c415f523f391003b42ed475f27e33a78233035df183525395f731d3bfb8cdcbd4da08ce
  languageName: node
  linkType: hard

"strip-final-newline@npm:^4.0.0":
  version: 4.0.0
  resolution: "strip-final-newline@npm:4.0.0"
  checksum: 10c0/b0cf2b62d597a1b0e3ebc42b88767f0a0d45601f89fd379a928a1812c8779440c81abba708082c946445af1d6b62d5f16e2a7cf4f30d9d6587b89425fae801ff
  languageName: node
  linkType: hard

"strip-json-comments@npm:^3.1.1":
  version: 3.1.1
  resolution: "strip-json-comments@npm:3.1.1"
  checksum: 10c0/9681a6257b925a7fa0f285851c0e613cc934a50661fa7bb41ca9cbbff89686bb4a0ee366e6ecedc4daafd01e83eee0720111ab294366fe7c185e935475ebcecd
  languageName: node
  linkType: hard

"strip-json-comments@npm:^5.0.2":
  version: 5.0.2
  resolution: "strip-json-comments@npm:5.0.2"
  checksum: 10c0/e9841b8face78a01b0eb66f81e0a3419186a96f1d26817a5e1f5260b0631c10e0a7f711dddc5988edf599e5c079e4dd6e91defd21523e556636ba5679786f5ac
  languageName: node
  linkType: hard

"strnum@npm:^1.0.5":
  version: 1.1.2
  resolution: "strnum@npm:1.1.2"
  checksum: 10c0/a0fce2498fa3c64ce64a40dada41beb91cabe3caefa910e467dc0518ef2ebd7e4d10f8c2202a6104f1410254cae245066c0e94e2521fb4061a5cb41831952392
  languageName: node
  linkType: hard

"superagent@npm:^10.2.1":
  version: 10.2.1
  resolution: "superagent@npm:10.2.1"
  dependencies:
    component-emitter: "npm:^1.3.0"
    cookiejar: "npm:^2.1.4"
    debug: "npm:^4.3.4"
    fast-safe-stringify: "npm:^2.1.1"
    form-data: "npm:^4.0.0"
    formidable: "npm:^3.5.4"
    methods: "npm:^1.1.2"
    mime: "npm:2.6.0"
    qs: "npm:^6.11.0"
  checksum: 10c0/526e3716f765873fc2f98a00fe0c8cbfe57976c501a30486307eefe54a6a5e379a0adf2ca8b29d40bc33a34d6baf7814972b0398b6002445ca3b4af07487f090
  languageName: node
  linkType: hard

"superjson@npm:^2.2.1, superjson@npm:^2.2.2":
  version: 2.2.2
  resolution: "superjson@npm:2.2.2"
  dependencies:
    copy-anything: "npm:^3.0.2"
  checksum: 10c0/aa49ebe6653e963020bc6a1ed416d267dfda84cfcc3cbd3beffd75b72e44eb9df7327215f3e3e77528f6e19ad8895b16a4964fdcd56d1799d14350db8c92afbc
  languageName: node
  linkType: hard

"supertest@npm:^7.1.0":
  version: 7.1.1
  resolution: "supertest@npm:7.1.1"
  dependencies:
    methods: "npm:^1.1.2"
    superagent: "npm:^10.2.1"
  checksum: 10c0/d5ca56e5a78b145bcfb44c0d9518bdcfce4657b62d0b4c37561c772a8a21b91a6a7c63832e73764484dfc32e4183e6e42028595d539028c05458e8d9867baf75
  languageName: node
  linkType: hard

"supports-color@npm:^5.5.0":
  version: 5.5.0
  resolution: "supports-color@npm:5.5.0"
  dependencies:
    has-flag: "npm:^3.0.0"
  checksum: 10c0/6ae5ff319bfbb021f8a86da8ea1f8db52fac8bd4d499492e30ec17095b58af11f0c55f8577390a749b1c4dde691b6a0315dab78f5f54c9b3d83f8fb5905c1c05
  languageName: node
  linkType: hard

"supports-color@npm:^7.0.0, supports-color@npm:^7.1.0":
  version: 7.2.0
  resolution: "supports-color@npm:7.2.0"
  dependencies:
    has-flag: "npm:^4.0.0"
  checksum: 10c0/afb4c88521b8b136b5f5f95160c98dee7243dc79d5432db7efc27efb219385bbc7d9427398e43dd6cc730a0f87d5085ce1652af7efbe391327bc0a7d0f7fc124
  languageName: node
  linkType: hard

"supports-color@npm:^8.0.0, supports-color@npm:^8.1.1":
  version: 8.1.1
  resolution: "supports-color@npm:8.1.1"
  dependencies:
    has-flag: "npm:^4.0.0"
  checksum: 10c0/ea1d3c275dd604c974670f63943ed9bd83623edc102430c05adb8efc56ba492746b6e95386e7831b872ec3807fd89dd8eb43f735195f37b5ec343e4234cc7e89
  languageName: node
  linkType: hard

"supports-hyperlinks@npm:^2.2.0":
  version: 2.3.0
  resolution: "supports-hyperlinks@npm:2.3.0"
  dependencies:
    has-flag: "npm:^4.0.0"
    supports-color: "npm:^7.0.0"
  checksum: 10c0/4057f0d86afb056cd799602f72d575b8fdd79001c5894bcb691176f14e870a687e7981e50bc1484980e8b688c6d5bcd4931e1609816abb5a7dc1486b7babf6a1
  languageName: node
  linkType: hard

"supports-preserve-symlinks-flag@npm:^1.0.0":
  version: 1.0.0
  resolution: "supports-preserve-symlinks-flag@npm:1.0.0"
  checksum: 10c0/6c4032340701a9950865f7ae8ef38578d8d7053f5e10518076e6554a9381fa91bd9c6850193695c141f32b21f979c985db07265a758867bac95de05f7d8aeb39
  languageName: node
  linkType: hard

"swr@npm:^2.2.5, swr@npm:^2.3.3":
  version: 2.3.3
  resolution: "swr@npm:2.3.3"
  dependencies:
    dequal: "npm:^2.0.3"
    use-sync-external-store: "npm:^1.4.0"
  peerDependencies:
    react: ^16.11.0 || ^17.0.0 || ^18.0.0 || ^19.0.0
  checksum: 10c0/882fc8291912860e0c50eae3470ebf0cd58b0144cb12adcc4b14c5cef913ea06479043830508d8b0b3d4061d99ad8dd52485c9c879fbd4e9b893484e6d8da9e3
  languageName: node
  linkType: hard

"tar@npm:^7.4.3":
  version: 7.4.3
  resolution: "tar@npm:7.4.3"
  dependencies:
    "@isaacs/fs-minipass": "npm:^4.0.0"
    chownr: "npm:^3.0.0"
    minipass: "npm:^7.1.2"
    minizlib: "npm:^3.0.1"
    mkdirp: "npm:^3.0.1"
    yallist: "npm:^5.0.0"
  checksum: 10c0/d4679609bb2a9b48eeaf84632b6d844128d2412b95b6de07d53d8ee8baf4ca0857c9331dfa510390a0727b550fd543d4d1a10995ad86cdf078423fbb8d99831d
  languageName: node
  linkType: hard

"tcp-port-used@npm:^1.0.2":
  version: 1.0.2
  resolution: "tcp-port-used@npm:1.0.2"
  dependencies:
    debug: "npm:4.3.1"
    is2: "npm:^2.0.6"
  checksum: 10c0/a5fb29e35f1e452f1064e3671d02b6d65e7d9bffad98d8da688270b6ffdaa9a8351fe8321aedf131f3904af70b569d9c5f6d9fe75d57dda19c466abac2bc025a
  languageName: node
  linkType: hard

"terminal-link@npm:^3.0.0":
  version: 3.0.0
  resolution: "terminal-link@npm:3.0.0"
  dependencies:
    ansi-escapes: "npm:^5.0.0"
    supports-hyperlinks: "npm:^2.2.0"
  checksum: 10c0/2ccf93f474d9c4fe1ac75764a48836e61c281def08f4aff154696bc83dd764078ee2f5a6a6148382fb928943d53f44313ae513c5f457649d2961a95e5cd343b3
  languageName: node
  linkType: hard

"test-exclude@npm:^6.0.0":
  version: 6.0.0
  resolution: "test-exclude@npm:6.0.0"
  dependencies:
    "@istanbuljs/schema": "npm:^0.1.2"
    glob: "npm:^7.1.4"
    minimatch: "npm:^3.0.4"
  checksum: 10c0/019d33d81adff3f9f1bfcff18125fb2d3c65564f437d9be539270ee74b994986abb8260c7c2ce90e8f30162178b09dbbce33c6389273afac4f36069c48521f57
  languageName: node
  linkType: hard

"thread-stream@npm:^3.0.0":
  version: 3.1.0
  resolution: "thread-stream@npm:3.1.0"
  dependencies:
    real-require: "npm:^0.2.0"
  checksum: 10c0/c36118379940b77a6ef3e6f4d5dd31e97b8210c3f7b9a54eb8fe6358ab173f6d0acfaf69b9c3db024b948c0c5fd2a7df93e2e49151af02076b35ada3205ec9a6
  languageName: node
  linkType: hard

"throttleit@npm:2.1.0":
  version: 2.1.0
  resolution: "throttleit@npm:2.1.0"
  checksum: 10c0/1696ae849522cea6ba4f4f3beac1f6655d335e51b42d99215e196a718adced0069e48deaaf77f7e89f526ab31de5b5c91016027da182438e6f9280be2f3d5265
  languageName: node
  linkType: hard

"tinyglobby@npm:^0.2.12, tinyglobby@npm:^0.2.2":
  version: 0.2.14
  resolution: "tinyglobby@npm:0.2.14"
  dependencies:
    fdir: "npm:^6.4.4"
    picomatch: "npm:^4.0.2"
  checksum: 10c0/f789ed6c924287a9b7d3612056ed0cda67306cd2c80c249fd280cf1504742b12583a2089b61f4abbd24605f390809017240e250241f09938054c9b363e51c0a6
  languageName: node
  linkType: hard

"tmpl@npm:1.0.5":
  version: 1.0.5
  resolution: "tmpl@npm:1.0.5"
  checksum: 10c0/f935537799c2d1922cb5d6d3805f594388f75338fe7a4a9dac41504dd539704ca4db45b883b52e7b0aa5b2fd5ddadb1452bf95cd23a69da2f793a843f9451cc9
  languageName: node
  linkType: hard

"to-regex-range@npm:^5.0.1":
  version: 5.0.1
  resolution: "to-regex-range@npm:5.0.1"
  dependencies:
    is-number: "npm:^7.0.0"
  checksum: 10c0/487988b0a19c654ff3e1961b87f471702e708fa8a8dd02a298ef16da7206692e8552a0250e8b3e8759270f62e9d8314616f6da274734d3b558b1fc7b7724e892
  languageName: node
  linkType: hard

"toidentifier@npm:1.0.1":
  version: 1.0.1
  resolution: "toidentifier@npm:1.0.1"
  checksum: 10c0/93937279934bd66cc3270016dd8d0afec14fb7c94a05c72dc57321f8bd1fa97e5bea6d1f7c89e728d077ca31ea125b78320a616a6c6cd0e6b9cb94cb864381c1
  languageName: node
  linkType: hard

"touch@npm:^3.1.0":
  version: 3.1.1
  resolution: "touch@npm:3.1.1"
  bin:
    nodetouch: bin/nodetouch.js
  checksum: 10c0/d2e4d269a42c846a22a29065b9af0b263de58effc85a1764bb7a2e8fc4b47700e9e2fcbd7eb1f5bffbb7c73d860f93600cef282b93ddac8f0b62321cb498b36e
  languageName: node
  linkType: hard

"tr46@npm:~0.0.3":
  version: 0.0.3
  resolution: "tr46@npm:0.0.3"
  checksum: 10c0/047cb209a6b60c742f05c9d3ace8fa510bff609995c129a37ace03476a9b12db4dbf975e74600830ef0796e18882b2381fb5fb1f6b4f96b832c374de3ab91a11
  languageName: node
  linkType: hard

"tree-kill@npm:^1.2.2":
  version: 1.2.2
  resolution: "tree-kill@npm:1.2.2"
  bin:
    tree-kill: cli.js
  checksum: 10c0/7b1b7c7f17608a8f8d20a162e7957ac1ef6cd1636db1aba92f4e072dc31818c2ff0efac1e3d91064ede67ed5dc57c565420531a8134090a12ac10cf792ab14d2
  languageName: node
  linkType: hard

"trim-lines@npm:^3.0.0":
  version: 3.0.1
  resolution: "trim-lines@npm:3.0.1"
  checksum: 10c0/3a1611fa9e52aa56a94c69951a9ea15b8aaad760eaa26c56a65330dc8adf99cb282fc07cc9d94968b7d4d88003beba220a7278bbe2063328eb23fb56f9509e94
  languageName: node
  linkType: hard

"ts-error@npm:^1.0.6":
  version: 1.0.6
  resolution: "ts-error@npm:1.0.6"
  checksum: 10c0/c46994b0b88eae75d676ab18edcdb3e6c309abb39d8169c2d15286d10f4fc7bfc58c537a81f3efe24701e840247b5e79ac8e21a7335327811a07cfc33f69a72f
  languageName: node
  linkType: hard

"ts-jest@npm:^29.3.2":
  version: 29.4.0
  resolution: "ts-jest@npm:29.4.0"
  dependencies:
    bs-logger: "npm:^0.2.6"
    ejs: "npm:^3.1.10"
    fast-json-stable-stringify: "npm:^2.1.0"
    json5: "npm:^2.2.3"
    lodash.memoize: "npm:^4.1.2"
    make-error: "npm:^1.3.6"
    semver: "npm:^7.7.2"
    type-fest: "npm:^4.41.0"
    yargs-parser: "npm:^21.1.1"
  peerDependencies:
    "@babel/core": ">=7.0.0-beta.0 <8"
    "@jest/transform": ^29.0.0 || ^30.0.0
    "@jest/types": ^29.0.0 || ^30.0.0
    babel-jest: ^29.0.0 || ^30.0.0
    jest: ^29.0.0 || ^30.0.0
    jest-util: ^29.0.0 || ^30.0.0
    typescript: ">=4.3 <6"
  peerDependenciesMeta:
    "@babel/core":
      optional: true
    "@jest/transform":
      optional: true
    "@jest/types":
      optional: true
    babel-jest:
      optional: true
    esbuild:
      optional: true
    jest-util:
      optional: true
  bin:
    ts-jest: cli.js
  checksum: 10c0/c266431200786995b5bd32f8e61f17a564ce231278aace1d98fb0ae670f24013aeea06c90ec6019431e5a6f5e798868785131bef856085c931d193e2efbcea04
  languageName: node
  linkType: hard

"ts-node@npm:^10.9.2":
  version: 10.9.2
  resolution: "ts-node@npm:10.9.2"
  dependencies:
    "@cspotcode/source-map-support": "npm:^0.8.0"
    "@tsconfig/node10": "npm:^1.0.7"
    "@tsconfig/node12": "npm:^1.0.7"
    "@tsconfig/node14": "npm:^1.0.0"
    "@tsconfig/node16": "npm:^1.0.2"
    acorn: "npm:^8.4.1"
    acorn-walk: "npm:^8.1.1"
    arg: "npm:^4.1.0"
    create-require: "npm:^1.1.0"
    diff: "npm:^4.0.1"
    make-error: "npm:^1.1.1"
    v8-compile-cache-lib: "npm:^3.0.1"
    yn: "npm:3.1.1"
  peerDependencies:
    "@swc/core": ">=1.2.50"
    "@swc/wasm": ">=1.2.50"
    "@types/node": "*"
    typescript: ">=2.7"
  peerDependenciesMeta:
    "@swc/core":
      optional: true
    "@swc/wasm":
      optional: true
  bin:
    ts-node: dist/bin.js
    ts-node-cwd: dist/bin-cwd.js
    ts-node-esm: dist/bin-esm.js
    ts-node-script: dist/bin-script.js
    ts-node-transpile-only: dist/bin-transpile.js
    ts-script: dist/bin-script-deprecated.js
  checksum: 10c0/5f29938489f96982a25ba650b64218e83a3357d76f7bede80195c65ab44ad279c8357264639b7abdd5d7e75fc269a83daa0e9c62fd8637a3def67254ecc9ddc2
  languageName: node
  linkType: hard

"tsafe@npm:^1.8.5":
  version: 1.8.5
  resolution: "tsafe@npm:1.8.5"
  checksum: 10c0/5eb88061c640a11035aa8f7d9713cf9d1e9d479b417ed5f7094151d891b76160d17dd3a8718d624b95a77240e1221fd2a6b6064a25447ca9983088f92442450b
  languageName: node
  linkType: hard

"tsconfck@npm:3.1.3":
  version: 3.1.3
  resolution: "tsconfck@npm:3.1.3"
  peerDependencies:
    typescript: ^5.0.0
  peerDependenciesMeta:
    typescript:
      optional: true
  bin:
    tsconfck: bin/tsconfck.js
  checksum: 10c0/64f7a8ed0a6d36b0902dfc0075e791d2242f7634644f124343ec0dec4f3f70092f929c5a9f59496d51883aa81bb1e595deb92a219593575d2e75b849064713d1
  languageName: node
  linkType: hard

"tslib@npm:^1.11.1":
  version: 1.14.1
  resolution: "tslib@npm:1.14.1"
  checksum: 10c0/69ae09c49eea644bc5ebe1bca4fa4cc2c82b7b3e02f43b84bd891504edf66dbc6b2ec0eef31a957042de2269139e4acff911e6d186a258fb14069cd7f6febce2
  languageName: node
  linkType: hard

"tslib@npm:^2.0.0, tslib@npm:^2.1.0, tslib@npm:^2.3.1, tslib@npm:^2.4.0, tslib@npm:^2.5.0, tslib@npm:^2.6.2":
  version: 2.8.1
  resolution: "tslib@npm:2.8.1"
  checksum: 10c0/9c4759110a19c53f992d9aae23aac5ced636e99887b51b9e61def52611732872ff7668757d4e4c61f19691e36f4da981cd9485e869b4a7408d689f6bf1f14e62
  languageName: node
  linkType: hard

"tsx@npm:^4.19.3":
  version: 4.20.3
  resolution: "tsx@npm:4.20.3"
  dependencies:
    esbuild: "npm:~0.25.0"
    fsevents: "npm:~2.3.3"
    get-tsconfig: "npm:^4.7.5"
  dependenciesMeta:
    fsevents:
      optional: true
  bin:
    tsx: dist/cli.mjs
  checksum: 10c0/6ff0d91ed046ec743fac7ed60a07f3c025e5b71a5aaf58f3d2a6b45e4db114c83e59ebbb078c8e079e48d3730b944a02bc0de87695088aef4ec8bbc705dc791b
  languageName: node
  linkType: hard

"type-detect@npm:4.0.8":
  version: 4.0.8
  resolution: "type-detect@npm:4.0.8"
  checksum: 10c0/8fb9a51d3f365a7de84ab7f73b653534b61b622aa6800aecdb0f1095a4a646d3f5eb295322127b6573db7982afcd40ab492d038cf825a42093a58b1e1353e0bd
  languageName: node
  linkType: hard

"type-fest@npm:^0.21.3":
  version: 0.21.3
  resolution: "type-fest@npm:0.21.3"
  checksum: 10c0/902bd57bfa30d51d4779b641c2bc403cdf1371fb9c91d3c058b0133694fcfdb817aef07a47f40faf79039eecbaa39ee9d3c532deff244f3a19ce68cea71a61e8
  languageName: node
  linkType: hard

"type-fest@npm:^1.0.2":
  version: 1.4.0
  resolution: "type-fest@npm:1.4.0"
  checksum: 10c0/a3c0f4ee28ff6ddf800d769eafafcdeab32efa38763c1a1b8daeae681920f6e345d7920bf277245235561d8117dab765cb5f829c76b713b4c9de0998a5397141
  languageName: node
  linkType: hard

"type-fest@npm:^4.41.0":
  version: 4.41.0
  resolution: "type-fest@npm:4.41.0"
  checksum: 10c0/f5ca697797ed5e88d33ac8f1fec21921839871f808dc59345c9cf67345bfb958ce41bd821165dbf3ae591cedec2bf6fe8882098dfdd8dc54320b859711a2c1e4
  languageName: node
  linkType: hard

"type-is@npm:^1.6.18, type-is@npm:~1.6.18":
  version: 1.6.18
  resolution: "type-is@npm:1.6.18"
  dependencies:
    media-typer: "npm:0.3.0"
    mime-types: "npm:~2.1.24"
  checksum: 10c0/a23daeb538591b7efbd61ecf06b6feb2501b683ffdc9a19c74ef5baba362b4347e42f1b4ed81f5882a8c96a3bfff7f93ce3ffaf0cbbc879b532b04c97a55db9d
  languageName: node
  linkType: hard

"type-is@npm:^2.0.0, type-is@npm:^2.0.1":
  version: 2.0.1
  resolution: "type-is@npm:2.0.1"
  dependencies:
    content-type: "npm:^1.0.5"
    media-typer: "npm:^1.1.0"
    mime-types: "npm:^3.0.0"
  checksum: 10c0/7f7ec0a060b16880bdad36824ab37c26019454b67d73e8a465ed5a3587440fbe158bc765f0da68344498235c877e7dbbb1600beccc94628ed05599d667951b99
  languageName: node
  linkType: hard

"typedarray@npm:^0.0.6":
  version: 0.0.6
  resolution: "typedarray@npm:0.0.6"
  checksum: 10c0/6005cb31df50eef8b1f3c780eb71a17925f3038a100d82f9406ac2ad1de5eb59f8e6decbdc145b3a1f8e5836e17b0c0002fb698b9fe2516b8f9f9ff602d36412
  languageName: node
  linkType: hard

"typescript-paths@npm:^1.5.1":
  version: 1.5.1
  resolution: "typescript-paths@npm:1.5.1"
  peerDependencies:
    typescript: ^4.7.2 || ^5
  checksum: 10c0/68b7cdf0c8a11e4555e172ccec85fb342d1e19029cce761979087b3509a1b320ccbce4be666cea7acac03ecdce23cc69b2afb8a4847f1bfc25bb49201fdbe5ad
  languageName: node
  linkType: hard

"typescript@npm:^5.8.3":
  version: 5.8.3
  resolution: "typescript@npm:5.8.3"
  bin:
    tsc: bin/tsc
    tsserver: bin/tsserver
  checksum: 10c0/5f8bb01196e542e64d44db3d16ee0e4063ce4f3e3966df6005f2588e86d91c03e1fb131c2581baf0fb65ee79669eea6e161cd448178986587e9f6844446dbb48
  languageName: node
  linkType: hard

"typescript@patch:typescript@npm%3A^5.8.3#optional!builtin<compat/typescript>":
  version: 5.8.3
  resolution: "typescript@patch:typescript@npm%3A5.8.3#optional!builtin<compat/typescript>::version=5.8.3&hash=5786d5"
  bin:
    tsc: bin/tsc
    tsserver: bin/tsserver
  checksum: 10c0/39117e346ff8ebd87ae1510b3a77d5d92dae5a89bde588c747d25da5c146603a99c8ee588c7ef80faaf123d89ed46f6dbd918d534d641083177d5fac38b8a1cb
  languageName: node
  linkType: hard

"ufo@npm:^1.5.4":
  version: 1.6.1
  resolution: "ufo@npm:1.6.1"
  checksum: 10c0/5a9f041e5945fba7c189d5410508cbcbefef80b253ed29aa2e1f8a2b86f4bd51af44ee18d4485e6d3468c92be9bf4a42e3a2b72dcaf27ce39ce947ec994f1e6b
  languageName: node
  linkType: hard

"ulid@npm:^2.3.0":
  version: 2.4.0
  resolution: "ulid@npm:2.4.0"
  bin:
    ulid: bin/cli.js
  checksum: 10c0/96f7597a2f09dadd380707a0755753d85717059deae54a9e28b6cbc34c02ef211dd1d1dcbfa8bd557d12309f174b87f3ba5f45d6b67573d1a2da202b5a0c9319
  languageName: node
  linkType: hard

"uncrypto@npm:^0.1.3":
  version: 0.1.3
  resolution: "uncrypto@npm:0.1.3"
  checksum: 10c0/74a29afefd76d5b77bedc983559ceb33f5bbc8dada84ff33755d1e3355da55a4e03a10e7ce717918c436b4dfafde1782e799ebaf2aadd775612b49f7b5b2998e
  languageName: node
  linkType: hard

"undefsafe@npm:^2.0.5":
  version: 2.0.5
  resolution: "undefsafe@npm:2.0.5"
  checksum: 10c0/96c0466a5fbf395917974a921d5d4eee67bca4b30d3a31ce7e621e0228c479cf893e783a109af6e14329b52fe2f0cb4108665fad2b87b0018c0df6ac771261d5
  languageName: node
  linkType: hard

"undici-types@npm:~6.21.0":
  version: 6.21.0
  resolution: "undici-types@npm:6.21.0"
  checksum: 10c0/c01ed51829b10aa72fc3ce64b747f8e74ae9b60eafa19a7b46ef624403508a54c526ffab06a14a26b3120d055e1104d7abe7c9017e83ced038ea5cf52f8d5e04
  languageName: node
  linkType: hard

"undici-types@npm:~7.8.0":
  version: 7.8.0
  resolution: "undici-types@npm:7.8.0"
  checksum: 10c0/9d9d246d1dc32f318d46116efe3cfca5a72d4f16828febc1918d94e58f6ffcf39c158aa28bf5b4fc52f410446bc7858f35151367bd7a49f21746cab6497b709b
  languageName: node
  linkType: hard

"unicorn-magic@npm:^0.3.0":
  version: 0.3.0
  resolution: "unicorn-magic@npm:0.3.0"
  checksum: 10c0/0a32a997d6c15f1c2a077a15b1c4ca6f268d574cf5b8975e778bb98e6f8db4ef4e86dfcae4e158cd4c7e38fb4dd383b93b13eefddc7f178dea13d3ac8a603271
  languageName: node
  linkType: hard

"unique-filename@npm:^4.0.0":
  version: 4.0.0
  resolution: "unique-filename@npm:4.0.0"
  dependencies:
    unique-slug: "npm:^5.0.0"
  checksum: 10c0/38ae681cceb1408ea0587b6b01e29b00eee3c84baee1e41fd5c16b9ed443b80fba90c40e0ba69627e30855570a34ba8b06702d4a35035d4b5e198bf5a64c9ddc
  languageName: node
  linkType: hard

"unique-slug@npm:^5.0.0":
  version: 5.0.0
  resolution: "unique-slug@npm:5.0.0"
  dependencies:
    imurmurhash: "npm:^0.1.4"
  checksum: 10c0/d324c5a44887bd7e105ce800fcf7533d43f29c48757ac410afd42975de82cc38ea2035c0483f4de82d186691bf3208ef35c644f73aa2b1b20b8e651be5afd293
  languageName: node
  linkType: hard

"unist-util-is@npm:^6.0.0":
  version: 6.0.0
  resolution: "unist-util-is@npm:6.0.0"
  dependencies:
    "@types/unist": "npm:^3.0.0"
  checksum: 10c0/9419352181eaa1da35eca9490634a6df70d2217815bb5938a04af3a662c12c5607a2f1014197ec9c426fbef18834f6371bfdb6f033040fa8aa3e965300d70e7e
  languageName: node
  linkType: hard

"unist-util-position@npm:^5.0.0":
  version: 5.0.0
  resolution: "unist-util-position@npm:5.0.0"
  dependencies:
    "@types/unist": "npm:^3.0.0"
  checksum: 10c0/dde3b31e314c98f12b4dc6402f9722b2bf35e96a4f2d463233dd90d7cde2d4928074a7a11eff0a5eb1f4e200f27fc1557e0a64a7e8e4da6558542f251b1b7400
  languageName: node
  linkType: hard

"unist-util-stringify-position@npm:^4.0.0":
  version: 4.0.0
  resolution: "unist-util-stringify-position@npm:4.0.0"
  dependencies:
    "@types/unist": "npm:^3.0.0"
  checksum: 10c0/dfe1dbe79ba31f589108cb35e523f14029b6675d741a79dea7e5f3d098785045d556d5650ec6a8338af11e9e78d2a30df12b1ee86529cded1098da3f17ee999e
  languageName: node
  linkType: hard

"unist-util-visit-parents@npm:^6.0.0":
  version: 6.0.1
  resolution: "unist-util-visit-parents@npm:6.0.1"
  dependencies:
    "@types/unist": "npm:^3.0.0"
    unist-util-is: "npm:^6.0.0"
  checksum: 10c0/51b1a5b0aa23c97d3e03e7288f0cdf136974df2217d0999d3de573c05001ef04cccd246f51d2ebdfb9e8b0ed2704451ad90ba85ae3f3177cf9772cef67f56206
  languageName: node
  linkType: hard

"unist-util-visit@npm:^5.0.0":
  version: 5.0.0
  resolution: "unist-util-visit@npm:5.0.0"
  dependencies:
    "@types/unist": "npm:^3.0.0"
    unist-util-is: "npm:^6.0.0"
    unist-util-visit-parents: "npm:^6.0.0"
  checksum: 10c0/51434a1d80252c1540cce6271a90fd1a106dbe624997c09ed8879279667fb0b2d3a685e02e92bf66598dcbe6cdffa7a5f5fb363af8fdf90dda6c855449ae39a5
  languageName: node
  linkType: hard

"universalify@npm:^2.0.0":
  version: 2.0.1
  resolution: "universalify@npm:2.0.1"
  checksum: 10c0/73e8ee3809041ca8b818efb141801a1004e3fc0002727f1531f4de613ea281b494a40909596dae4a042a4fb6cd385af5d4db2e137b1362e0e91384b828effd3a
  languageName: node
  linkType: hard

"unpipe@npm:1.0.0, unpipe@npm:~1.0.0":
  version: 1.0.0
  resolution: "unpipe@npm:1.0.0"
  checksum: 10c0/193400255bd48968e5c5383730344fbb4fa114cdedfab26e329e50dd2d81b134244bb8a72c6ac1b10ab0281a58b363d06405632c9d49ca9dfd5e90cbd7d0f32c
  languageName: node
  linkType: hard

"unplugin-utils@npm:^0.2.4":
  version: 0.2.4
  resolution: "unplugin-utils@npm:0.2.4"
  dependencies:
    pathe: "npm:^2.0.2"
    picomatch: "npm:^4.0.2"
  checksum: 10c0/b5ab2db37823f5b4c8ee8719caa4b5a50b2da33c74c8110d46deb7a2399dfa15cbcaa0cff62aa6400c76e778e42becd9195c09b6502c0c007d03610f432c875f
  languageName: node
  linkType: hard

"untruncate-json@npm:^0.0.1":
  version: 0.0.1
  resolution: "untruncate-json@npm:0.0.1"
  checksum: 10c0/9fbd68098fcbee72c7178a36de624732dea67527117859540c546de9c03403ecb28a41f82bc8097f9f66f40485c749e5170d935e580648f0b7f9412641ad899c
  languageName: node
  linkType: hard

"update-browserslist-db@npm:^1.1.3":
  version: 1.1.3
  resolution: "update-browserslist-db@npm:1.1.3"
  dependencies:
    escalade: "npm:^3.2.0"
    picocolors: "npm:^1.1.1"
  peerDependencies:
    browserslist: ">= 4.21.0"
  bin:
    update-browserslist-db: cli.js
  checksum: 10c0/682e8ecbf9de474a626f6462aa85927936cdd256fe584c6df2508b0df9f7362c44c957e9970df55dfe44d3623807d26316ea2c7d26b80bb76a16c56c37233c32
  languageName: node
  linkType: hard

"uri-js@npm:^4.2.2":
  version: 4.4.1
  resolution: "uri-js@npm:4.4.1"
  dependencies:
    punycode: "npm:^2.1.0"
  checksum: 10c0/4ef57b45aa820d7ac6496e9208559986c665e49447cb072744c13b66925a362d96dd5a46c4530a6b8e203e5db5fe849369444440cb22ecfc26c679359e5dfa3c
  languageName: node
  linkType: hard

"url-join@npm:4.0.1":
  version: 4.0.1
  resolution: "url-join@npm:4.0.1"
  checksum: 10c0/ac65e2c7c562d7b49b68edddcf55385d3e922bc1dd5d90419ea40b53b6de1607d1e45ceb71efb9d60da02c681d13c6cb3a1aa8b13fc0c989dfc219df97ee992d
  languageName: node
  linkType: hard

"use-sync-external-store@npm:^1.4.0":
  version: 1.5.0
  resolution: "use-sync-external-store@npm:1.5.0"
  peerDependencies:
    react: ^16.8.0 || ^17.0.0 || ^18.0.0 || ^19.0.0
  checksum: 10c0/1b8663515c0be34fa653feb724fdcce3984037c78dd4a18f68b2c8be55cc1a1084c578d5b75f158d41b5ddffc2bf5600766d1af3c19c8e329bb20af2ec6f52f4
  languageName: node
  linkType: hard

"util-deprecate@npm:^1.0.1":
  version: 1.0.2
  resolution: "util-deprecate@npm:1.0.2"
  checksum: 10c0/41a5bdd214df2f6c3ecf8622745e4a366c4adced864bc3c833739791aeeeb1838119af7daed4ba36428114b5c67dcda034a79c882e97e43c03e66a4dd7389942
  languageName: node
  linkType: hard

"utils-merge@npm:1.0.1":
  version: 1.0.1
  resolution: "utils-merge@npm:1.0.1"
  checksum: 10c0/02ba649de1b7ca8854bfe20a82f1dfbdda3fb57a22ab4a8972a63a34553cf7aa51bc9081cf7e001b035b88186d23689d69e71b510e610a09a4c66f68aa95b672
  languageName: node
  linkType: hard

"uuid@npm:^11.1.0":
  version: 11.1.0
  resolution: "uuid@npm:11.1.0"
  bin:
    uuid: dist/esm/bin/uuid
  checksum: 10c0/34aa51b9874ae398c2b799c88a127701408cd581ee89ec3baa53509dd8728cbb25826f2a038f9465f8b7be446f0fbf11558862965b18d21c993684297628d4d3
  languageName: node
  linkType: hard

"uuid@npm:^9.0.0, uuid@npm:^9.0.1":
  version: 9.0.1
  resolution: "uuid@npm:9.0.1"
  bin:
    uuid: dist/bin/uuid
  checksum: 10c0/1607dd32ac7fc22f2d8f77051e6a64845c9bce5cd3dd8aa0070c074ec73e666a1f63c7b4e0f4bf2bc8b9d59dc85a15e17807446d9d2b17c8485fbc2147b27f9b
  languageName: node
  linkType: hard

"v8-compile-cache-lib@npm:^3.0.1":
  version: 3.0.1
  resolution: "v8-compile-cache-lib@npm:3.0.1"
  checksum: 10c0/bdc36fb8095d3b41df197f5fb6f11e3a26adf4059df3213e3baa93810d8f0cc76f9a74aaefc18b73e91fe7e19154ed6f134eda6fded2e0f1c8d2272ed2d2d391
  languageName: node
  linkType: hard

"v8-to-istanbul@npm:^9.0.1":
  version: 9.3.0
  resolution: "v8-to-istanbul@npm:9.3.0"
  dependencies:
    "@jridgewell/trace-mapping": "npm:^0.3.12"
    "@types/istanbul-lib-coverage": "npm:^2.0.1"
    convert-source-map: "npm:^2.0.0"
  checksum: 10c0/968bcf1c7c88c04df1ffb463c179558a2ec17aa49e49376120504958239d9e9dad5281aa05f2a78542b8557f2be0b0b4c325710262f3b838b40d703d5ed30c23
  languageName: node
  linkType: hard

"vary@npm:^1, vary@npm:^1.1.2, vary@npm:~1.1.2":
  version: 1.1.2
  resolution: "vary@npm:1.1.2"
  checksum: 10c0/f15d588d79f3675135ba783c91a4083dcd290a2a5be9fcb6514220a1634e23df116847b1cc51f66bfb0644cf9353b2abb7815ae499bab06e46dd33c1a6bf1f4f
  languageName: node
  linkType: hard

"vfile-message@npm:^4.0.0":
  version: 4.0.2
  resolution: "vfile-message@npm:4.0.2"
  dependencies:
    "@types/unist": "npm:^3.0.0"
    unist-util-stringify-position: "npm:^4.0.0"
  checksum: 10c0/07671d239a075f888b78f318bc1d54de02799db4e9dce322474e67c35d75ac4a5ac0aaf37b18801d91c9f8152974ea39678aa72d7198758b07f3ba04fb7d7514
  languageName: node
  linkType: hard

"vfile@npm:^6.0.0":
  version: 6.0.3
  resolution: "vfile@npm:6.0.3"
  dependencies:
    "@types/unist": "npm:^3.0.0"
    vfile-message: "npm:^4.0.0"
  checksum: 10c0/e5d9eb4810623f23758cfc2205323e33552fb5972e5c2e6587babe08fe4d24859866277404fb9e2a20afb71013860d96ec806cb257536ae463c87d70022ab9ef
  languageName: node
  linkType: hard

"walker@npm:^1.0.8":
  version: 1.0.8
  resolution: "walker@npm:1.0.8"
  dependencies:
    makeerror: "npm:1.0.12"
  checksum: 10c0/a17e037bccd3ca8a25a80cb850903facdfed0de4864bd8728f1782370715d679fa72e0a0f5da7c1c1379365159901e5935f35be531229da53bbfc0efdabdb48e
  languageName: node
  linkType: hard

"weaviate-client@npm:^3.5.2":
  version: 3.6.2
  resolution: "weaviate-client@npm:3.6.2"
  dependencies:
    abort-controller-x: "npm:^0.4.3"
    graphql: "npm:^16.10.0"
    graphql-request: "npm:^6.1.0"
    long: "npm:^5.2.4"
    nice-grpc: "npm:^2.1.11"
    nice-grpc-client-middleware-retry: "npm:^3.1.10"
    nice-grpc-common: "npm:^2.0.2"
    uuid: "npm:^9.0.1"
  checksum: 10c0/8a4a0ef2b7a5999116916fa4ac9ea432ca902999389f3fe37bc6ee54ac1d9e76d5444887521fca3f8241bd147390e6c7b0c4c23c8474faf413dbb532ee5411b8
  languageName: node
  linkType: hard

"webidl-conversions@npm:^3.0.0":
  version: 3.0.1
  resolution: "webidl-conversions@npm:3.0.1"
  checksum: 10c0/5612d5f3e54760a797052eb4927f0ddc01383550f542ccd33d5238cfd65aeed392a45ad38364970d0a0f4fea32e1f4d231b3d8dac4a3bdd385e5cf802ae097db
  languageName: node
  linkType: hard

"whatwg-url@npm:^5.0.0":
  version: 5.0.0
  resolution: "whatwg-url@npm:5.0.0"
  dependencies:
    tr46: "npm:~0.0.3"
    webidl-conversions: "npm:^3.0.0"
  checksum: 10c0/1588bed84d10b72d5eec1d0faa0722ba1962f1821e7539c535558fb5398d223b0c50d8acab950b8c488b4ba69043fd833cc2697056b167d8ad46fac3995a55d5
  languageName: node
  linkType: hard

"which@npm:^2.0.1":
  version: 2.0.2
  resolution: "which@npm:2.0.2"
  dependencies:
    isexe: "npm:^2.0.0"
  bin:
    node-which: ./bin/node-which
  checksum: 10c0/66522872a768b60c2a65a57e8ad184e5372f5b6a9ca6d5f033d4b0dc98aff63995655a7503b9c0a2598936f532120e81dd8cc155e2e92ed662a2b9377cc4374f
  languageName: node
  linkType: hard

"which@npm:^5.0.0":
  version: 5.0.0
  resolution: "which@npm:5.0.0"
  dependencies:
    isexe: "npm:^3.1.1"
  bin:
    node-which: bin/which.js
  checksum: 10c0/e556e4cd8b7dbf5df52408c9a9dd5ac6518c8c5267c8953f5b0564073c66ed5bf9503b14d876d0e9c7844d4db9725fb0dcf45d6e911e17e26ab363dc3965ae7b
  languageName: node
  linkType: hard

"winston@npm:2.x":
  version: 2.4.7
  resolution: "winston@npm:2.4.7"
  dependencies:
    async: "npm:^2.6.4"
    colors: "npm:1.0.x"
    cycle: "npm:1.0.x"
    eyes: "npm:0.1.x"
    isstream: "npm:0.1.x"
    stack-trace: "npm:0.0.x"
  checksum: 10c0/8c6f7365955d93a78f3345db9259052fd68c64096898c5787cdd766a26555d869e56c6607db29c85733d342fe86b8e8b65862843cb751391e594752b1565a89b
  languageName: node
  linkType: hard

"wrap-ansi-cjs@npm:wrap-ansi@^7.0.0, wrap-ansi@npm:^7.0.0":
  version: 7.0.0
  resolution: "wrap-ansi@npm:7.0.0"
  dependencies:
    ansi-styles: "npm:^4.0.0"
    string-width: "npm:^4.1.0"
    strip-ansi: "npm:^6.0.0"
  checksum: 10c0/d15fc12c11e4cbc4044a552129ebc75ee3f57aa9c1958373a4db0292d72282f54373b536103987a4a7594db1ef6a4f10acf92978f79b98c49306a4b58c77d4da
  languageName: node
  linkType: hard

"wrap-ansi@npm:^8.1.0":
  version: 8.1.0
  resolution: "wrap-ansi@npm:8.1.0"
  dependencies:
    ansi-styles: "npm:^6.1.0"
    string-width: "npm:^5.0.1"
    strip-ansi: "npm:^7.0.1"
  checksum: 10c0/138ff58a41d2f877eae87e3282c0630fc2789012fc1af4d6bd626eeb9a2f9a65ca92005e6e69a75c7b85a68479fe7443c7dbe1eb8fbaa681a4491364b7c55c60
  languageName: node
  linkType: hard

"wrappy@npm:1":
  version: 1.0.2
  resolution: "wrappy@npm:1.0.2"
  checksum: 10c0/56fece1a4018c6a6c8e28fbc88c87e0fbf4ea8fd64fc6c63b18f4acc4bd13e0ad2515189786dd2c30d3eec9663d70f4ecf699330002f8ccb547e4a18231fc9f0
  languageName: node
  linkType: hard

"write-file-atomic@npm:^4.0.2":
  version: 4.0.2
  resolution: "write-file-atomic@npm:4.0.2"
  dependencies:
    imurmurhash: "npm:^0.1.4"
    signal-exit: "npm:^3.0.7"
  checksum: 10c0/a2c282c95ef5d8e1c27b335ae897b5eca00e85590d92a3fd69a437919b7b93ff36a69ea04145da55829d2164e724bc62202cdb5f4b208b425aba0807889375c7
  languageName: node
  linkType: hard

"ws@npm:^8.11.0, ws@npm:^8.18.2":
  version: 8.18.2
  resolution: "ws@npm:8.18.2"
  peerDependencies:
    bufferutil: ^4.0.1
    utf-8-validate: ">=5.0.2"
  peerDependenciesMeta:
    bufferutil:
      optional: true
    utf-8-validate:
      optional: true
  checksum: 10c0/4b50f67931b8c6943c893f59c524f0e4905bbd183016cfb0f2b8653aa7f28dad4e456b9d99d285bbb67cca4fedd9ce90dfdfaa82b898a11414ebd66ee99141e4
  languageName: node
  linkType: hard

"ws@npm:~8.17.1":
  version: 8.17.1
  resolution: "ws@npm:8.17.1"
  peerDependencies:
    bufferutil: ^4.0.1
    utf-8-validate: ">=5.0.2"
  peerDependenciesMeta:
    bufferutil:
      optional: true
    utf-8-validate:
      optional: true
  checksum: 10c0/f4a49064afae4500be772abdc2211c8518f39e1c959640457dcee15d4488628620625c783902a52af2dd02f68558da2868fd06e6fd0e67ebcd09e6881b1b5bfe
  languageName: node
  linkType: hard

"xmlhttprequest-ssl@npm:~2.0.0":
  version: 2.0.0
  resolution: "xmlhttprequest-ssl@npm:2.0.0"
  checksum: 10c0/b64ab371459bd5e3a4827e3c7535759047d285fd310aea6fd028973d547133f3be0d473c1fdae9f14d89bf509267759198ae1fbe89802079a7e217ddd990d734
  languageName: node
  linkType: hard

"xstate@npm:^5.19.4":
  version: 5.20.0
  resolution: "xstate@npm:5.20.0"
  checksum: 10c0/4443c87ce86901e71881842845785acb26eb5d7579c3a95966db83e6499f574325efbfd593e3051a21dbf5b101ba9474d284a43335ea56f518c666f0361bfc89
  languageName: node
  linkType: hard

"xtend@npm:^4.0.0, xtend@npm:^4.0.2":
  version: 4.0.2
  resolution: "xtend@npm:4.0.2"
  checksum: 10c0/366ae4783eec6100f8a02dff02ac907bf29f9a00b82ac0264b4d8b832ead18306797e283cf19de776538babfdcb2101375ec5646b59f08c52128ac4ab812ed0e
  languageName: node
  linkType: hard

"xxhash-wasm@npm:^1.1.0":
  version: 1.1.0
  resolution: "xxhash-wasm@npm:1.1.0"
  checksum: 10c0/35aa152fc7d775ae13364fe4fb20ebd89c6ac1f56cdb6060a6d2f1ed68d15180694467e63a4adb3d11936a4798ccd75a540979070e70d9b911e9981bbdd9cea6
  languageName: node
  linkType: hard

"y18n@npm:^5.0.5":
  version: 5.0.8
  resolution: "y18n@npm:5.0.8"
  checksum: 10c0/4df2842c36e468590c3691c894bc9cdbac41f520566e76e24f59401ba7d8b4811eb1e34524d57e54bc6d864bcb66baab7ffd9ca42bf1eda596618f9162b91249
  languageName: node
  linkType: hard

"yallist@npm:4.0.0, yallist@npm:^4.0.0":
  version: 4.0.0
  resolution: "yallist@npm:4.0.0"
  checksum: 10c0/2286b5e8dbfe22204ab66e2ef5cc9bbb1e55dfc873bbe0d568aa943eb255d131890dfd5bf243637273d31119b870f49c18fcde2c6ffbb7a7a092b870dc90625a
  languageName: node
  linkType: hard

"yallist@npm:^3.0.2":
  version: 3.1.1
  resolution: "yallist@npm:3.1.1"
  checksum: 10c0/c66a5c46bc89af1625476f7f0f2ec3653c1a1791d2f9407cfb4c2ba812a1e1c9941416d71ba9719876530e3340a99925f697142989371b72d93b9ee628afd8c1
  languageName: node
  linkType: hard

"yallist@npm:^5.0.0":
  version: 5.0.0
  resolution: "yallist@npm:5.0.0"
  checksum: 10c0/a499c81ce6d4a1d260d4ea0f6d49ab4da09681e32c3f0472dee16667ed69d01dae63a3b81745a24bd78476ec4fcf856114cb4896ace738e01da34b2c42235416
  languageName: node
  linkType: hard

"yaml@npm:^2.3.4":
  version: 2.8.0
  resolution: "yaml@npm:2.8.0"
  bin:
    yaml: bin.mjs
  checksum: 10c0/f6f7310cf7264a8107e72c1376f4de37389945d2fb4656f8060eca83f01d2d703f9d1b925dd8f39852a57034fafefde6225409ddd9f22aebfda16c6141b71858
  languageName: node
  linkType: hard

"yargs-parser@npm:^20.2.7":
  version: 20.2.9
  resolution: "yargs-parser@npm:20.2.9"
  checksum: 10c0/0685a8e58bbfb57fab6aefe03c6da904a59769bd803a722bb098bd5b0f29d274a1357762c7258fb487512811b8063fb5d2824a3415a0a4540598335b3b086c72
  languageName: node
  linkType: hard

"yargs-parser@npm:^21.1.1":
  version: 21.1.1
  resolution: "yargs-parser@npm:21.1.1"
  checksum: 10c0/f84b5e48169479d2f402239c59f084cfd1c3acc197a05c59b98bab067452e6b3ea46d4dd8ba2985ba7b3d32a343d77df0debd6b343e5dae3da2aab2cdf5886b2
  languageName: node
  linkType: hard

"yargs@npm:^17.3.1, yargs@npm:^17.7.2":
  version: 17.7.2
  resolution: "yargs@npm:17.7.2"
  dependencies:
    cliui: "npm:^8.0.1"
    escalade: "npm:^3.1.1"
    get-caller-file: "npm:^2.0.5"
    require-directory: "npm:^2.1.1"
    string-width: "npm:^4.2.3"
    y18n: "npm:^5.0.5"
    yargs-parser: "npm:^21.1.1"
  checksum: 10c0/ccd7e723e61ad5965fffbb791366db689572b80cca80e0f96aad968dfff4156cd7cd1ad18607afe1046d8241e6fb2d6c08bf7fa7bfb5eaec818735d8feac8f05
  languageName: node
  linkType: hard

"yn@npm:3.1.1":
  version: 3.1.1
  resolution: "yn@npm:3.1.1"
  checksum: 10c0/0732468dd7622ed8a274f640f191f3eaf1f39d5349a1b72836df484998d7d9807fbea094e2f5486d6b0cd2414aad5775972df0e68f8604db89a239f0f4bf7443
  languageName: node
  linkType: hard

"yocto-queue@npm:^0.1.0":
  version: 0.1.0
  resolution: "yocto-queue@npm:0.1.0"
  checksum: 10c0/dceb44c28578b31641e13695d200d34ec4ab3966a5729814d5445b194933c096b7ced71494ce53a0e8820685d1d010df8b2422e5bf2cdea7e469d97ffbea306f
  languageName: node
  linkType: hard

"yocto-spinner@npm:^0.2.3":
  version: 0.2.3
  resolution: "yocto-spinner@npm:0.2.3"
  dependencies:
    yoctocolors: "npm:^2.1.1"
  checksum: 10c0/4c4527f68161334291355eae0ab9a8e1b988bd854eebd93697d9a88b008362d71ad9f24334a79e48aca6ba1c085e365cd2981ba5ddc0ea54cc3efd96f2d08714
  languageName: node
  linkType: hard

"yoctocolors@npm:^2.1.1":
  version: 2.1.1
  resolution: "yoctocolors@npm:2.1.1"
  checksum: 10c0/85903f7fa96f1c70badee94789fade709f9d83dab2ec92753d612d84fcea6d34c772337a9f8914c6bed2f5fc03a428ac5d893e76fab636da5f1236ab725486d0
  languageName: node
  linkType: hard

"zod-error@npm:1.5.0":
  version: 1.5.0
  resolution: "zod-error@npm:1.5.0"
  dependencies:
    zod: "npm:^3.20.2"
  checksum: 10c0/f7809d1e3a2f2ba4e956631c1a8ebb79bbfdbb7b1009fb6cc4f2fe0292fcc966e985df154811c262f86af61e4605434ebcc9c834c0a1a368804c351c1e68b646
  languageName: node
  linkType: hard

"zod-from-json-schema@npm:^0.0.5":
  version: 0.0.5
  resolution: "zod-from-json-schema@npm:0.0.5"
  dependencies:
    zod: "npm:^3.24.2"
  checksum: 10c0/ca7f3240361d48e3e5eac35ca2adc194b73ffe6ef35215e43fb8da674371efc2d40ee986279ebb7ed4a0d4e044b1219b6f51594f9073e7a528323f79123f56fe
  languageName: node
  linkType: hard

"zod-to-json-schema@npm:^3.24.1, zod-to-json-schema@npm:^3.24.5":
  version: 3.24.5
  resolution: "zod-to-json-schema@npm:3.24.5"
  peerDependencies:
    zod: ^3.24.1
  checksum: 10c0/0745b94ba53e652d39f262641cdeb2f75d24339fb6076a38ce55bcf53d82dfaea63adf524ebc5f658681005401687f8e9551c4feca7c4c882e123e66091dfb90
  languageName: node
  linkType: hard

"zod-validation-error@npm:^1.5.0":
  version: 1.5.0
  resolution: "zod-validation-error@npm:1.5.0"
  peerDependencies:
    zod: ^3.18.0
  checksum: 10c0/b05d74900fa840e35abb66e0b0f90bd0175bcf8bf0bf9cea7de1383c9a35b75f870951a529cfc2045f2629f00b9ce1b30745b0e4689fd198743d6da91b321a58
  languageName: node
  linkType: hard

"zod@npm:3.23.8":
  version: 3.23.8
  resolution: "zod@npm:3.23.8"
  checksum: 10c0/8f14c87d6b1b53c944c25ce7a28616896319d95bc46a9660fe441adc0ed0a81253b02b5abdaeffedbeb23bdd25a0bf1c29d2c12dd919aef6447652dd295e3e69
  languageName: node
  linkType: hard

"zod@npm:^3.20.2, zod@npm:^3.22.4, zod@npm:^3.23.8, zod@npm:^3.24.2, zod@npm:^3.24.3, zod@npm:^3.25.57, zod@npm:^3.25.67":
  version: 3.25.67
  resolution: "zod@npm:3.25.67"
  checksum: 10c0/80a0cab3033272c4ab9312198081f0c4ea88e9673c059aa36dc32024906363729db54bdb78f3dc9d5529bd1601f74974d5a56c0a23e40c6f04a9270c9ff22336
  languageName: node
  linkType: hard

"zwitch@npm:^2.0.4":
  version: 2.0.4
  resolution: "zwitch@npm:2.0.4"
  checksum: 10c0/3c7830cdd3378667e058ffdb4cf2bb78ac5711214e2725900873accb23f3dfe5f9e7e5a06dcdc5f29605da976fc45c26d9a13ca334d6eea2245a15e77b8fc06e
  languageName: node
  linkType: hard
